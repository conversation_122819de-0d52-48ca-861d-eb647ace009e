﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Fix_Asignacion_Ticket_Compensable_Nulo : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Tickets_Compensables""
                            (""Id_Ticket"", ""Suma_Agrupacion_Toneladas_Por_Compensar"", ""Nro_Ticket_Compensacion"",
                             ""Maximo_Toneladas_Compensables"") as
                WITH RECURSIVE
                tickets_to_compensate AS (
                    SELECT
                        td.""Id_Ticket"",
                        td.""Fecha_Hora_Pesaje""::date AS ""Fecha_Pesaje"",
                        sum(td.""Toneladas_Resultantes"") AS ""Suma_Toneladas_Agrupadas"",
                        EXTRACT(year FROM td.""Fecha_Hora_Pesaje""::date)::INT AS year,
                        EXTRACT(month FROM td.""Fecha_Hora_Pesaje""::date)::INT AS month,
                        ROW_NUMBER() OVER (
                            PARTITION BY EXTRACT(year FROM td.""Fecha_Hora_Pesaje""::date), EXTRACT(month FROM td.""Fecha_Hora_Pesaje""::date)
                            ORDER BY td.""Fecha_Hora_Pesaje""::date, td.""Id_Ticket""
                        ) AS demand_seq
                    FROM ""REPEM_Detalle_Compensacion_Tickets"" td
                    WHERE td.""Tipo_Compensacion"" IN ('PARCIAL', 'TOTAL')
                    GROUP BY td.""Fecha_Hora_Pesaje""::date, td.""Id_Ticket""
                ),
                compensation_posibilities AS (
                    SELECT
                        rpm2.""REPEM04_Id"",
                        rpm2.""REPEM04_FechaHora_Pesaje""::date AS ""RPM_Fecha_Pesaje"",
                        rpm2.""REPEM04_PesoTotal_Toneladas"",
                        EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"")::INT AS year,
                        EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"")::INT AS month,
                        ROW_NUMBER() OVER (PARTITION BY EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje""),
                                                         EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"")
                                              ORDER BY rpm2.""REPEM04_PesoTotal_Toneladas"" DESC) AS supply_seq
                    FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm2
                    WHERE rpm2.""REPEM04_RutaCodigo""::text = ANY (ARRAY ['615618','618219','618119','618319'])
                    AND rpm2.""REPEM04_PesoTotal_Toneladas"" > 0
                ),
                ticket_assignment (
                    year, month,
                    assigned_demand_seq,
                    assigned_supply_seq,
                    current_supply_id,
                    current_supply_max_tons,
                    remaining_capacity
                ) AS (
                    SELECT
                        d.year, d.month,
                        d.demand_seq,
                        s.supply_seq,
                        s.""REPEM04_Id"",
                        s.""REPEM04_PesoTotal_Toneladas"",
                        s.""REPEM04_PesoTotal_Toneladas"" - d.""Suma_Toneladas_Agrupadas"" AS remaining_capacity
                    FROM tickets_to_compensate d
                    JOIN compensation_posibilities s ON d.year = s.year AND d.month = s.month
                    WHERE d.demand_seq = 1 AND s.supply_seq = 1
                    UNION ALL
                    SELECT
                        prev.year, prev.month,
                        curr_d.demand_seq,
                        CASE
                            WHEN prev.remaining_capacity >= curr_d.""Suma_Toneladas_Agrupadas""
                            THEN prev.assigned_supply_seq
                            ELSE prev.assigned_supply_seq + 1
                        END AS assigned_supply_seq,
                        CASE
                             WHEN prev.remaining_capacity >= curr_d.""Suma_Toneladas_Agrupadas""
                             THEN prev.current_supply_id
                             ELSE next_s.""REPEM04_Id""
                        END AS current_supply_id,
                         CASE
                             WHEN prev.remaining_capacity >= curr_d.""Suma_Toneladas_Agrupadas""
                             THEN prev.current_supply_max_tons
                             ELSE next_s.""REPEM04_PesoTotal_Toneladas""
                        END AS current_supply_max_tons,
                        CASE
                            WHEN prev.remaining_capacity >= curr_d.""Suma_Toneladas_Agrupadas""
                            THEN prev.remaining_capacity - curr_d.""Suma_Toneladas_Agrupadas""
                            ELSE next_s.""REPEM04_PesoTotal_Toneladas"" - curr_d.""Suma_Toneladas_Agrupadas""
                        END AS remaining_capacity
                    FROM ticket_assignment prev
                    JOIN tickets_to_compensate curr_d
                        ON prev.year = curr_d.year
                       AND prev.month = curr_d.month
                       AND prev.assigned_demand_seq + 1 = curr_d.demand_seq
                    LEFT JOIN compensation_posibilities next_s
                        ON prev.year = next_s.year
                       AND prev.month = next_s.month
                       AND prev.assigned_supply_seq + 1 = next_s.supply_seq
                    WHERE (prev.remaining_capacity >= curr_d.""Suma_Toneladas_Agrupadas"")
                    OR (prev.remaining_capacity < curr_d.""Suma_Toneladas_Agrupadas"" AND next_s.supply_seq IS NOT NULL)
                )
                SELECT
                    d.""Id_Ticket"",
                    d.""Suma_Toneladas_Agrupadas"" AS ""Suma_Agrupacion_Toneladas_Por_Compensar"",
                    ga.current_supply_id AS ""Nro_Ticket_Compensacion"",
                    ga.current_supply_max_tons AS ""Maximo_Toneladas_Compensables_Ticket_Asignado"",
                    d.""Fecha_Pesaje""
                FROM ticket_assignment ga
                JOIN tickets_to_compensate d
                    ON ga.year = d.year
                   AND ga.month = d.month
                   AND ga.assigned_demand_seq = d.demand_seq
                ORDER BY d.year, d.month, d.demand_seq;
            ");

            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Compensaciones_por_Redondeo""
                            (""Nro_Ticket_Ajustable"", ""Fecha_Pesaje"", ""NUAP"", ""Area_Aprovechamiento"", ""Peso_Ticket"", ""Toneladas_Ajuste"",
                             ""Mes"", ""Año"")
                as
                WITH parent_dct AS MATERIALIZED (SELECT ""REPEM_Detalle_Compensacion_Tickets"".""Id_Ticket"",
                                                        ""REPEM_Detalle_Compensacion_Tickets"".""NUAP"",
                                                        ""REPEM_Detalle_Compensacion_Tickets"".""Fecha_Hora_Pesaje"",
                                                        ""REPEM_Detalle_Compensacion_Tickets"".""Toneladas_Resultantes"",
                                                        ""REPEM_Detalle_Compensacion_Tickets"".""Tipo_Compensacion""
                                                 FROM ""REPEM_Detalle_Compensacion_Tickets""
                                                 WHERE ""REPEM_Detalle_Compensacion_Tickets"".""Tipo_Compensacion"" <> 'ORIGINAL'::text),
                     cte AS (WITH exclusions AS (WITH variables AS (SELECT 0.5 AS margen_tolerancia)
                                                 SELECT tc.""Nro_Ticket_Compensacion"",
                                                        sum(tc.""Suma_Agrupacion_Toneladas_Por_Compensar"") AS ""Suma_Agrupacion_Toneladas_Por_Compensar"",
                                                        max(tc.""Maximo_Toneladas_Compensables"")           AS ""Maximo_Toneladas_Compensables"",
                                                        (sum(tc.""Suma_Agrupacion_Toneladas_Por_Compensar"") + v.margen_tolerancia) <
                                                        max(tc.""Maximo_Toneladas_Compensables"")           AS ""Valido""
                                                 FROM ""REPEM_Tickets_Compensables"" tc,
                                                      variables v
                                                 GROUP BY tc.""Nro_Ticket_Compensacion"", v.margen_tolerancia),
                                  posibilities AS (SELECT rpm.""REPEM04_Id""               AS ""Nro_Ticket_Ajustable"",
                                                          rpm.""REPEM04_FechaHora_Pesaje"" AS ""Fecha_Pesaje"",
                                                          dct.""NUAP"",
                                                          dct.""Toneladas_Resultantes""    AS ""Peso_Ticket"",
                                                          rpm.""REPEM04_PesoTotal_Toneladas"",
                                                          dpm.""Toneladas_Desviacion""     AS ""Toneladas_Ajuste"",
                                                          ap.""REPEM02_Nombre""            AS ""Area_Aprovechamiento""
                                                   FROM ""REPEM_Detalle_Compensacion_Tickets"" dct
                                                            JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                                                 ON dct.""Id_Ticket"" = rpm.""REPEM04_Id""
                                                            JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                                 ON pb.""REPEM03_Id"" = rpm.""REPEM04_Id""
                                                            JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                                                 ON ap.""REPEM02_Codigo"" = dct.""NUAP""
                                                            JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = dct.""NUAP"" AND
                                                                                                             dpm.""Año""::numeric =
                                                                                                             EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                             dpm.""Mes""::numeric =
                                                                                                             EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                             dct.""Toneladas_Resultantes"" >=
                                                                                                             dpm.""Toneladas_Desviacion""
                                                   WHERE pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                                     AND NOT (rpm.""REPEM04_Id"" IN (SELECT exclusions.""Nro_Ticket_Compensacion""
                                                                                   FROM exclusions
                                                                                   WHERE exclusions.""Valido"" = false))
                                                     AND rpm.""REPEM04_RutaCodigo""::text <> '614001'::text),
                                  ranked_posibilities AS (SELECT p.""Nro_Ticket_Ajustable"",
                                                                 p.""Fecha_Pesaje"",
                                                                 p.""NUAP"",
                                                                 p.""Peso_Ticket"",
                                                                 p.""REPEM04_PesoTotal_Toneladas"",
                                                                 p.""Toneladas_Ajuste"",
                                                                 p.""Area_Aprovechamiento"",
                                                                 row_number()
                                                                 OVER (PARTITION BY p.""Toneladas_Ajuste"", p.""Nro_Ticket_Ajustable"" ORDER BY p.""REPEM04_PesoTotal_Toneladas"" DESC) AS rn
                                                          FROM posibilities p
                                                          ORDER BY p.""REPEM04_PesoTotal_Toneladas"" DESC)
                             SELECT DISTINCT ON (rp.""NUAP"", (EXTRACT(year FROM rp.""Fecha_Pesaje"")), (EXTRACT(month FROM rp.""Fecha_Pesaje""))) rp.""Nro_Ticket_Ajustable"",
                                                                                                                                             rp.""Fecha_Pesaje"",
                                                                                                                                             EXTRACT(year FROM rp.""Fecha_Pesaje"")  AS ""Año"",
                                                                                                                                             EXTRACT(month FROM rp.""Fecha_Pesaje"") AS ""Mes"",
                                                                                                                                             rp.""NUAP"",
                                                                                                                                             rp.""Peso_Ticket"",
                                                                                                                                             rp.""REPEM04_PesoTotal_Toneladas"",
                                                                                                                                             rp.""Toneladas_Ajuste"",
                                                                                                                                             rp.""Area_Aprovechamiento""
                             FROM ranked_posibilities rp
                             WHERE rp.rn = 1
                             ORDER BY rp.""NUAP"", (EXTRACT(year FROM rp.""Fecha_Pesaje"")), (EXTRACT(month FROM rp.""Fecha_Pesaje"")),
                                      rp.""REPEM04_PesoTotal_Toneladas"" DESC),
                     restas AS (SELECT cte.""Nro_Ticket_Ajustable"",
                                       max(cte.""Fecha_Pesaje"")                AS max,
                                       '440405001'::bigint                    AS ""NUAP"",
                                       'Medellín'::character varying(20)      AS ""Area_Aprovechamiento"",
                                       sum(cte.""Peso_Ticket"")::numeric(18, 2) AS ""Peso_Ticket"",
                                       - sum(cte.""Toneladas_Ajuste"")          AS ""Toneladas_Ajuste"",
                                       max(cte.""Mes"")                         AS ""Mes"",
                                       max(cte.""Año"")                         AS ""Año""
                                FROM cte
                                GROUP BY cte.""Nro_Ticket_Ajustable"")
                SELECT cte.""Nro_Ticket_Ajustable"",
                       cte.""Fecha_Pesaje"",
                       cte.""NUAP"",
                       cte.""Area_Aprovechamiento"",
                       cte.""Peso_Ticket""::numeric(18, 2) AS ""Peso_Ticket"",
                       dm.""Toneladas_Desviacion""         AS ""Toneladas_Ajuste"",
                       cte.""Mes"",
                       cte.""Año""
                FROM cte
                         JOIN ""REPEM_Distribuciones_de_Microrutas"" dm
                              ON dm.""NUAP"" = cte.""NUAP"" AND dm.""Año""::numeric = EXTRACT(year FROM cte.""Fecha_Pesaje"") AND
                                 dm.""Mes""::numeric = EXTRACT(month FROM cte.""Fecha_Pesaje"")
                UNION ALL
                SELECT restas.""Nro_Ticket_Ajustable"",
                       restas.max AS ""Fecha_Pesaje"",
                       restas.""NUAP"",
                       restas.""Area_Aprovechamiento"",
                       restas.""Peso_Ticket"",
                       restas.""Toneladas_Ajuste"",
                       restas.""Mes"",
                       restas.""Año""
                FROM restas;
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Tickets_Compensables""
                            (""Id_Ticket"", ""Suma_Agrupacion_Toneladas_Por_Compensar"", ""Nro_Ticket_Compensacion"",
                             ""Maximo_Toneladas_Compensables"") as
                WITH cte AS (SELECT td.""Id_Ticket"",
                                    td.""Fecha_Hora_Pesaje""::date    AS ""Fecha_Pesaje"",
                                    sum(td.""Toneladas_Resultantes"") AS ""Suma_Toneladas_Agrupadas""
                             FROM ""REPEM_Detalle_Compensacion_Tickets"" td
                             WHERE td.""Tipo_Compensacion"" = 'PARCIAL'::text
                                OR td.""Tipo_Compensacion"" = 'TOTAL'::text
                             GROUP BY (td.""Fecha_Hora_Pesaje""::date), td.""Id_Ticket""),
                     ranked_rpm AS (SELECT rpm2.""REPEM04_Id"",
                                           rpm2.""REPEM04_FechaHora_Pesaje""::date                                                                        AS ""RPM_Fecha_Pesaje"",
                                           rpm2.""REPEM04_PesoTotal_Toneladas"",
                                           rank()
                                           OVER (PARTITION BY (rpm2.""REPEM04_FechaHora_Pesaje""::date) ORDER BY rpm2.""REPEM04_PesoTotal_Toneladas"" DESC) AS rank_peso
                                    FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm2
                                    WHERE rpm2.""REPEM04_RutaCodigo""::text = ANY
                                          (ARRAY ['615618'::text, '618219'::text, '618119'::text, '618319'::text]))
                SELECT cte.""Id_Ticket"",
                       cte.""Suma_Toneladas_Agrupadas""           AS ""Suma_Agrupacion_Toneladas_Por_Compensar"",
                       ranked_rpm.""REPEM04_Id""                  AS ""Nro_Ticket_Compensacion"",
                       ranked_rpm.""REPEM04_PesoTotal_Toneladas"" AS ""Maximo_Toneladas_Compensables"",
                       cte.""Fecha_Pesaje""
                FROM cte
                         LEFT JOIN ranked_rpm ON ranked_rpm.""RPM_Fecha_Pesaje"" = cte.""Fecha_Pesaje"" AND ranked_rpm.rank_peso = 1 AND
                                                 ranked_rpm.""REPEM04_PesoTotal_Toneladas"" >= cte.""Suma_Toneladas_Agrupadas"";
            ");
            
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Compensaciones_por_Redondeo""
                (""Nro_Ticket_Ajustable"", ""Fecha_Pesaje"", ""NUAP"", ""Area_Aprovechamiento"", ""Peso_Ticket"", ""Toneladas_Ajuste"",
                 ""Mes"", ""Año"")
                as
                WITH parent_dct AS MATERIALIZED (SELECT ""REPEM_Detalle_Compensacion_Tickets"".""Id_Ticket"",
                                                        ""REPEM_Detalle_Compensacion_Tickets"".""NUAP"",
                                                        ""REPEM_Detalle_Compensacion_Tickets"".""Fecha_Hora_Pesaje"",
                                                        ""REPEM_Detalle_Compensacion_Tickets"".""Toneladas_Resultantes"",
                                                        ""REPEM_Detalle_Compensacion_Tickets"".""Tipo_Compensacion""
                                                 FROM ""REPEM_Detalle_Compensacion_Tickets""
                                                 WHERE ""REPEM_Detalle_Compensacion_Tickets"".""Tipo_Compensacion"" <> 'ORIGINAL'::text),
                     total_compensations AS (SELECT parent_dct.""Id_Ticket"",
                                                    parent_dct.""NUAP"",
                                                    parent_dct.""Fecha_Hora_Pesaje"",
                                                    parent_dct.""Toneladas_Resultantes"",
                                                    parent_dct.""Tipo_Compensacion""
                                             FROM parent_dct
                                             WHERE parent_dct.""Tipo_Compensacion"" = 'TOTAL'::text),
                     tickets_compensables AS (SELECT ""REPEM_Tickets_Compensables"".""Id_Ticket"",
                                                     ""REPEM_Tickets_Compensables"".""Suma_Agrupacion_Toneladas_Por_Compensar"",
                                                     ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"",
                                                     ""REPEM_Tickets_Compensables"".""Maximo_Toneladas_Compensables""
                                              FROM ""REPEM_Tickets_Compensables""),
                     cte AS (WITH exclusions AS (SELECT parent_dct.""Id_Ticket"",
                                                        parent_dct.""NUAP"",
                                                        parent_dct.""Fecha_Hora_Pesaje"",
                                                        parent_dct.""Toneladas_Resultantes"",
                                                        parent_dct.""Tipo_Compensacion""
                                                 FROM parent_dct
                                                 WHERE parent_dct.""Tipo_Compensacion"" = 'PARCIAL'::text),
                                  posibilities AS (SELECT rpm.""REPEM04_Id""               AS ""Nro_Ticket_Ajustable"",
                                                          rpm.""REPEM04_FechaHora_Pesaje"" AS ""Fecha_Pesaje"",
                                                          dct.""NUAP"",
                                                          dct.""Toneladas_Resultantes""    AS ""Peso_Ticket"",
                                                          rpm.""REPEM04_PesoTotal_Toneladas"",
                                                          dpm.""Toneladas_Desviacion""     AS ""Toneladas_Ajuste"",
                                                          ap.""REPEM02_Nombre""            AS ""Area_Aprovechamiento""
                                                   FROM ""REPEM_Detalle_Compensacion_Tickets"" dct
                                                            JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                                                 ON dct.""Id_Ticket"" = rpm.""REPEM04_Id""
                                                            JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                                 ON pb.""REPEM03_Id"" = rpm.""REPEM04_Id""
                                                            JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                                                 ON ap.""REPEM02_Codigo"" = dct.""NUAP""
                                                            JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = dct.""NUAP"" AND
                                                                                                             dpm.""Año""::numeric =
                                                                                                             EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                             dpm.""Mes""::numeric =
                                                                                                             EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                             dct.""Toneladas_Resultantes"" >=
                                                                                                             dpm.""Toneladas_Desviacion""
                                                   WHERE pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                                     AND NOT (rpm.""REPEM04_Id"" IN (SELECT exclusions.""Id_Ticket""
                                                                                   FROM exclusions))
                                                     AND rpm.""REPEM04_RutaCodigo""::text <> '614001'::text),
                                  ranked_posibilities AS (SELECT p.""Nro_Ticket_Ajustable"",
                                                                 p.""Fecha_Pesaje"",
                                                                 p.""NUAP"",
                                                                 p.""Peso_Ticket"",
                                                                 p.""REPEM04_PesoTotal_Toneladas"",
                                                                 p.""Toneladas_Ajuste"",
                                                                 p.""Area_Aprovechamiento"",
                                                                 row_number()
                                                                 OVER (PARTITION BY p.""Toneladas_Ajuste"", p.""Nro_Ticket_Ajustable"" ORDER BY p.""REPEM04_PesoTotal_Toneladas"" DESC) AS rn
                                                          FROM posibilities p
                                                          ORDER BY p.""REPEM04_PesoTotal_Toneladas"" DESC)
                             SELECT DISTINCT ON (rp.""NUAP"", (EXTRACT(year FROM rp.""Fecha_Pesaje"")), (EXTRACT(month FROM rp.""Fecha_Pesaje""))) rp.""Nro_Ticket_Ajustable"",
                                                                                                                                             rp.""Fecha_Pesaje"",
                                                                                                                                             EXTRACT(year FROM rp.""Fecha_Pesaje"")  AS ""Año"",
                                                                                                                                             EXTRACT(month FROM rp.""Fecha_Pesaje"") AS ""Mes"",
                                                                                                                                             rp.""NUAP"",
                                                                                                                                             rp.""Peso_Ticket"",
                                                                                                                                             rp.""REPEM04_PesoTotal_Toneladas"",
                                                                                                                                             rp.""Toneladas_Ajuste"",
                                                                                                                                             rp.""Area_Aprovechamiento""
                             FROM ranked_posibilities rp
                             WHERE rp.rn = 1
                             ORDER BY rp.""NUAP"", (EXTRACT(year FROM rp.""Fecha_Pesaje"")), (EXTRACT(month FROM rp.""Fecha_Pesaje"")),
                                      rp.""REPEM04_PesoTotal_Toneladas"" DESC),
                     to_substract
                         AS (SELECT COALESCE(tc.""Nro_Ticket_Compensacion"", cte.""Nro_Ticket_Ajustable"") AS ""Nro_Ticket_Ajustable"",
                                    cte.""Fecha_Pesaje"",
                                    440405001                                                          AS ""NUAP"",
                                    'Medellín'::character varying(20)                                  AS ""Area_Aprovechamiento"",
                                    cte.""Mes"",
                                    cte.""Año"",
                                    sum(cte.""Toneladas_Ajuste"")                                        AS ""Toneladas_Ajuste""
                             FROM cte
                                      LEFT JOIN tickets_compensables tc ON tc.""Id_Ticket"" = cte.""Nro_Ticket_Ajustable"" AND
                                                                           (cte.""Nro_Ticket_Ajustable"" IN
                                                                            (SELECT total_compensations.""Id_Ticket""
                                                                             FROM total_compensations))
                             GROUP BY cte.""Mes"", cte.""Año"", cte.""Nro_Ticket_Ajustable"", cte.""Fecha_Pesaje"",
                                      tc.""Nro_Ticket_Compensacion"")
                SELECT cte.""Nro_Ticket_Ajustable"",
                       cte.""Fecha_Pesaje"",
                       cte.""NUAP"",
                       cte.""Area_Aprovechamiento"",
                       cte.""Peso_Ticket""::numeric(18, 2) AS ""Peso_Ticket"",
                       dm.""Toneladas_Desviacion""         AS ""Toneladas_Ajuste"",
                       cte.""Mes"",
                       cte.""Año""
                FROM cte
                         JOIN ""REPEM_Distribuciones_de_Microrutas"" dm
                              ON dm.""NUAP"" = cte.""NUAP"" AND dm.""Año""::numeric = EXTRACT(year FROM cte.""Fecha_Pesaje"") AND
                                 dm.""Mes""::numeric = EXTRACT(month FROM cte.""Fecha_Pesaje"")
                UNION ALL
                SELECT ts.""Nro_Ticket_Ajustable"",
                       ts.""Fecha_Pesaje"",
                       ts.""NUAP"",
                       ts.""Area_Aprovechamiento"",
                       0::numeric(18, 2)       AS ""Peso_Ticket"",
                       - ts.""Toneladas_Ajuste"" AS ""Toneladas_Ajuste"",
                       ts.""Mes"",
                       ts.""Año""
                FROM to_substract ts;
            ");
        }
    }
}
