﻿using Classification.Domain.Constants;
using Common.Domain.Constants;
using Common.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Common.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class MaterialTypeConfiguration : IEntityTypeConfiguration<MaterialType>
{
	private const string DateTimeWithoutTimeZone = "timestamp without time zone";

	public void Configure(EntityTypeBuilder<MaterialType> builder)
    {
        builder.ToTable(CommonTableNames.MaterialType);
        
        builder
            .HasKey(m => m.Id)
            .HasName(MaterialTypeColumns.PrimaryKeyConstraintName);
        
        builder
            .Property(m => m.Id)
            .HasColumnName(MaterialTypeColumns.Id)
            .IsRequired();
        
        builder
            .Property(mt => mt.Name)
            .HasMaxLength(50)
            .HasColumnName(MaterialTypeColumns.Name)
            .IsRequired();
        
        builder
            .Property(mt => mt.Description)
            .HasMaxLength(50)
            .HasColumnName(MaterialTypeColumns.Description)
            .IsRequired(false);

		builder.Property(m => m.IsDeleted)
			.HasDefaultValue(false)
			.HasColumnName(MaterialTypeColumns.IsDeleted)
			.IsRequired();

		builder
			.Property(m => m.CreationDate)
			.HasColumnName(MaterialTypeColumns.CreationDate)
			.HasColumnType(DateTimeWithoutTimeZone)
			.IsRequired();

		builder
			.Property(m => m.CreatedBy)
			.HasMaxLength(60).HasColumnName(MaterialTypeColumns.CreatedBy)
			.IsRequired();

		builder
			.Property(m => m.LastModificationDate)
			.HasColumnName(MaterialTypeColumns.LastModificationDate)
			.HasColumnType(DateTimeWithoutTimeZone)
			.IsRequired(false);

		builder
			.Property(m => m.LastModifiedBy)
			.HasMaxLength(60).HasColumnName(MaterialTypeColumns.LastModifiedBy)
			.IsRequired(false);

		builder
			.Property(m => m.DeletionDate)
			.HasColumnName(MaterialTypeColumns.DeletionDate)
			.HasColumnType(DateTimeWithoutTimeZone)
			.IsRequired(false);

		builder
			.Property(m => m.DeletedBy)
			.HasMaxLength(60)
			.HasColumnName(MaterialTypeColumns.DeletedBy)
			.IsRequired(false);

		builder
            .Property(mt => mt.GroupId)
            .HasColumnName(MaterialTypeColumns.GroupId)
            .IsRequired();

		builder.Ignore(mt => mt.Group);
			
	}
}