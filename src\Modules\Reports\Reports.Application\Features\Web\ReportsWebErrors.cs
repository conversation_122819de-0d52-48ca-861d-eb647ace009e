using System.Net;
using Orion.SharedKernel.Domain.Entities.Error;
using Orion.SharedKernel.Domain.Entities.Loggers;

namespace Reports.Application.Features.Web;

public class TownNotFound : Error
{
    public TownNotFound(IEnumerable<string> townCodes)
    {
        Title = "Uno o varios municipios no existen.";
        Messages = new List<string>(
            townCodes.Select(code => $"No se encontró el municipio con código: {code}."));
        Status = HttpStatusCode.NotFound;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}

public class ExistentWeighinCannotBeModifiedWithotCancelDate : Error
{
    public ExistentWeighinCannotBeModifiedWithotCancelDate(IEnumerable<string> weighingIds)
    {
        Title = "Uno o varios pesajes no pueden ser modificados sin fecha de cancelación.";
        Messages = new List<string>(
            weighingIds.Select(id => $"El pesaje con código: {id} no puede ser modificado sin fecha de cancelación."));
        Status = HttpStatusCode.Conflict;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}

public class ReportTypeIsInvalid : Error
{
    public ReportTypeIsInvalid(string reportType)
    {
        Title = "El tipo de reporte no es válido.";
        Messages = new List<string> { $"El tipo de reporte no es válido: {reportType}" };
        Status = HttpStatusCode.Conflict;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}

public class ReportExportTypeIsInvalid : Error
{
    public ReportExportTypeIsInvalid(string? reportType)
    {
        Title = "El tipo de archivo para el reporte no es válido.";
        Messages = new List<string>
        {
            reportType is null ? $"El tipo de archivo para el reporte no es válido: {reportType }"
                : "El tipo de archivo para el reporte no puede ser nulo."
        };
        Status = HttpStatusCode.Conflict;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}

public class VehicleRetrievalAlreadyExists : Error
{
    public VehicleRetrievalAlreadyExists(IEnumerable<string> ids)
    {
        Title = "Uno o varios registros de recolección vehicular ya existen.";
        Messages = new List<string>(
            ids.Select(id => $"El registro de recolección vehicular ya existe con código: {id}"));
        Status = HttpStatusCode.Conflict;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}

public class CollectionByMicrorouteAlreadyExists : Error
{
    public CollectionByMicrorouteAlreadyExists(IEnumerable<string> ids)
    {
        Title = "Uno o varios registros de recolección por micro ruta ya existen.";
        Messages = new List<string>(
            ids.Select(id => $"El registro de recolección por micro ruta ya existe con código: {id}"));
        Status = HttpStatusCode.Conflict;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}

public class UnloadingTicketRequestFailed : Error
{
    public UnloadingTicketRequestFailed(string message)
    {
        Title = "No se pudo generar el ticket de descarga.";
        Messages = new List<string> { "Ocurrio un error inesperado al intentar generar el ticket de descarga.", $"Detalle: {message}" };
        Status = HttpStatusCode.InternalServerError;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Error;
    }
}

public class RegulatoryRouteCodesNotFound : Error
{
    public RegulatoryRouteCodesNotFound()
    {
        Title = "No se encontraron códigos de ruta regulatorios.";
        Messages = new List<string> { "No se encontró ningun registro de códigos de ruta regulatorios." };
        Status = HttpStatusCode.NoContent;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}

public class DistributionsNotFound : Error
{
    public DistributionsNotFound()
    {
        Title = "No se encontraron los aforos del mes consultado.";
        Messages = new List<string> { "No se encontró ningun registro de distribuciones en el periodo dado." };
        Status = HttpStatusCode.NoContent;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}

public class ReportFormat14NotFound : Error
{
    public ReportFormat14NotFound()
    {
        Title = "No se encontro un reporte formato 14 valido para el periodo dado.";
        Messages = new List<string> { "No se encontró ningun registro de reporte 14 en el periodo dado." };
        Status = HttpStatusCode.NoContent;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}

public class ReportFormat34NotFound : Error
{
    public ReportFormat34NotFound()
    {
        Title = "No se encontro un reporte formato 34 valido para el periodo dado.";
        Messages = new List<string> { "No se encontró ningun registro de reporte 34 en el periodo dado." };
        Status = HttpStatusCode.NoContent;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}

public class RecyclingAreaNotFound : Error
{
    public RecyclingAreaNotFound()
    {
        Title = "No se encontraron las areas de prestación de servicio.";
        Messages = new List<string> { "Se requiere cargar las areas de prestación de servicio." };
        Status = HttpStatusCode.NoContent;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}

public class WeighingScaleNotFound : Error
{
    public WeighingScaleNotFound()
    {
        Title = "No se encontro ningún pesaje de balanza en el periodo dado.";
        Messages = new List<string> { "No se encontró ningun registro en la integración con balanzas para el periodo dado." };
        Status = HttpStatusCode.NoContent;
        Platform = PlatformApp.Web;
        ErrorCode = LevelErrorCode.Information;
    }
}