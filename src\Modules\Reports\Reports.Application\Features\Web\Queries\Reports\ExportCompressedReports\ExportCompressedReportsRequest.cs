﻿using MediatR;
using Reports.Application.DTOs.Reports;

namespace Reports.Application.Features.Web.Queries.Reports.ExportCompressedReports;

public record ExportCompressedReportsRequest : IRequest<ExportCompressedReportsResponse>
{
    public DateOnly FromDate { get; init; }
    public DateOnly ToDate { get; init; }

    public ExportCompressedReportsRequest(DateTime fromDate, DateTime toDate)
    {
        FromDate = DateOnly.FromDateTime(fromDate);
        ToDate = DateOnly.FromDateTime(toDate);
    }
}