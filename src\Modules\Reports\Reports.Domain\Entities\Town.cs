﻿using Orion.SharedKernel.Domain.Entities;

namespace Reports.Domain.Entities;

public class Town : Entity<string>
{
    private const int MinimumCodeChars = 5;
    
    public string Code { get; set; }
    public string Name { get; set; }
    public string Department { get; set; }
    public string Province { get; set; }

    public Town() { }

    public Town(string code)
    {
        Code = code.PadLeft(MinimumCodeChars, '0');
    }
    
    public Town(string code, string name, string department, string province)
    {
        Code = code;
        Name = name;
        Department = department;
        Province = province;
    }
}