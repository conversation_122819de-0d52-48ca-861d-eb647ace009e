﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Add_RSUI_CrossLinking_Tables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_06-Peajes",
                columns: table => new
                {
                    REPEM06_Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    REPEM06_Codigo_Tipo_Vehiculo = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    REPEM06_Valor = table.Column<double>(type: "double precision", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Reporting-Emvarias_06-Peajes_key", x => x.REPEM06_Id);
                });

            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_07-Microrutas",
                columns: table => new
                {
                    REPEM07_Numero_de_Microruta = table.Column<long>(type: "bigint", maxLength: 20, nullable: false),
                    REPEM07_Ruta_de_Higiene = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    REPEM07_NUAP = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Reporting-Emvarias_07-Microrutas_key", x => new { x.REPEM07_Numero_de_Microruta, x.REPEM07_Ruta_de_Higiene });
                });

            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_08-Clientes",
                columns: table => new
                {
                    REPEM08_NIT = table.Column<long>(type: "bigint", maxLength: 10, nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    REPEM08_Nombre_Completo = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Reporting-Emvarias_08-Clientes_key", x => x.REPEM08_NIT);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_06-Peajes");

            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_07-Microrutas");

            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_08-Clientes");
        }
    }
}
