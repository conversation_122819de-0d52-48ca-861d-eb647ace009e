﻿using AutoMapper;
using MediatR;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Application.Exceptions;
using Reports.Application.DTOs.Microroutes;
using Reports.Domain;

namespace Reports.Application.Features.Web.Queries.Microroutes.GetAllRegulatoryRouteCodes;

internal class GetAllRegulatoryRouteCodesHandler : MappingService, IRequestHandler<GetAllRegulatoryRouteCodesRequest, GetAllRegulatoryRouteCodesResponse>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private const int MinutesTillCacheInvalidation = 360;

    public GetAllRegulatoryRouteCodesHandler(IMapper mapper, IReportsUnitOfWork unitOfWork) : base(mapper)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<GetAllRegulatoryRouteCodesResponse> Handle(GetAllRegulatoryRouteCodesRequest request, CancellationToken cancellationToken)
    {
        var microroutes = await _unitOfWork.Microroutes.GetAllAsync(
            isPaginated: false,
            useCache: true,
            cacheExpirationInMinutes: MinutesTillCacheInvalidation,
            cancellationToken: cancellationToken);
        
        if (microroutes.TotalRecords == 0)
            throw new OrionException(_unitOfWork.ErrorService.GenerateError(new RegulatoryRouteCodesNotFound()));
        
        return new GetAllRegulatoryRouteCodesResponse(Mapper.Map<IEnumerable<RegulatoryRouteCodesDto>>(microroutes.Results));
    }
}