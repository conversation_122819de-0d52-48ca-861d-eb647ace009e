using System.Collections.Immutable;
using System.Linq.Expressions;
using AutoMapper;
using MediatR;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Application.Exceptions;
using Reports.Domain;
using Reports.Domain.Entities;
using Reports.Domain.Events;

namespace Reports.Application.Features.Web.Commands.Weighins.CreateWeighing;

internal sealed class CreateWeighingHandler : MappingService,
    IRequestHandler<CreateWeighingRequest, CreateWeighingResponse>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly IMediator _mediator;

    public CreateWeighingHandler(IMapper mapper, IMediator mediator, IReportsUnitOfWork reportsUnitOfWork) : base(mapper)
    {
        _unitOfWork = reportsUnitOfWork;
        _mediator = mediator;
    }

    public async Task<CreateWeighingResponse> Handle(CreateWeighingRequest request, CancellationToken cancellationToken)
    {
        var weighins = Mapper.Map<List<WeighingScale>>(request.Weighins);
        
        var (weighinsToInsert, weighinsToUpdate, weighinsToCancel)
            = await GetWeighinsByOperation(weighins, cancellationToken);

        await ValidateTownCodes(weighins, cancellationToken);

        await _unitOfWork.BeginTransactionAsync(cancellationToken);

        if (weighinsToInsert.Any())
        {
            // TODO: Bug, no BulkInsert no detecta el Id. Lo toma como Nulo y rompe la restricción de la PK.
            //await _unitOfWork.WeighingScales.BulkInsertAsync(weighinsToInsert, cancellationToken);
            
            await _unitOfWork.WeighingScales.AddRangeAsync(weighinsToInsert, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
        }
            

        if (weighinsToUpdate.Any() || weighinsToCancel.Any())
        {
            var weighinsToModify = weighinsToUpdate
                .Concat(weighinsToCancel)
                .ToList();

            await _unitOfWork.WeighingScales.BulkUpdateAsync(weighinsToModify, cancellationToken);
        }
        
        await _unitOfWork.CommitAsync(cancellationToken);

        if (weighinsToInsert.Any())
            RaiseEvents(weighinsToInsert, CancellationToken.None);

        return new CreateWeighingResponse(weighinsToInsert, weighinsToUpdate, weighinsToCancel);
    }

    private async Task<(List<WeighingScale> weighinsToInsert, List<WeighingScale> weighinsToUpdate, List<WeighingScale> weighinsToCancel)>
        GetWeighinsByOperation(List<WeighingScale> weighins, CancellationToken cancellationToken)
    {
        var weighingIds = weighins
            .Select(p => p.Id!)
            .ToImmutableHashSet();
        
        var existingWeighinsToModify = await _unitOfWork
            .WeighingScales
            .GetAllAsync(
                predicate: p => weighingIds.Contains(p.Id!),
                useCache: false,
                isPaginated: false,
                maxQueryCount: int.MaxValue,
                cancellationToken: cancellationToken);
        
        var existingWeighinIds = existingWeighinsToModify
            .Results
            .Select(p => p.Id!)
            .ToImmutableHashSet();

        var weighinsToInsert = weighins
            .Where(weighin => !existingWeighinIds.Contains(weighin.Id!))
            .ToList();

        var existingWeighinsIdsWithoutCancelDate = existingWeighinsToModify
            .Results
            .Where(weighin => weighin.CancelDate is null)
            .Select(p => p.Id!)
            .ToImmutableHashSet();
        
        var weighinsToCancelIds = weighins
            .Where(weighin => weighin.CancelDate is not null 
                              && existingWeighinsIdsWithoutCancelDate.Contains(weighin.Id!))
            .Select(p => p.Id!)
            .ToImmutableHashSet();
        
        var weighinsToCancel = weighins
            .Where(weighin => weighinsToCancelIds.Contains(weighin.Id!))
            .ToList();

        var weighinsToUpdate = weighins
            .Where(weighin => !weighinsToCancelIds.Contains(weighin.Id!)
                && existingWeighinIds.Contains(weighin.Id!))
            .ToList();

        return (weighinsToInsert, weighinsToUpdate, weighinsToCancel);
    }

    private async Task ValidateTownCodes(List<WeighingScale> weighins, CancellationToken cancellationToken)
    {
        var townCodes = GetHashedMemberSet(weighins, p => p.TownCode);

        var allTownsFound = await _unitOfWork
            .Towns
            .AllMatchesByIdAsync(townCodes, cancellationToken);

        if (!allTownsFound)
        {
            var towns = await _unitOfWork
                .Towns
                .GetAllAsync(
                    predicate: GetPredicateMatchTowns(townCodes),
                    useCache: false,
                    isPaginated: false,
                    maxQueryCount: int.MaxValue,
                    cancellationToken: cancellationToken);

            var nonFoundTownCodes = townCodes.Except(
                towns.Results.Select(p => p.Code));

            throw new OrionException(
                _unitOfWork
                    .ErrorService
                    .GenerateError(
                        new TownNotFound(nonFoundTownCodes)));
        }
    }

    private static ImmutableHashSet<TProperty> GetHashedMemberSet<TEntity, TProperty>(List<TEntity> entities, Func<TEntity, TProperty> selector)
    {
        return entities.Select(selector).ToImmutableHashSet();
    }

    private static Expression<Func<Town, bool>> GetPredicateMatchTowns(ImmutableHashSet<string> townIdsToCheck)
    {
        return town => townIdsToCheck.Contains(town.Code);
    }

    private async Task RaiseEvents(IEnumerable<WeighingScale> weighins, CancellationToken cancellationToken)
    {
        await _mediator.Publish(new WeighinsUpsertDomainEvent(weighins), cancellationToken);
    }
}