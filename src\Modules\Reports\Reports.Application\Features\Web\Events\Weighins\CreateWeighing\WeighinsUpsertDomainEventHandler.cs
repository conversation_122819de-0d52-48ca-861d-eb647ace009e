﻿using AutoMapper;
using MediatR;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Application.Exceptions;
using Reports.Application.Services.Http.Legacy;
using Reports.Application.Services.Http.Legacy.Request;
using Reports.Domain;
using Reports.Domain.Events;

namespace Reports.Application.Features.Web.Events.Weighins.CreateWeighing;

public class WeighinsUpsertDomainEventHandler : INotificationHandler<WeighinsUpsertDomainEvent>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly ILegacyApiService _legacyApiService;
    private readonly IMapper _mapper;
    private readonly ICurrentUserService _currentUserService;

    
    public WeighinsUpsertDomainEventHandler(IReportsUnitOfWork unitOfWork, ICurrentUserService currentUserService, ILegacyApiService legacyApiService, IMapper mapper)
    {
        _unitOfWork = unitOfWork;
        _legacyApiService = legacyApiService;
        _mapper = mapper;
        _currentUserService = currentUserService;
    }
    
    public async Task Handle(WeighinsUpsertDomainEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            var startTime = DateTime.Now;
            
            var authorization = _currentUserService.GetJWTLegacy();
            
            var requests = _mapper.Map<List<UnloadingTicketRequest>>(notification.Weighins);

            var tasks = requests
                .Select(request => request.SetAuthorization(authorization))    
                .Select(request => _legacyApiService.SendUnloadingTicketAsync(request))
                .ToList();

            await Task.WhenAll(tasks);
            
            var ticketLogs = requests.ToDictionary(
                request => $"Se cargó el ticket de descarga: {request.Number}", 
                request => (object)$"Vehiculo: {request.LicensePlate} - Peso Total: {request.GrossWeight} - Peso Tara: {request.TareWeight} - Fecha: {request.Datetime}"
            );
            
            var endTime = DateTime.Now;
            
            var secondsElapsed = (endTime - startTime).TotalSeconds;

            await _unitOfWork.LogEventMessage.GenerateLogEventMessage($"Se cargaron correctamente {ticketLogs.Count} tickets de descarga en {secondsElapsed} segundos.", ticketLogs);
        }
        catch (OrionException)
        {
            throw;
        }
        catch (Exception ex)
        {
            throw new OrionException(_unitOfWork.ErrorService.GenerateError(new UnloadingTicketRequestFailed(ex.Message)));
        }
    }
}