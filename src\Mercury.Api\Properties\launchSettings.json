{"profiles": {"Mercury.Api": {"commandName": "Project", "launchBrowser": true, "launchUrl": "scalar", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7058;http://localhost:5142"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": false, "launchUrl": "scalar", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/scalar", "publishAllPorts": true, "useSSL": true}}, "$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:48218", "sslPort": 44378}}}