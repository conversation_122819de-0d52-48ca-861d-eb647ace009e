using Reports.Domain.Entities;

namespace Reports.Domain.Services;

/// <summary>
/// Specialized audit service for WeighingScale entities.
/// Provides domain-specific audit operations for weighing scale operations.
/// </summary>
public interface IWeighingScaleAuditService
{
    /// <summary>
    /// Creates audit records for weighing scale bulk operations
    /// </summary>
    /// <param name="insertedWeighins">Weighing scales that were inserted</param>
    /// <param name="updatedWeighins">Weighing scales that were updated (with previous state)</param>
    /// <param name="cancelledWeighins">Weighing scales that were cancelled</param>
    /// <param name="user">The user who performed the operation</param>
    /// <param name="operationMetadata">Optional metadata about the operation</param>
    /// <returns>Collection of audit records</returns>
    Task<IEnumerable<HistoricalAuditColumns>> CreateWeighingScaleAuditRecordsAsync(
        IEnumerable<WeighingScale> insertedWeighins,
        IEnumerable<EntityChange<WeighingScale>> updatedWeighins,
        IEnumerable<WeighingScale> cancelledWeighins,
        string user,
        string? operationMetadata = null);

    /// <summary>
    /// Stores audit records in the repository
    /// </summary>
    /// <param name="auditRecords">The audit records to store</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task StoreAuditRecordsAsync(IEnumerable<HistoricalAuditColumns> auditRecords, CancellationToken cancellationToken);

    /// <summary>
    /// Creates entity changes by comparing current weighing scales with their previous state
    /// </summary>
    /// <param name="currentWeighins">Current state of weighing scales</param>
    /// <param name="previousWeighins">Previous state of weighing scales</param>
    /// <returns>Collection of entity changes</returns>
    IEnumerable<EntityChange<WeighingScale>> CreateEntityChanges(
        IEnumerable<WeighingScale> currentWeighins,
        IEnumerable<WeighingScale> previousWeighins);
}
