using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Weighings;

public class CreateWeighingRequestDto
{
    /// <summary>
    /// Nro. de comprobante.
    /// </summary>
    /// <remarks>10 carácteres alfanuméricos</remarks>
    /// <example>AF1020FA32</example>
    [JsonPropertyName("NroComprobante")]
    public string? Id { get; set; }
    
    /// <summary>
    /// Placa del vehículo.
    /// </summary>
    /// <remarks>Mínimo 5 carácteres alfanuméricos</remarks>
    /// <example>GKV295</example>
    [JsonPropertyName("Placa")]
    public string? LicensePlate { get; set; }
    
    /// <summary>
    /// Fecha de ingreso.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD HH:MM:SS</remarks>
    /// <example>2023-07-14 12:02:36</example>
    [JsonPropertyName("FechaIngreso")]
    public string? EntryDate { get; set; }
    
    /// <summary>
    /// Hora de ingreso.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD HH:MM:SS</remarks>
    /// <example>2023-07-14 11:18:00</example>
    [JsonPropertyName("HoraIngreso")]
    public string? EntryTime { get; set; }
    
    /// <summary>
    /// Codigo del municipio.
    /// </summary>
    /// <remarks>Deben ser 5 caracteres, si no, se completara con ceros a la izquierda.</remarks>
    /// <example>05001</example>
    [JsonPropertyName("MunicipioViaje")]
    public string? TownCode { get; set; }
    
    /// <summary>
    /// Número de identificación tributaria del transporte.
    /// </summary>
    /// <example>811007618</example>
    [JsonPropertyName("NitViaje")]
    public string? NIT { get; set; }
    
    /// <summary>
    /// Peso de entrada.
    /// </summary>
    /// <example>28730</example>
    [JsonPropertyName("PesoEntrada")]
    public double ArrivingWeight { get; set; }
    
    /// <summary>
    /// Peso de salida.
    /// </summary>
    /// <example>13550</example>
    [JsonPropertyName("PesoDeposito")]
    public double LeavingWeight { get; set; }
    
    /// <summary>
    /// Fecha de salida.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD HH:MM:SS</remarks>
    /// <example>2023-07-14 12:02:36</example>
    [JsonPropertyName("FechaSalida")]
    public string? EgressDate { get; set; }
    
    /// <summary>
    /// Hora de salida.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD HH:MM:SS</remarks>
    /// <example>2023-07-14 12:02:36</example>
    [JsonPropertyName("HoraSalida")]
    public string? EgressTime { get; set; }
    
    /// <summary>
    /// Código tipo de material.
    /// </summary>
    /// <example>302</example>
    [JsonPropertyName("TipoResiduo")]
    public string? MaterialType { get; set; }
    
    /// <summary>
    /// Tipo de origen.
    /// </summary>
    /// <remarks>(1) NUAP, (2) NUET, (3) NUECA, (4) Otros</remarks>
    /// <example>NUAP</example>
    /// <example>1</example>
    [JsonPropertyName("TipoOrigen")]
    public string? OriginType { get; set; }
    
    /// <summary>
    /// Código de área de prestación del servicio.
    /// </summary>
    /// <example>124105088</example>
    [JsonPropertyName("Nuap")]
    public string? NUAP { get; set; }
    
    /// <summary>
    /// Tipo de carga.
    /// </summary>
    /// <remarks>(1) A, (2) M</remarks>
    /// <example>A</example>
    /// <example>1</example>
    [JsonPropertyName("Tipo")]
    public string? LoadingType { get; set; }
    
    /// <summary>
    /// Fecha y hora de carga del registro en la balanza.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD HH:MM:SS</remarks>
    /// <example>2023-07-14 13:00:02</example>
    [JsonPropertyName("FechaCargue")]
    public string? LoadingDate { get; set; }
    
    /// <summary>
    /// Fecha y hora de cancelación.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD HH:MM:SS</remarks>
    /// <example>2023-07-14 12:02:36</example>
    [JsonPropertyName("FechaCancela")]
    public string? CancelDate { get; set; }
}