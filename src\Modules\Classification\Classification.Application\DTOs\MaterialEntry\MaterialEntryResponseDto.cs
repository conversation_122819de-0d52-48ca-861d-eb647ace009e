﻿using System.Text.Json.Serialization;

namespace Classification.Application.DTOs.MaterialEntry;

public class MaterialEntryResponseDto
{
    /// <summary>
    /// Identificador del ingreso de material.
    /// </summary>
    /// <example>1</example>
    [JsonPropertyName("id")]
    public int Id { get; set; }
    
    /// <summary>
    /// Fecha de entrada de material.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD HH:MM:SS</remarks>
    /// <example>2024-07-14 21:02:18</example>
    [JsonPropertyName("entryDate")]
    public string? EntryDate { get; set; }
    
    /// <summary>
    /// Responsable del ingreso del registro.
    /// </summary>
    /// <example>jna<PERSON>mura</example>
    [JsonPropertyName("responsible")]
    public string? Responsible { get; set; }
    
    /// <summary>
    /// Placa del vehículo.
    /// </summary>
    /// <example>GKV295</example>
    [JsonPropertyName("licensePlate")]
    public string? LicensePlate { get; set; }
    
    /// <summary>
    /// Ruta del vehículo.
    /// </summary>
    /// <example>A204K12</example>
    [JsonPropertyName("route")]
    public string? Route { get; set; }
    
    /// <summary>
    /// Origen del ingreso.
    /// </summary>
    /// <example>Web</example>
    [JsonPropertyName("origin")]
    public string? Origin { get; set; }
    
    /// <summary>
    /// Detalles del ingreso.
    /// </summary>
    /// <example cref="MaterialEntryDetailResponseDto">MaterialEntryDetailResponseDto</example>
    [JsonPropertyName("details")]
    public IEnumerable<MaterialEntryDetailResponseDto> Details { get; set; }
}