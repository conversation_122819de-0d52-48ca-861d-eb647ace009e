﻿using AutoMapper;
using Classification.Application.Common.Extensions;
using Classification.Application.DTOs.MaterialEntry;
using Classification.Domain.Entities;

namespace Classification.Application.Common.MappingProfiles;

public class DomainToDtoMappingProfile : Profile
{
    public DomainToDtoMappingProfile()
    {
        #region MaterialEntry

        CreateMap<Sack, SackResponseDto>()
            .ForMember(dest => dest.Presentation,
                opt =>
                    opt.MapFrom(src => src.Presentation.Value));

        CreateMap<MaterialEntryDetail, MaterialEntryDetailResponseDto>();

        CreateMap<MaterialEntry, MaterialEntryResponseDto>()
            .ForMember(dest => dest.EntryDate,
                opt =>
                    opt.MapFrom(src => src.Date.ToStandardDateTimeString()))
            .ForMember(dest => dest.Origin,
                opt =>
                    opt.MapFrom(src => src.Origin.ToString()));

        #endregion
    }
}