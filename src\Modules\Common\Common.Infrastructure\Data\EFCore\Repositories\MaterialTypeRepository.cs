﻿using System.Linq.Expressions;
using Common.Domain.Entities;
using Common.Domain.Repositories;
using Microsoft.EntityFrameworkCore;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;

namespace Common.Infrastructure.Data.EFCore.Repositories;

public class MaterialTypeRepository : Repository<MaterialType, int, CommonDbContext>, IMaterialTypeRepository
{
    public MaterialTypeRepository(IDbContextProvider<CommonDbContext> dbContextProvider, ICacheService cacheService) : base(dbContextProvider, cacheService) { }
}