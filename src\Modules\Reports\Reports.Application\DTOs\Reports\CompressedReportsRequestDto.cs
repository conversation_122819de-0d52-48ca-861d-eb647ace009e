﻿namespace Reports.Application.DTOs.Reports;

public record CompressedReportsRequestDto
{
    /// <summary>
    /// Fecha de entrada a filtrar desde.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD</remarks>
    /// <example>2023-07-14</example>
    public DateTime FromDate { get; init; }

    /// <summary>
    /// Fecha de entrada a filtrar hasta.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD</remarks>
    /// <example>2023-07-14</example>
    public DateTime ToDate { get; init; }
}