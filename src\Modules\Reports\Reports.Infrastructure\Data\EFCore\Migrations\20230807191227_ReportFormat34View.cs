﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class ReportFormat34View : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                CREATE OR REPLACE VIEW ""View_Reporte_Formato_34"" AS
				SELECT
					********* AS ""NUSD"",
					""REPEM03_Tipo_de_origen"" AS ""OriginType"",
					CASE WHEN ""REPEM03_Tipo_de_origen"" = 'Others' THEN '0'
						 ELSE ""REPEM03_Nro_Unico_Area_Prestacion""
					END AS ""PlaceOriginNumber"",
					Recoleccion_Vehicular.""REPEM05_Empresa"" AS ""CompanyName"",
					""REPEM03_Nro_Identificacion_Tributaria"" AS ""NIT"",
					""REPEM03_Municipio"" AS ""DaneCode"",
					""REPEM03_Patente"" AS ""LicensePlate"",
					""REPEM03_Fecha_de_entrada""::DATE AS ""EntryDate"",
					""REPEM03_Fecha_de_entrada""::TIME AS ""EntryTime"",
					""REPEM03_Fecha_de_egreso""::DATE AS ""EgressDate"",
					""REPEM03_Fecha_de_egreso""::TIME AS ""EgressTime"",
					Recoleccion_Vehicular.""REPEM05_Toneladas"" AS ""Tons""
				FROM ""Reporting-Emvarias_03-Pesaje_de_Balanza"" Pesaje_de_Balanza
				INNER JOIN ""Reporting-Emvarias_05-Recoleccion_Vehicular"" Recoleccion_Vehicular
					ON Pesaje_de_Balanza.""REPEM03_Nro_Unico_Area_Prestacion"" = Recoleccion_Vehicular.""REPEM05_NUAP"" AND
					   Pesaje_de_Balanza.""REPEM03_Patente"" = Recoleccion_Vehicular.""REPEM05_Patente"" AND
					   Recoleccion_Vehicular.""REPEM05_FechaHora_Recoleccion""
							BETWEEN Pesaje_de_Balanza.""REPEM03_Fecha_de_entrada""
								AND Pesaje_de_Balanza.""REPEM03_Fecha_de_egreso""
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"DROP VIEW public.""View_Reporte_Formato_34""");
        }
    }
}
