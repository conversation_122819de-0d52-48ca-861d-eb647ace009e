﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Reports_14_and_34_rounds : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                DROP VIEW public.""View_Reporte_Formato_14"";

                CREATE OR REPLACE VIEW public.""View_Reporte_Formato_14""
                 AS
                 SELECT pb.""REPEM03_Nro_Unico_Area_Prestacion"" AS ""NUAP"",
                    pb.""REPEM03_Tipo_de_origen"" AS ""OriginType"",
                    ********* AS ""DepositPlace"",
                    pb.""REPEM03_Patente"" AS ""LicensePlate"",
                    pb.""REPEM03_Fecha_de_entrada""::date AS ""EntryDate"",
                    pb.""REPEM03_Fecha_de_entrada""::time without time zone AS ""EntryTime"",
                    mr.""REPEM04_Microruta"" AS ""Microroute"",
                    ROUND(CAST(mr.""REPEM04_TonLimUrb"" AS NUMERIC), 3) AS ""UrbanCleaningTons"",
                    ROUND(CAST(mr.""REPEM04_TonBarrido"" AS NUMERIC), 3) AS ""SweepingTons"",
                    ROUND(CAST((mr.""REPEM04_TonLimUrb"" + mr.""REPEM04_TonBarrido"") AS NUMERIC), 3) AS ""NonRecyclableTons"",
                    ROUND(CAST(mr.""REPEM04_TonRechazo"" AS NUMERIC), 3) AS ""RejectedTons"",
                    ROUND(CAST(mr.""REPEM04_TonResAprob"" AS NUMERIC), 3) AS ""RecyclableTons"",
                    1 AS ""MeasuringUnit"",
                    mr.""REPEM04_Valor_peaje"" AS ""Toll""
                   FROM ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                     JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" mr ON mr.""REPEM04_NUAP"" = pb.""REPEM03_Nro_Unico_Area_Prestacion"" AND mr.""REPEM04_Patente"" = pb.""REPEM03_Patente"" AND mr.""REPEM04_FechaHora_Recoleccion"" >= pb.""REPEM03_Fecha_de_entrada"" AND mr.""REPEM04_FechaHora_Recoleccion"" <= pb.""REPEM03_Fecha_de_egreso"";
            ");

            migrationBuilder.Sql(@"
                DROP VIEW public.""View_Reporte_Formato_34"";

                CREATE OR REPLACE VIEW public.""View_Reporte_Formato_34""
                 AS
                 SELECT ********* AS ""NUSD"",
                    pesaje_de_balanza.""REPEM03_Tipo_de_origen"" AS ""OriginType"",
                        CASE
                            WHEN pesaje_de_balanza.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '0'::bigint
                            ELSE pesaje_de_balanza.""REPEM03_Nro_Unico_Area_Prestacion""
                        END AS ""PlaceOriginNumber"",
                    recoleccion_vehicular.""REPEM05_Empresa"" AS ""CompanyName"",
                    pesaje_de_balanza.""REPEM03_Nro_Identificacion_Tributaria"" AS ""NIT"",
                    pesaje_de_balanza.""REPEM03_Municipio"" AS ""DaneCode"",
                    pesaje_de_balanza.""REPEM03_Patente"" AS ""LicensePlate"",
                    pesaje_de_balanza.""REPEM03_Fecha_de_entrada""::date AS ""EntryDate"",
                    pesaje_de_balanza.""REPEM03_Fecha_de_entrada""::time without time zone AS ""EntryTime"",
                    pesaje_de_balanza.""REPEM03_Fecha_de_egreso""::date AS ""EgressDate"",
                    pesaje_de_balanza.""REPEM03_Fecha_de_egreso""::time without time zone AS ""EgressTime"",
                    ROUND(CAST(recoleccion_vehicular.""REPEM05_Toneladas"" AS NUMERIC), 3) AS ""Tons""
                   FROM ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pesaje_de_balanza
                     JOIN ""Reporting-Emvarias_05-Recoleccion_Vehicular"" recoleccion_vehicular ON pesaje_de_balanza.""REPEM03_Nro_Unico_Area_Prestacion"" = recoleccion_vehicular.""REPEM05_NUAP"" AND pesaje_de_balanza.""REPEM03_Patente"" = recoleccion_vehicular.""REPEM05_Patente"" AND recoleccion_vehicular.""REPEM05_FechaHora_Recoleccion"" >= pesaje_de_balanza.""REPEM03_Fecha_de_entrada"" AND recoleccion_vehicular.""REPEM05_FechaHora_Recoleccion"" <= pesaje_de_balanza.""REPEM03_Fecha_de_egreso"";
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
        }
    }
}