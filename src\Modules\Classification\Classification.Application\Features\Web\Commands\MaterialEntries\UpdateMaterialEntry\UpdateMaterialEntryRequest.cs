﻿using Classification.Application.Features.Web.Commands.MaterialEntries.UpdateMaterialEntry.DTOs;
using Classification.Domain.Constants;
using System.Text.Json.Serialization;

namespace Classification.Application.Features.Web.Commands.MaterialEntries.UpdateMaterialEntry;

public record UpdateMaterialEntryRequest : IRequest
{
	[JsonIgnore]
	public int Id {  get; set; }

	[JsonPropertyName("origin")]
	public string OriginType { get; set; }

	[JsonPropertyName("detailsToUpdate")]
	public IEnumerable<MaterialEntryDetailDto> DetailsToUpdate { get; set; }

	[JsonPropertyName("sacksToUpdate")]
	public IEnumerable<UpdateSackDto> SacksToUpdate { get; set; }

	[JsonPropertyName("sacksToAdd")]
	public IEnumerable<CreateSackDto> SacksToAdd { get; set; }
	
	[JsonPropertyName("sackIdsToBeRemoved")]
	public IEnumerable<int> SackIdsToBeRemoved { get; set; }
}