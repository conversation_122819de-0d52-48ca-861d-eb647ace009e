﻿using Common.Domain;
using Common.Domain.Repositories;
using Orion.SharedKernel.Domain.Services;
using Orion.SharedKernel.Infrastructure.Data;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;

namespace Common.Infrastructure.Data.EFCore;

public class CommonUnitOfWork : UnitOfWork<CommonDbContext>, ICommonUnitOfWork
{
    public CommonUnitOfWork(IMaterialTypeRepository materialTypeRepository, IErrorService errorService, ILogEventMessage logEventMessage,
        IDbContextProvider<CommonDbContext> dbContextProvider) : base(dbContextProvider)
    {
        MaterialTypeRepository = materialTypeRepository;
        ErrorService = errorService;
        LogEventMessage = logEventMessage;
    }

    public IMaterialTypeRepository MaterialTypeRepository { get; }
    public IErrorService ErrorService { get; }
    public ILogEventMessage LogEventMessage { get; }
}