﻿using Classification.Application.DTOs.MaterialEntry;
using Classification.Domain.Entities;
using Common.Application.Services.Http.Orion;
using Common.Domain.Entities.Http.Orion.Request;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Application.Exceptions;

namespace Classification.Application.Features.Web.Commands.MaterialEntries.CreateMaterialEntry;

internal sealed class CreateMaterialEntryHandler : MappingService, IRequestHandler<CreateMaterialEntryRequest, CreateMaterialEntryResponse>
{
    private readonly IClassificationUnitOfWork _classificationUnitOfWork;
    private readonly IOrionApiService _orionApiService;

    public CreateMaterialEntryHandler(IMapper mapper, 
        IClassificationUnitOfWork classificationUnitOfWork, 
        IOrionApiService orionApiService
        ) : base(mapper)
    {
        _classificationUnitOfWork = classificationUnitOfWork;
        _orionApiService = orionApiService;
    }

    public async Task<CreateMaterialEntryResponse> Handle(CreateMaterialEntryRequest request, CancellationToken cancellationToken)
    {
        var materialEntry = Mapper.Map<MaterialEntry>(request.MaterialEntryRequest);
        
        await ValidateMaterialEntry(materialEntry, cancellationToken);

        var entity = await InsertAndRetrieveMaterialEntry(materialEntry, cancellationToken);
        
        var materialEntryDto = Mapper.Map<MaterialEntryResponseDto>(entity);

        return new CreateMaterialEntryResponse(materialEntryDto);
    }

    private async Task<MaterialEntry> InsertAndRetrieveMaterialEntry(MaterialEntry materialEntry, CancellationToken cancellationToken)
    {
        await _classificationUnitOfWork
            .MaterialEntryRepository
            .AddAsync(materialEntry, cancellationToken);
        
        await _classificationUnitOfWork.SaveChangesAsync(cancellationToken);
        
        var result = await _classificationUnitOfWork
            .MaterialEntryRepository
            .GetSingleWithIncludesAsync(
                predicate: me => me.Id == materialEntry.Id,
                cancellationToken: cancellationToken
            );
        
        return result!;
    }

    private async Task ValidateMaterialEntry(MaterialEntry materialEntry, CancellationToken cancellationToken)
    {
        await Task.WhenAll(
            ValidateUniqueness(materialEntry, cancellationToken),
            ValidateSacksPresentations(materialEntry, cancellationToken)
        );
    }

    private async Task ValidateSacksPresentations(MaterialEntry materialEntry, CancellationToken cancellationToken)
    {
        var presentationIds = materialEntry
            .Details
            .SelectMany(detail => detail.Sacks)
            .Select(s => s.PresentationId)
            .ToHashSet();

        var existentPresentationTypes = await _orionApiService.GetPresentationTypes(cancellationToken);

        var nonMatchingIds = presentationIds.Where(pid => existentPresentationTypes.All(dv => dv.Id != pid)).ToList();

        if (nonMatchingIds.Count > 0)
        {
            throw new OrionException(_classificationUnitOfWork
                .ErrorService
                .GenerateError(
                    new NonExistentSacksPresentation(nonMatchingIds))
            );
        }
    }

    private async Task ValidateUniqueness(MaterialEntry materialEntry, CancellationToken cancellationToken)
    {
        var existsMaterialEntry = await _classificationUnitOfWork
            .MaterialEntryRepository
            .GetSingleAsync(me => me.Route == materialEntry.Route 
                                  && me.Date == materialEntry.Date, cancellationToken);

        if (existsMaterialEntry is not null)
            throw new OrionException(_classificationUnitOfWork
                .ErrorService
                .GenerateError(
                    new MaterialEntryAlreadyExists(materialEntry.Route, materialEntry.Date))
            );
    }
}