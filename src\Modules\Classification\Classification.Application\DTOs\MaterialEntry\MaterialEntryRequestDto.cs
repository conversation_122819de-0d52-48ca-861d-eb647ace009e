﻿using System.Text.Json.Serialization;

namespace Classification.Application.DTOs.MaterialEntry;

public class MaterialEntryRequestDto
{
    /// <summary>
    /// Fecha de entrada de material.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD HH:MM:SS</remarks>
    /// <example>2024-07-14 21:02:18</example>
    [JsonPropertyName("entryDate")]
    public string? EntryDate { get; set; }
    
    /// <summary>
    /// Responsable del ingreso del registro.
    /// </summary>
    /// <remarks>Nombre del usuario que realiza el ingreso del registro.</remarks>
    /// <example>j<PERSON><PERSON><PERSON></example>
    [JsonPropertyName("responsible")]
    public string? Responsible { get; set; }
    
    /// <summary>
    /// Placa del vehículo.
    /// </summary>
    /// <remarks>Mínimo 5 carácteres alfanuméricos</remarks>
    /// <example>GKV295</example>
    [JsonPropertyName("licensePlate")]
    public string? LicensePlate { get; set; }
    
    /// <summary>
    /// Ruta del vehículo.
    /// </summary>
    /// <remarks>Identificador de la ruta que recorre el vehículo.</remarks>
    /// <example>A204K12</example>
    [JsonPropertyName("route")]
    public string? Route { get; set; }
    
    /// <summary>
    /// Origen del ingreso.
    /// </summary>
    /// <remarks>Identificador del origen del ingreso.</remarks>
    /// <example>Web</example>
    [JsonPropertyName("origin")]
    public string? Origin { get; set; }
    
    /// <summary>
    /// Detalles del ingreso.
    /// </summary>
    /// <remarks>Detalles poseer al menos un detalle de ingreso.</remarks>
    /// <example cref="MaterialEntryDetailRequestDto">MaterialEntryDetailRequestDto</example>
    [JsonPropertyName("details")]
    public IEnumerable<MaterialEntryDetailRequestDto> Details { get; set; }
}