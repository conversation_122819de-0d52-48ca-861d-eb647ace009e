using FluentValidation;
using Reports.Domain.Constants;
using Shared.Application.Common.ValidationExtensions;

namespace Reports.Application.Features.Web.Commands.Weighins.CreateWeighing;

public class CreateWeighingValidator : AbstractValidator<CreateWeighingRequest>
{
    public CreateWeighingValidator()
    {
        RuleFor(p => p.<PERSON>)
            .NotEmpty()
            .WithMessage("La lista de pesajes no puede estar vacía");

        RuleForEach(pr => pr.<PERSON>)
            .ChildRules(pc =>
            {
                pc.RuleFor(p => p.Id)
                    .NotEmpty()
                    .WithMessage("El id del pesaje no puede estar vacío")
                    .MaximumLength(10)
                    .WithMessage("El id del pesaje no puede tener más de 10 caracteres");
                
                pc.RuleFor(p => p.LicensePlate)
                    .NotEmpty()
                    .WithMessage("La placa del vehiculo no puede estar vacía")
                    .MinimumLength(5)
                    .WithMessage("La placa del vehiculo no puede tener menos de 5 caracteres");
                
                pc.RuleFor(p => p.NIT)
                    .NotEmpty()
                    .WithMessage("El NIT no puede estar vacío");
                
                pc.RuleFor(p => p.NUAP)
                    .NotEmpty()
                    .WithMessage("El NUAP no puede estar vacío");

                pc.RuleFor(p => p.ArrivingWeight)
                    .Minimun(0)
                    .WithMessage("El peso de llegada no puede ser menor a 0");

                pc.RuleFor(p => p.LeavingWeight)
                    .Minimun(0)
                    .WithMessage("El peso de salida no puede ser menor a 0");

                pc.RuleFor(p => p.TownCode)
                    .MaximumLength(6)
                    .WithMessage("El código de municipio no puede tener más de 6 caracteres")
                    .NotEmpty();
                
                pc.RuleFor(p => p.EntryDate)
                    .NotEmpty()
                    .WithMessage("La fecha de ingreso no puede estar vacía")
                    .IsValidDateWithTime()
                    .WithMessage("La fecha de ingreso debe tener el formato yyyy-MM-dd HH:mm:ss");
                
                pc.RuleFor(p => p.EntryTime)
                    .NotEmpty()
                    .WithMessage("La hora de ingreso no puede estar vacía")
                    .IsValidDateWithTime()
                    .WithMessage("La hora de ingreso debe tener el formato yyyy-MM-dd HH:mm:ss");
                
                pc.RuleFor(p => p.EgressDate)
                    .NotEmpty()
                    .WithMessage("La fecha de salida no puede estar vacía")
                    .IsValidDateWithTime()
                    .WithMessage("La fecha de salida debe tener el formato yyyy-MM-dd HH:mm:ss");
                
                pc.RuleFor(p => p.EgressTime)
                    .NotEmpty()
                    .WithMessage("La hora de salida no puede estar vacía")
                    .IsValidDateWithTime()
                    .WithMessage("La hora de salida debe tener el formato yyyy-MM-dd HH:mm:ss");

                pc.RuleFor(p => p.MaterialType)
                    .NotEmpty()
                    .WithMessage("El código de tipo de material no puede estar vacío");

                pc.RuleFor(p => p.OriginType)
                    .NotEmpty()
                    .WithMessage("El tipo de origen no puede estar vacío")
                    .IsValidEnum(typeof(OriginType))
                    .WithMessage("El tipo de origen no es válido");
                
                pc.RuleFor(p => p.LoadingType)
                    .NotEmpty()
                    .WithMessage("El tipo de carga no puede estar vacío")
                    .IsValidEnum(typeof(LoadingType))
                    .WithMessage("El tipo de carga no es válido");
                
                pc.RuleFor(p => p.LoadingDate)
                    .NotEmpty()
                    .WithMessage("La fecha de carga no puede estar vacía")
                    .IsValidDateWithTime()
                    .WithMessage("La fecha de carga debe tener el formato yyyy-MM-dd HH:mm:ss");
                
                pc.RuleFor(p => p.CancelDate)
                    .IsValidDateWithTimeOrNull()
                    .WithMessage("La fecha de cancelación debe ser nula o tener el formato yyyy-MM-dd HH:mm:ss");
            });
    }
}