﻿using System.Text.Json.Serialization;

namespace Classification.Application.DTOs.MaterialEntry;

public class SackResponseDto
{
    /// <summary>
    /// Identificador del bolson registrado en el detalle.
    /// </summary>
    /// <example>1</example>
    [JsonPropertyName("id")]
    public int Id { get; set; }
    
    /// <summary>
    /// Cantidad representada.
    /// </summary>
    /// <example>18.21</example>
    [JsonPropertyName("quantity")]
    public double? Quantity { get; set; }
    
    /// <summary>
    /// Descripción de la presentación.
    /// </summary>
    /// <example>Tula</example>
    [JsonPropertyName("presentation")]
    public string Presentation { get; set; }
}