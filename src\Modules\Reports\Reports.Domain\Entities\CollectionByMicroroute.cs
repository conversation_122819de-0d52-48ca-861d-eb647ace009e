using Orion.SharedKernel.Domain.Entities;

namespace Reports.Domain.Entities;

public sealed class CollectionByMicroroute : Entity<long>
{
    public string LicensePlate { get; set; }
    public string ServiceStatus { get; set; }
    public string GroupTurn { get; set; }
    public string ServiceType { get; set; }
    public string RouteCode { get; set; }
    public bool IsReinforcement { get; set; }
    public string Intern { get; set; }
    public decimal TotalWeight { get; set; }
    public long ServiceId { get; set; }
    public DateTime? RouteArrivalDate { get; set; }
    public DateTime? RouteDepartureDate { get; set; }
    public string? Observations { get; set; }
    public decimal TotalTonnage { get; set; }
    public DateTime? WeighingDate { get; set; }
    public DateTime? ServicingDate { get; set; }
    public DateTime? ServiceStartDate { get; set; }
    public DateTime? BaseArrivalDate { get; set; }
    public DateTime? BaseDepartureDate { get; set; }
    
    private static decimal CalculateTonnage(decimal totalWeight) => totalWeight / 1000;

    public CollectionByMicroroute() { }
    
    public CollectionByMicroroute(string licensePlate, string serviceStatus, string groupTurn, string serviceType, string routeCode,
        bool isReinforcement, string intern, decimal totalWeight, long serviceId, DateTime? routeArrivalDate, DateTime? routeDepartureDate, string? observations,
        DateTime? weighingDate, DateTime? servicingDate, DateTime? serviceStartDate, DateTime? baseArrivalDate, DateTime? baseDepartureDate)
    {
        LicensePlate = licensePlate;
        ServiceStatus = serviceStatus;
        GroupTurn = groupTurn;
        ServiceType = serviceType;
        RouteCode = routeCode;
        IsReinforcement = isReinforcement;
        Intern = intern;
        TotalWeight = totalWeight;
        ServiceId = serviceId;
        RouteArrivalDate = routeArrivalDate;
        RouteDepartureDate = routeDepartureDate;
        Observations = observations;
        WeighingDate = weighingDate;
        ServicingDate = servicingDate;
        ServiceStartDate = serviceStartDate;
        BaseArrivalDate = baseArrivalDate;
        BaseDepartureDate = baseDepartureDate;
        TotalTonnage = CalculateTonnage(totalWeight);
    }
}