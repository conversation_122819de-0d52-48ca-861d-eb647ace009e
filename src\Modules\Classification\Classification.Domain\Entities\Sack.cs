﻿using Common.Domain.Entities.Http.Orion.Response;
using Orion.SharedKernel.Domain.Entities;
using Orion.SharedKernel.Domain.Entities.Audit.DeletionAudit;

namespace Classification.Domain.Entities;

public class Sack : Entity<int>, ISoftDeletedEntity
{
    public int Quantity { get; set; }
	public bool IsDeleted { get; set; }

    public int PresentationId { get; set; }
    public DomainValue Presentation { get; set; }

    public int MaterialEntryDetailId { get; set; }
    public MaterialEntryDetail MaterialEntryDetail { get; set; }

	public void Update(int quantity)
    {
        Quantity = quantity;
    }
}