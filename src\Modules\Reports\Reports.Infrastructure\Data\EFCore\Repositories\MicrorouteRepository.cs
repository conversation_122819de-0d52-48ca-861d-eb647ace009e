﻿using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;
using Reports.Domain.Entities;
using Reports.Domain.Repositories;

namespace Reports.Infrastructure.Data.EFCore.Repositories;

public class MicrorouteRepository : Repository<MicroRoute, long, ReportsDbContext>, IMicrorouteRepository
{
    public MicrorouteRepository(IDbContextProvider<ReportsDbContext> dbContextProvider, ICacheService cacheService)
        : base(dbContextProvider, cacheService) { }
}