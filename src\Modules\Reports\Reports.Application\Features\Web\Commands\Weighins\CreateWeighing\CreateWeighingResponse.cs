using Reports.Application.DTOs.Weighings;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Application.Features.Web.Commands.Weighins.CreateWeighing;

public class CreateWeighingResponse
{
    public CreateWeighingResponseDto Weighin { get; private set; }

    public CreateWeighingResponse(IEnumerable<WeighingScale> weighinsToInsert, IEnumerable<WeighingScale> weighinsToUpdate,
        IEnumerable<WeighingScale> weighinsToCancel)
    {
        var weighingItemsResponse = new List<CreateWeighingItemResponseDto>();
        
        weighingItemsResponse.AddRange(weighinsToInsert.Select(s => 
            new CreateWeighingItemResponseDto {
                Id = s.Id!,
                Status = (int)OperationCodes.Alta
            }));

        weighingItemsResponse.AddRange(weighinsToUpdate.Select(s =>
            new CreateWeighingItemResponseDto {
                Id = s.Id!,
                Status = (int)OperationCodes.Modificacion
            }));

        weighingItemsResponse.AddRange(weighinsToCancel.Select(s => 
            new CreateWeighingItemResponseDto {
                Id = s.Id!,
                Status = (int)OperationCodes.Anulacion
            }));
        
        Weighin = new CreateWeighingResponseDto
        {
            Items = weighingItemsResponse,
            Total = weighingItemsResponse.Count
        };
    }
}