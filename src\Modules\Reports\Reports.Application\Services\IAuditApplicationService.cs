using Reports.Application.DTOs.Audit;
using Reports.Domain.Constants;

namespace Reports.Application.Services;

/// <summary>
/// Application service for audit operations.
/// Provides a facade for audit-related operations and coordinates between domain services.
/// </summary>
public interface IAuditApplicationService
{
    /// <summary>
    /// Retrieves audit records for a specific entity
    /// </summary>
    /// <param name="entityId">The ID of the entity</param>
    /// <param name="tableName">The table/entity name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of audit record DTOs</returns>
    Task<IEnumerable<AuditRecordDto>> GetAuditRecordsForEntityAsync(
        string entityId,
        string tableName,
        CancellationToken cancellationToken);

    /// <summary>
    /// Retrieves audit records by action type with pagination
    /// </summary>
    /// <param name="request">The request parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated audit records</returns>
    Task<PaginatedAuditRecordsDto> GetAuditRecordsByActionTypeAsync(
        GetAuditRecordsByActionTypeRequest request,
        CancellationToken cancellationToken);

    /// <summary>
    /// Retrieves audit records by user with pagination
    /// </summary>
    /// <param name="request">The request parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated audit records</returns>
    Task<PaginatedAuditRecordsDto> GetAuditRecordsByUserAsync(
        GetAuditRecordsByUserRequest request,
        CancellationToken cancellationToken);

    /// <summary>
    /// Checks if an entity has audit records
    /// </summary>
    /// <param name="entityId">The ID of the entity</param>
    /// <param name="tableName">The table/entity name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if audit records exist</returns>
    Task<bool> HasAuditRecordsAsync(
        string entityId,
        string tableName,
        CancellationToken cancellationToken);

    /// <summary>
    /// Gets audit statistics for a table
    /// </summary>
    /// <param name="tableName">The table/entity name</param>
    /// <param name="fromDate">Optional start date filter</param>
    /// <param name="toDate">Optional end date filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Audit statistics</returns>
    Task<AuditStatisticsDto> GetAuditStatisticsAsync(
        string tableName,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Request for getting audit records by action type
/// </summary>
public class GetAuditRecordsByActionTypeRequest
{
    public HistoricalMovementType ActionType { get; set; }
    public string? TableName { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 25;
}

/// <summary>
/// Request for getting audit records by user
/// </summary>
public class GetAuditRecordsByUserRequest
{
    public string User { get; set; } = string.Empty;
    public string? TableName { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 25;
}
