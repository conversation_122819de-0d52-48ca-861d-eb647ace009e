﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Fix_Toneladas_Nulas_Exportacion_F34_Casos_de_Rechazos : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                CREATE OR REPLACE VIEW ""REPEM_Reporte_SUI_Recolecciones_F34""
                AS
                WITH combined_data AS (
                    -- First query (Dep1)
                    SELECT 720105237 AS ""C1_NUSD"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                        ELSE NULL::bigint
                    END AS ""C2_TIPO_ORIGEN"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                        ELSE r14.""C1_NUAP""
                    END AS ""C3_NUSITIO_ORI"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                        ELSE cli.""REPEM08_Nombre_Completo""
                    END AS ""C4_NOMBRE_EMPRESA"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                        ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                    END AS ""C5_NIT_EMPRESA"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                        ELSE mun.""REPEM01_Codigo""
                    END AS ""C6_COD_DANE_ORI"",
                    pb.""REPEM03_Patente"" AS ""C7_PLACA"",
                    pb.""REPEM03_Fecha_de_entrada""::date AS ""C8_FECHA_INGRESO"",
                    pb.""REPEM03_Fecha_de_egreso""::date AS ""C9_FECHA_SALIDA"",
                    pb.""REPEM03_Fecha_de_entrada""::time without time zone AS ""C10_HORA_INGRESO"",
                    pb.""REPEM03_Fecha_de_egreso""::time without time zone AS ""C11_HORA_SALIDA"",
                    (r14.""C9_TON_BARRIDO""::numeric + r14.""C10_TONRESNA"" -
                    COALESCE(r14.""C11_TONRECHAPR"", 0::numeric)::numeric)::numeric AS ""C12_TONELADAS"",
                    pb.""REPEM03_Id"" AS ""CE_ID_TICKET"",
                    COALESCE(tct.""REPEM10_Valor"", 0::numeric) AS ""CE_VALOR_TICKET_LEGACY"",
                    COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                    COALESCE(r14.""C11_TONRECHAPR"", 0::numeric) AS ""CE_TONELADAS_RECHAZADAS"",
                    true AS ""CE_EXISTE_EN_F14"",
                    pb.""REPEM03_Nro_Identificacion_Tributaria"" AS ""CE_NIT_EMPRESA"",
                    r14.""C1_NUAP"" AS ""CE_NUAP"",
                    r14.""C5_FECHA"" AS ""CE_FECHA_FILTRO""
                    FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                    JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                    ON pb.""REPEM03_Id"" = r14.""CE_ID_TICKET"" AND pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                    LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                    ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                    LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                    ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                    LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                    ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND pb.""REPEM03_Fecha_de_cancelacion"" IS NULL

                    UNION ALL

                    -- Second query (Dep2)
                    SELECT 720105237 AS ""C1_NUSD"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                        ELSE NULL::bigint
                    END AS ""C2_TIPO_ORIGEN"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                        ELSE pb.""REPEM03_Nro_Unico_Area_Prestacion""
                    END AS ""C3_NUSITIO_ORI"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                        ELSE cli.""REPEM08_Nombre_Completo""
                    END AS ""C4_NOMBRE_EMPRESA"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                        ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                    END AS ""C5_NIT_EMPRESA"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                        ELSE mun.""REPEM01_Codigo""
                    END AS ""C6_COD_DANE_ORI"",
                    pb.""REPEM03_Patente"" AS ""C7_PLACA"",
                    pb.""REPEM03_Fecha_de_entrada""::date AS ""C8_FECHA_INGRESO"",
                    pb.""REPEM03_Fecha_de_egreso""::date AS ""C9_FECHA_SALIDA"",
                    pb.""REPEM03_Fecha_de_entrada""::time without time zone AS ""C10_HORA_INGRESO"",
                    pb.""REPEM03_Fecha_de_egreso""::time without time zone AS ""C11_HORA_SALIDA"",
                    tct.""REPEM10_Valor""::numeric / 1000::numeric -
                    COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric) AS ""C12_TONELADAS"",
                    pb.""REPEM03_Id"" AS ""CE_ID_TICKET"",
                    COALESCE(tct.""REPEM10_Valor"", 0::numeric) AS ""CE_VALOR_TICKET_LEGACY"",
                    COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                    COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric) AS ""CE_TONELADAS_RECHAZADAS"",
                    false AS ""CE_EXISTE_EN_F14"",
                    pb.""REPEM03_Nro_Identificacion_Tributaria"" AS ""CE_NIT_EMPRESA"",
                    pb.""REPEM03_Nro_Unico_Area_Prestacion"" AS ""CE_NUAP"",
                    pb.""REPEM03_Fecha_de_entrada""::date AS ""CE_FECHA_FILTRO""
                    FROM ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                    LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                    ON pb.""REPEM03_Id"" = tct.""REPEM10_Id""
                    LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                    ON pb.""REPEM03_Id"" = tre.""Ticket_Asignable"" AND pb.""REPEM03_Nro_Unico_Area_Prestacion"" = 440405001
                    LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                    ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                    LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                    ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                    WHERE pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                        and pb.""REPEM03_Id"" NOT IN (
                            SELECT DISTINCT
                                        rpm.""REPEM04_Id"" AS id
                                    FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                    JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                        ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                        )


                    UNION ALL

                    -- Third query (Dep3)
                    SELECT 720105237 AS ""C1_NUSD"",
                    '3'::bigint AS ""C2_TIPO_ORIGEN"",
                    trept.""Num_ECA"" AS ""C3_NUSITIO_ORI"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                        ELSE cli.""REPEM08_Nombre_Completo""
                    END AS ""C4_NOMBRE_EMPRESA"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                        ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                    END AS ""C5_NIT_EMPRESA"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                        ELSE mun.""REPEM01_Codigo""
                    END AS ""C6_COD_DANE_ORI"",
                    pb.""REPEM03_Patente"" AS ""C7_PLACA"",
                    pb.""REPEM03_Fecha_de_entrada""::date AS ""C8_FECHA_INGRESO"",
                    pb.""REPEM03_Fecha_de_egreso""::date AS ""C9_FECHA_SALIDA"",
                    pb.""REPEM03_Fecha_de_entrada""::time without time zone AS ""C10_HORA_INGRESO"",
                    pb.""REPEM03_Fecha_de_egreso""::time without time zone AS ""C11_HORA_SALIDA"",
                    COALESCE((trept.""Toneladas_Rechazadas"")::numeric, 0::numeric) AS ""C12_TONELADAS"",
                    pb.""REPEM03_Id"" AS ""CE_ID_TICKET"",
                    COALESCE(tct.""REPEM10_Valor"", 0::numeric) AS ""CE_VALOR_TICKET_LEGACY"",
                    COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                    0 AS ""CE_TONELADAS_RECHAZADAS"",
                    true AS ""CE_EXISTE_EN_F14"",
                    pb.""REPEM03_Nro_Identificacion_Tributaria"" AS ""CE_NIT_EMPRESA"",
                    440405001 AS ""CE_NUAP"",
                    r14.""C5_FECHA"" AS ""CE_FECHA_FILTRO""
                    FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                    INNER JOIN ""REPEM_Toneladas_Rechazos_Por_Ticket"" trept
                    ON r14.""CE_ID_TICKET"" = trept.""Id_Ticket"" AND trept.""NUAP"" = r14.""C1_NUAP""
                    JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                    ON pb.""REPEM03_Id"" = r14.""CE_ID_TICKET"" AND pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                    LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                    ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                    LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                    ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                    LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                    ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                )
                SELECT 
                    ""C1_NUSD"",
                    ""C2_TIPO_ORIGEN"",
                    ""C3_NUSITIO_ORI"",
                    ""C4_NOMBRE_EMPRESA"",
                    ""C5_NIT_EMPRESA"",
                    ""C6_COD_DANE_ORI"",
                    ""C7_PLACA"",
                    ""C8_FECHA_INGRESO"",
                    ""C9_FECHA_SALIDA"",
                    ""C10_HORA_INGRESO"",
                    ""C11_HORA_SALIDA"",
                    COALESCE(""C12_TONELADAS"", 0::numeric) AS ""C12_TONELADAS"",
                    ""CE_ID_TICKET"",
                    ""CE_VALOR_TICKET_LEGACY"",
                    ""CE_VALOR_TON_TICKET_LEGACY"",
                    ""CE_TONELADAS_RECHAZADAS"",
                    ""CE_EXISTE_EN_F14"",
                    ""CE_NIT_EMPRESA"",
                    ""CE_NUAP"",
                    ""CE_FECHA_FILTRO""
                FROM combined_data;
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                CREATE OR REPLACE VIEW ""REPEM_Reporte_SUI_Recolecciones_F34""
                AS
                WITH combined_data AS (
                    -- First query (Dep1)
                    SELECT 720105237 AS ""C1_NUSD"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                        ELSE NULL::bigint
                    END AS ""C2_TIPO_ORIGEN"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                        ELSE r14.""C1_NUAP""
                    END AS ""C3_NUSITIO_ORI"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                        ELSE cli.""REPEM08_Nombre_Completo""
                    END AS ""C4_NOMBRE_EMPRESA"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                        ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                    END AS ""C5_NIT_EMPRESA"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                        ELSE mun.""REPEM01_Codigo""
                    END AS ""C6_COD_DANE_ORI"",
                    pb.""REPEM03_Patente"" AS ""C7_PLACA"",
                    pb.""REPEM03_Fecha_de_entrada""::date AS ""C8_FECHA_INGRESO"",
                    pb.""REPEM03_Fecha_de_egreso""::date AS ""C9_FECHA_SALIDA"",
                    pb.""REPEM03_Fecha_de_entrada""::time without time zone AS ""C10_HORA_INGRESO"",
                    pb.""REPEM03_Fecha_de_egreso""::time without time zone AS ""C11_HORA_SALIDA"",
                    (r14.""C9_TON_BARRIDO""::numeric + r14.""C10_TONRESNA"" -
                    COALESCE(r14.""C11_TONRECHAPR"", 0::numeric)::numeric)::numeric AS ""C12_TONELADAS"",
                    pb.""REPEM03_Id"" AS ""CE_ID_TICKET"",
                    COALESCE(tct.""REPEM10_Valor"", 0::numeric) AS ""CE_VALOR_TICKET_LEGACY"",
                    COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                    COALESCE(r14.""C11_TONRECHAPR"", 0::numeric) AS ""CE_TONELADAS_RECHAZADAS"",
                    true AS ""CE_EXISTE_EN_F14"",
                    pb.""REPEM03_Nro_Identificacion_Tributaria"" AS ""CE_NIT_EMPRESA"",
                    r14.""C1_NUAP"" AS ""CE_NUAP"",
                    r14.""C5_FECHA"" AS ""CE_FECHA_FILTRO""
                    FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                    JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                    ON pb.""REPEM03_Id"" = r14.""CE_ID_TICKET"" AND pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                    LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                    ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                    LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                    ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                    LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                    ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND pb.""REPEM03_Fecha_de_cancelacion"" IS NULL

                    UNION ALL

                    -- Second query (Dep2)
                    SELECT 720105237 AS ""C1_NUSD"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                        ELSE NULL::bigint
                    END AS ""C2_TIPO_ORIGEN"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                        ELSE pb.""REPEM03_Nro_Unico_Area_Prestacion""
                    END AS ""C3_NUSITIO_ORI"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                        ELSE cli.""REPEM08_Nombre_Completo""
                    END AS ""C4_NOMBRE_EMPRESA"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                        ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                    END AS ""C5_NIT_EMPRESA"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                        ELSE mun.""REPEM01_Codigo""
                    END AS ""C6_COD_DANE_ORI"",
                    pb.""REPEM03_Patente"" AS ""C7_PLACA"",
                    pb.""REPEM03_Fecha_de_entrada""::date AS ""C8_FECHA_INGRESO"",
                    pb.""REPEM03_Fecha_de_egreso""::date AS ""C9_FECHA_SALIDA"",
                    pb.""REPEM03_Fecha_de_entrada""::time without time zone AS ""C10_HORA_INGRESO"",
                    pb.""REPEM03_Fecha_de_egreso""::time without time zone AS ""C11_HORA_SALIDA"",
                    tct.""REPEM10_Valor""::numeric / 1000::numeric -
                    COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric) AS ""C12_TONELADAS"",
                    pb.""REPEM03_Id"" AS ""CE_ID_TICKET"",
                    COALESCE(tct.""REPEM10_Valor"", 0::numeric) AS ""CE_VALOR_TICKET_LEGACY"",
                    COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                    COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric) AS ""CE_TONELADAS_RECHAZADAS"",
                    false AS ""CE_EXISTE_EN_F14"",
                    pb.""REPEM03_Nro_Identificacion_Tributaria"" AS ""CE_NIT_EMPRESA"",
                    pb.""REPEM03_Nro_Unico_Area_Prestacion"" AS ""CE_NUAP"",
                    pb.""REPEM03_Fecha_de_entrada""::date AS ""CE_FECHA_FILTRO""
                    FROM ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                    LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                    ON pb.""REPEM03_Id"" = tct.""REPEM10_Id""
                    LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                    ON pb.""REPEM03_Id"" = tre.""Ticket_Asignable"" AND pb.""REPEM03_Nro_Unico_Area_Prestacion"" = 440405001
                    LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                    ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                    LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                    ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                    WHERE pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                        and pb.""REPEM03_Id"" NOT IN (
                            SELECT DISTINCT
                                        rpm.""REPEM04_Id"" AS id
                                    FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                    JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                        ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                        )


                    UNION ALL

                    -- Third query (Dep3)
                    SELECT 720105237 AS ""C1_NUSD"",
                    '3'::bigint AS ""C2_TIPO_ORIGEN"",
                    trept.""Num_ECA"" AS ""C3_NUSITIO_ORI"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                        ELSE cli.""REPEM08_Nombre_Completo""
                    END AS ""C4_NOMBRE_EMPRESA"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                        ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                    END AS ""C5_NIT_EMPRESA"",
                    CASE
                        WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                        ELSE mun.""REPEM01_Codigo""
                    END AS ""C6_COD_DANE_ORI"",
                    pb.""REPEM03_Patente"" AS ""C7_PLACA"",
                    pb.""REPEM03_Fecha_de_entrada""::date AS ""C8_FECHA_INGRESO"",
                    pb.""REPEM03_Fecha_de_egreso""::date AS ""C9_FECHA_SALIDA"",
                    pb.""REPEM03_Fecha_de_entrada""::time without time zone AS ""C10_HORA_INGRESO"",
                    pb.""REPEM03_Fecha_de_egreso""::time without time zone AS ""C11_HORA_SALIDA"",
                    (trept.""Toneladas_Rechazadas"")::numeric AS ""C12_TONELADAS"",
                    pb.""REPEM03_Id"" AS ""CE_ID_TICKET"",
                    COALESCE(tct.""REPEM10_Valor"", 0::numeric) AS ""CE_VALOR_TICKET_LEGACY"",
                    COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                    0 AS ""CE_TONELADAS_RECHAZADAS"",
                    true AS ""CE_EXISTE_EN_F14"",
                    pb.""REPEM03_Nro_Identificacion_Tributaria"" AS ""CE_NIT_EMPRESA"",
                    440405001 AS ""CE_NUAP"",
                    r14.""C5_FECHA"" AS ""CE_FECHA_FILTRO""
                    FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                    INNER JOIN ""REPEM_Toneladas_Rechazos_Por_Ticket"" trept
                    ON r14.""CE_ID_TICKET"" = trept.""Id_Ticket"" AND trept.""NUAP"" = r14.""C1_NUAP""
                    JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                    ON pb.""REPEM03_Id"" = r14.""CE_ID_TICKET"" AND pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                    LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                    ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                    LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                    ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                    LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                    ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                )
                SELECT *
                FROM combined_data;
            ");
        }
    }
}
