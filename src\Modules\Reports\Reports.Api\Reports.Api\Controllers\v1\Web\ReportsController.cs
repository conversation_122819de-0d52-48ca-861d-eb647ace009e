using System.Net;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Orion.SharedKernel.Api.Controllers;
using Orion.SharedKernel.Domain.Entities.Error;
using Reports.Application.DTOs.Reports;
using Reports.Application.DTOs.Reports.MassBalance;
using Reports.Application.Features.Web.Queries.Reports.ExportCompressedReports;
using Reports.Application.Features.Web.Queries.Reports.ExportDiscounts;
using Reports.Application.Features.Web.Queries.Reports.ExportReport;
using Reports.Application.Features.Web.Queries.Reports.GetReportPreview;
using Reports.Application.Features.Web.Queries.Reports.MassBalance;

namespace Reports.Api.Controllers.v1.Web;

[ApiVersion("1.0")]
[Authorize]
public class ReportsController : OrionController
{
    /// <summary>
    /// Obtiene la vista previa de un reporte.
    /// </summary>
    /// <param name="request">Filtros del reporte a generar.</param>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(List<F14PreviewResponseDto>))]
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(List<F34PreviewResponseDto>))]
    [HttpGet("Preview")]
    public async Task<IActionResult> GetReportPreview([FromQuery] GetReportPreviewRequest request)
    {
        var response = await Mediator.Send(request);

        return Ok(response.ReportPreview);
    }

    /// <summary>
    /// Obtiene el objeto representativo de un reporte de balance de masas
    /// de un periodo determinado
    /// </summary>
    /// <param name="request">Filtros del reporte a generar.</param>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(MassBalanceResponseDto))]
    [ProducesResponseType((int)HttpStatusCode.Conflict, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpGet("MassBalance")]
    public async Task<IActionResult> GetMassBalance([FromQuery] GetMassBalanceRequest request)
    {
        var response = await Mediator.Send(request);

        return Ok(response.MassBalance);
    }

    /// <summary>
    /// Exporta los reportes 14 y 34
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(FileResult))]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError, Type = typeof(Error))]
    [HttpGet("Export")]
    public async Task ExportReport([FromQuery] ReportExportRequest request)
    {
        var response = await Mediator.Send(request);

        await response.Result.ExecuteAsync(HttpContext);
    }
    
    /// <summary>
    /// Genera un ZIP con los reportes formateados según la regulación de la intendencia
    /// de un periodo seleccionado en formato CSV
    /// </summary>
    /// <param name="requestDto"></param>
    /// <returns></returns>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(FileResult))]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError, Type = typeof(Error))]
    [HttpGet("ExportCompressedReports")]
    public async Task ExportCompressedReports([FromQuery] CompressedReportsRequestDto requestDto)
    {
        var request = new ExportCompressedReportsRequest(requestDto.FromDate, requestDto.ToDate);
        
        var response = await Mediator.Send(request);

        await response.Result.ExecuteAsync(HttpContext);
    }
    
    /// <summary>
    /// Exporta un excel o csv con un listado de descuentos de toneladas
    /// por numero de tiquete para un periodo determinado
    /// </summary>
    /// <param name="requestDto"></param>
    /// <returns></returns>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(FileResult))]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError, Type = typeof(Error))]
    [HttpGet("DiscountsReport")]
    public async Task ExportDiscounts([FromQuery] ExportDiscountsRequest request)
    {
        var response = await Mediator.Send(request);

        await response.Result.ExecuteAsync(HttpContext);
    }
}