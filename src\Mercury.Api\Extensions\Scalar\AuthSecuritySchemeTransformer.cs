﻿using Microsoft.AspNetCore.OpenApi;
using Microsoft.OpenApi.Models;

namespace Mercury.Api.Extensions.Scalar;

internal sealed class AuthSecuritySchemeTransformer : IOpenApiDocumentTransformer
{
    private const string BearerAuthDescription =
        @"Por favor, ingrese el token de autenticación obtenido desde la API de Orión.";

    private const string BearerFormat = "JWT";
    private const string BearerScheme = "Bearer";
    private const string BearerHeader = "Authorization";


    public Task TransformAsync(OpenApiDocument document, OpenApiDocumentTransformerContext context,
        CancellationToken cancellationToken)
    {
        document.Components ??= new OpenApiComponents();
        document.Components.SecuritySchemes ??= new Dictionary<string, OpenApiSecurityScheme>();

        document.Components.SecuritySchemes[BearerScheme] = new OpenApiSecurityScheme()
        {
            BearerFormat = BearerFormat,
            Description = BearerAuthDescription,
            Name = BearerHeader,
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.Http,
            Scheme = BearerScheme
        };

        var securityRequirement = new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Id = BearerScheme,
                        Type = ReferenceType.SecurityScheme
                    }
                },
                []
            }
        };

        document.SecurityRequirements.Add(securityRequirement);

        return Task.CompletedTask;
    }
}