﻿using Classification.Domain.Entities;
using Classification.Domain.Repositories;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;

namespace Classification.Infrastructure.Data.EFCore.Repositories;

public class SackRepository : Repository<Sack, int, ClassificationDbContext>, ISackRepository
{
    public SackRepository(IDbContextProvider<ClassificationDbContext> dbContextProvider, ICacheService cacheService) : base(dbContextProvider, cacheService) { }
}