using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class HistoricalAuditColumnsConfiguration : IEntityTypeConfiguration<HistoricalAuditColumns>
{
    public void Configure(EntityTypeBuilder<HistoricalAuditColumns> builder)
    {
        builder.ToTable(ReportsTableNames.HistoricalAuditColumns);

        builder.HasKey(x => x.Id)
            .HasName(HistoricalAuditColumnsColumns.PrimaryKeyConstraintName);

        builder.Property(x => x.Id)
            .HasColumnName(HistoricalAuditColumnsColumns.Id)
            .IsRequired();

        builder.Property(x => x.EntityId)
            .HasColumnName(HistoricalAuditColumnsColumns.EntityId)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(x => x.TableName)
            .HasColumnName(HistoricalAuditColumnsColumns.TableName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(x => x.ActionType)
            .HasColumnName(HistoricalAuditColumnsColumns.ActionType)
            .HasConversion<int>()
            .IsRequired();

        builder.Property(x => x.User)
            .HasColumnName(HistoricalAuditColumnsColumns.User)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(x => x.ActionDate)
            .HasColumnName(HistoricalAuditColumnsColumns.ActionDate)
            .HasColumnType("timestamp without time zone")
            .IsRequired();

        builder.Property(x => x.HistoricalData)
            .HasColumnName(HistoricalAuditColumnsColumns.HistoricalData)
            .HasColumnType("jsonb")
            .IsRequired();

        builder.Property(x => x.Metadata)
            .HasColumnName(HistoricalAuditColumnsColumns.Metadata)
            .HasColumnType("text")
            .IsRequired(false);

        // Indexes for performance
        builder.HasIndex(x => x.EntityId)
            .HasDatabaseName("IX_HistoricalAuditColumns_EntityId");

        builder.HasIndex(x => x.TableName)
            .HasDatabaseName("IX_HistoricalAuditColumns_TableName");

        builder.HasIndex(x => x.ActionDate)
            .HasDatabaseName("IX_HistoricalAuditColumns_ActionDate");

        builder.HasIndex(x => x.User)
            .HasDatabaseName("IX_HistoricalAuditColumns_User");

        // Composite index for common queries
        builder.HasIndex(x => new { x.TableName, x.EntityId, x.ActionDate })
            .HasDatabaseName("IX_HistoricalAuditColumns_TableName_EntityId_ActionDate");
    }
}
