﻿using Classification.Domain;
using Classification.Domain.Repositories;
using Orion.SharedKernel.Domain.Services;
using Orion.SharedKernel.Infrastructure.Data;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;

namespace Classification.Infrastructure.Data.EFCore;

public class ClassificationUnitOfWork : UnitOfWork<ClassificationDbContext>, IClassificationUnitOfWork
{
    public ClassificationUnitOfWork(IDbContextProvider<ClassificationDbContext> dbContextProvider,
        ISackRepository sackRepository,
        IMaterialEntryRepository materialEntryRepository, 
        IMaterialClassificationRepository materialClassificationRepository, 
        IMaterialClassificationDetailRepository materialClassificationDetailRepository,
        IErrorService errorService,
        ILogEventMessage logEventMessage) : base(dbContextProvider)
    {
        SackRepository = sackRepository;
        MaterialEntryRepository = materialEntryRepository;
        MaterialClassificationRepository = materialClassificationRepository;
        MaterialClassificationDetailRepository = materialClassificationDetailRepository;
        ErrorService = errorService;
        LogEventMessage = logEventMessage;
    }
    
    public ISackRepository SackRepository { get; }
    public IMaterialEntryRepository MaterialEntryRepository { get; }
    public IMaterialClassificationRepository MaterialClassificationRepository { get; }
    public IMaterialClassificationDetailRepository MaterialClassificationDetailRepository { get; }
    public IErrorService ErrorService { get; }
    public ILogEventMessage LogEventMessage { get; }
}