﻿using System.Collections.Immutable;
using System.Linq.Expressions;
using Orion.SharedKernel.Domain.Repositories.Entities;
using Reports.Domain.Entities;

namespace Reports.Domain.Repositories;

public interface IWeighingScaleRepository : IRepository<WeighingScale, long>
{
    Task BulkInsertAsync(List<WeighingScale> weighins, CancellationToken cancellationToken);
    
    Task BulkUpdateAsync(List<WeighingScale> weighins, CancellationToken cancellationToken);
}