﻿using Classification.Domain.Entities;
using System.Linq.Expressions;
using Orion.SharedKernel.Application.Exceptions;

using Classification.Application.Common.Errors;

namespace Classification.Application.Features.Web.Commands.MaterialEntries.DeleteMaterialEntry
{
    public class DeleteMaterialEntryHandler : IRequestHandler<DeleteMaterialEntryRequest>
    {
        private readonly IClassificationUnitOfWork _classificationUnitOfWork;

        public DeleteMaterialEntryHandler (IClassificationUnitOfWork classificationUnitOfWork)
        {
            _classificationUnitOfWork = classificationUnitOfWork;
        }

        public async Task<Unit> Handle(DeleteMaterialEntryRequest request, CancellationToken cancellationToken)
        {
            Expression<Func<MaterialEntry, bool>> predicate = e => e.Id.Equals(request.Id) && e.IsDeleted == false;

            MaterialEntry materialEntry = await _classificationUnitOfWork
                .MaterialEntryRepository
                .GetSingleAsync(predicate)
                ?? throw new OrionException(_classificationUnitOfWork.ErrorService.GenerateError(new MaterialEntryNotFound(request.Id)));
            
            materialEntry.Delete();

            _classificationUnitOfWork.MaterialEntryRepository.Update(materialEntry);

            await _classificationUnitOfWork.SaveChangesAsync(cancellationToken);

            return Unit.Value;
        }
    }
}
