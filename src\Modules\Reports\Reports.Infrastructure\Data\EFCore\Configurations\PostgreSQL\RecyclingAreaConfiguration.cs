﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class RecyclingAreaConfiguration : IEntityTypeConfiguration<RecyclingArea>
{
    public void Configure(EntityTypeBuilder<RecyclingArea> builder)
    {
        builder.ToTable(ReportsTableNames.RecyclingArea);

        builder.Ignore(p => p.Id);

        builder
            .Property(p => p.Code)
            .HasColumnName(RecyclingAreaColumns.Code)
            .IsRequired();

        builder
            .Property(p => p.Name)
            .HasMaxLength(20)
            .HasColumnName(RecyclingAreaColumns.Name)
            .IsRequired();
            
        builder
            .HasKey(p => p.Code)
            .HasName(RecyclingAreaColumns.PrimaryKeyConstraintName);
    }
}