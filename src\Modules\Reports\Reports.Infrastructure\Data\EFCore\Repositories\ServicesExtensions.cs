﻿using Microsoft.Extensions.DependencyInjection;
using Reports.Domain;
using Reports.Domain.Repositories;

namespace Reports.Infrastructure.Data.EFCore.Repositories;

public static class ServicesExtensions
{
    public static IServiceCollection AddReportsRepositories(this IServiceCollection services)
    {
        services.AddScoped<IReportsUnitOfWork, ReportsUnitOfWork>();

        services.AddScoped<IWeighingScaleRepository, WeighingScaleRepository>();
        services.AddScoped<IVehicleRetrievalRepository, VehicleRetrievalRepository>();
        services.AddScoped<IRecyclingAreaRepository, RecyclingAreaRepository>();
        services.AddScoped<IDistributionRepository, DistributionRepository>();
        services.AddScoped<ICollectionByMicrorouteRepository, CollectionByMicrorouteRepository>();
        services.AddScoped<ITownRepository, TownRepository>();
        services.AddScoped<ITollRepository, TollRepository>();
        services.AddScoped<IClientRepository, ClientRepository>();
        services.AddScoped<IMicrorouteRepository, MicrorouteRepository>();
        
        services.AddScoped<IReportFormat14Repository, ReportFormat14Repository>();
        services.AddScoped<IReportFormat34Repository, ReportFormat34Repository>();

        return services;
    }
}