using System.Globalization;
using AutoMapper;
using Orion.SharedKernel.Domain.Constants;
using Reports.Application.DTOs.Collections;
using Reports.Application.DTOs.Weighings;
using Reports.Application.Features.Web.Queries.Reports.ExportReport;
using Reports.Application.Features.Web.Queries.Reports.GetReportPreview;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Application.Common.MappingProfiles;

public class RequestToDomainMappingProfile : Profile
{
    private const string Culture = "es-CO";
    private const string DateFormat = "yyyy-MM-dd";
    private const string TimeFormat = "HH:mm:ss";
    private const string DateTimeFormat = $"{DateFormat} {TimeFormat}";

    public RequestToDomainMappingProfile()
    {
        var culture = CultureInfo.CreateSpecificCulture(Culture);

        #region Weighings

        CreateMap<CreateWeighingRequestDto, WeighingScale>()
            .ForMember(p => p.EntryDate,
                opt => opt.MapFrom(
                    src =>
                        DateTimeOffset.ParseExact(src.EntryDate, DateTimeFormat, culture).Date
                        + DateTimeOffset.ParseExact(src.EntryTime, DateTimeFormat, culture).TimeOfDay))
            .ForMember(p => p.EgressDate,
                opt => opt.MapFrom(
                    src =>
                        DateTimeOffset.ParseExact(src.EgressDate, DateTimeFormat, culture).Date
                        + DateTimeOffset.ParseExact(src.EgressTime, DateTimeFormat, culture).TimeOfDay))
            .ForMember(p => p.LoadingType,
                opt => opt.MapFrom(
                    src =>
                        Enum.Parse<LoadingType>(src.LoadingType)))
            .ForMember(p => p.OriginType,
                opt => opt.MapFrom(
                    src =>
                        Enum.Parse<OriginType>(src.OriginType)))
            .AfterMap((e, r) => r.SetTownCode(e.TownCode));

        #endregion Weighings
    }
}