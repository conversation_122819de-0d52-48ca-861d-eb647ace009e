﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class ClientConfiguration : IEntityTypeConfiguration<Client>
{
    public void Configure(EntityTypeBuilder<Client> builder)
    {
        builder.ToTable(ReportsTableNames.Client);
        
        builder
            .Property(p => p.Id)
            .HasColumnName(ClientColumns.NIT)
            .HasMaxLength(10);

        builder
            .Property(p => p.FullName)
            .HasColumnName(ClientColumns.FullName)
            .HasMaxLength(100);
        
        builder
            .<PERSON><PERSON>ey(p => p.Id)
            .HasName(ClientColumns.PrimaryKeyConstraintName);
    }
}