﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Reports.Infrastructure.Data.EFCore;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    [DbContext(typeof(ReportsDbContext))]
    [Migration("20250314150533_Agregar_Toneladas_Rutas_Compartidas_En_Distribuciones")]
    partial class Agregar_Toneladas_Rutas_Compartidas_En_Distribuciones
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.22")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Reports.Domain.Entities.Client", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM08_NIT");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("REPEM08_Nombre_Completo");

                    b.HasKey("Id")
                        .HasName("Reporting-Emvarias_08-Clientes_key");

                    b.ToTable("Reporting-Emvarias_08-Clientes", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.CollectionByMicroroute", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM04_Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime?>("BaseArrivalDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM04_FechaHora_LlegadaBase");

                    b.Property<DateTime?>("BaseDepartureDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM04_FechaHora_SalidaBase");

                    b.Property<string>("GroupTurn")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("REPEM04_GrupoTurno");

                    b.Property<string>("Intern")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("REPEM04_Interno");

                    b.Property<bool>("IsReinforcement")
                        .HasColumnType("boolean")
                        .HasColumnName("REPEM04_EsRefuerzo");

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("character varying(6)")
                        .HasColumnName("REPEM04_Patente");

                    b.Property<string>("Observations")
                        .HasColumnType("text")
                        .HasColumnName("REPEM04_Observaciones");

                    b.Property<DateTime?>("RouteArrivalDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM04_FechaHora_EntradaRuta");

                    b.Property<string>("RouteCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("REPEM04_RutaCodigo");

                    b.Property<DateTime?>("RouteDepartureDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM04_FechaHora_SalidaRuta");

                    b.Property<long>("ServiceId")
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM04_IdServicio");

                    b.Property<DateTime?>("ServiceStartDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM04_FechaHora_InicioServicio");

                    b.Property<string>("ServiceStatus")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("REPEM04_EstadoServicio");

                    b.Property<string>("ServiceType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("REPEM04_TipoServicio");

                    b.Property<DateTime?>("ServicingDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM04_Fecha_de_Servicio");

                    b.Property<decimal>("TotalTonnage")
                        .HasPrecision(18, 2)
                        .HasColumnType("numeric(18,2)")
                        .HasColumnName("REPEM04_PesoTotal_Toneladas");

                    b.Property<decimal>("TotalWeight")
                        .HasPrecision(18, 2)
                        .HasColumnType("numeric(18,2)")
                        .HasColumnName("REPEM04_PesoTotal");

                    b.Property<DateTime?>("WeighingDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM04_FechaHora_Pesaje");

                    b.HasKey("Id")
                        .HasName("Reporting-Emvarias_04-Recoleccion_por_Microruta_key");

                    b.ToTable("Reporting-Emvarias_04-Recoleccion_por_Microruta", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.Distribution", b =>
                {
                    b.Property<decimal>("DeviationTons")
                        .HasColumnType("numeric")
                        .HasColumnName("Toneladas_Desviacion");

                    b.Property<decimal>("DistributedTons")
                        .HasColumnType("numeric")
                        .HasColumnName("Toneladas_Distribuidas");

                    b.Property<decimal>("DistributionTollPercentage")
                        .HasColumnType("numeric")
                        .HasColumnName("Porcentaje_Distribucion_Peaje");

                    b.Property<int>("Month")
                        .HasColumnType("integer")
                        .HasColumnName("Mes");

                    b.Property<int>("NUAP")
                        .HasColumnType("integer")
                        .HasColumnName("NUAP");

                    b.Property<string>("RecyclingArea")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("Area_Aprovechamiento");

                    b.Property<decimal>("ReportedTons")
                        .HasColumnType("numeric")
                        .HasColumnName("Toneladas_Reportadas");

                    b.Property<int>("Trips")
                        .HasColumnType("integer")
                        .HasColumnName("Cantidad_de_Viajes");

                    b.Property<int>("Year")
                        .HasColumnType("integer")
                        .HasColumnName("Año");

                    b.ToView("REPEM_Distribuciones_de_Microrutas");
                });

            modelBuilder.Entity("Reports.Domain.Entities.GeneratedServiceTicket", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM10_Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<decimal>("Weight")
                        .HasPrecision(18, 2)
                        .HasColumnType("numeric(18,2)")
                        .HasColumnName("REPEM10_Valor");

                    b.HasKey("Id")
                        .HasName("Reporting-Emvarias_10-Tickets_de_Servicio_Generados_key");

                    b.ToTable("Reporting-Emvarias_10-Tickets_de_Servicio_Generados", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.MicroRoute", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM07_Numero_de_Microruta");

                    b.Property<long>("NUAP")
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM07_NUAP");

                    b.Property<string>("ExtendedRouteCode")
                        .IsRequired()
                        .HasColumnType("char(7)")
                        .HasColumnName("REPEM07_Ruta_Larga");

                    b.Property<long>("ExternalRouteCode")
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM07_Numero_Ruta_Intendencia");

                    b.Property<int>("NonAforatedPercentage")
                        .HasColumnType("integer")
                        .HasColumnName("REPEM07_Porcentaje_No_Aforado");

                    b.Property<int>("RecyclableTonsPercentage")
                        .HasColumnType("integer")
                        .HasColumnName("REPEM07_Porcentaje_Residuos_Aprovechables");

                    b.Property<int>("SweepingPercentage")
                        .HasColumnType("integer")
                        .HasColumnName("REPEM07_Porcentaje_Barrido");

                    b.Property<int>("UrbanCleaningPercentage")
                        .HasColumnType("integer")
                        .HasColumnName("REPEM07_Porcentaje_Limpieza_Urbana");

                    b.HasKey("Id", "NUAP")
                        .HasName("Reporting-Emvarias_07-Microrutas_key");

                    b.ToTable("Reporting-Emvarias_07-Microrutas", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.RecyclingArea", b =>
                {
                    b.Property<long>("Code")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM02_Codigo");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Code"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("REPEM02_Nombre");

                    b.HasKey("Code")
                        .HasName("Reporting-Emvarias_02-Areas_de_Aprovechamiento_key");

                    b.ToTable("Reporting-Emvarias_02-Areas_de_Aprovechamiento", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.Rejection", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM09_Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<long>("ECA")
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM09_ECA");

                    b.Property<string>("ExtendedRouteCode")
                        .IsRequired()
                        .HasColumnType("char(7)")
                        .HasColumnName("REPEM09_Ruta_Larga");

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("character varying(6)")
                        .HasColumnName("REPEM09_Placa");

                    b.Property<DateOnly>("RejectionDate")
                        .HasColumnType("date")
                        .HasColumnName("REPEM09_Fecha_Corta");

                    b.Property<decimal>("Tonnage")
                        .HasPrecision(18, 10)
                        .HasColumnType("numeric(18,10)")
                        .HasColumnName("REPEM09_Toneladas");

                    b.HasKey("Id")
                        .HasName("Reporting-Emvarias_09-Rechazos_key");

                    b.HasIndex("RejectionDate", "LicensePlate")
                        .HasDatabaseName("REPEM09_Fecha_Corta_Placa_idx")
                        .HasAnnotation("Postgres:IndexMethod", "btree");

                    b.ToTable("Reporting-Emvarias_09-Rechazos", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.ReportFormat14", b =>
                {
                    b.Property<bool?>("CompensationRelation")
                        .HasColumnType("boolean")
                        .HasColumnName("CE_REL_COMPENSACION");

                    b.Property<long?>("CompensationRelationTicketId")
                        .HasColumnType("bigint")
                        .HasColumnName("CE_REL_COMPENSACION_ID_TICKET");

                    b.Property<string>("DestinationCode")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("C3_NUSD");

                    b.Property<int>("DestinationType")
                        .HasColumnType("integer")
                        .HasColumnName("C2_TIPO_SITIO");

                    b.Property<string>("ExtendedRouteCode")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("CE_RUTA_LARGA");

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("C4_PLACA");

                    b.Property<int>("MeasuringUnit")
                        .HasColumnType("integer")
                        .HasColumnName("C13_SISTEMA_MEDICION");

                    b.Property<long>("MicrorouteId")
                        .HasColumnType("bigint")
                        .HasColumnName("C7_NUMICRO");

                    b.Property<long>("NUAP")
                        .HasColumnType("bigint")
                        .HasColumnName("C1_NUAP");

                    b.Property<decimal>("NonRecyclableTons")
                        .HasColumnType("numeric")
                        .HasColumnName("C10_TONRESNA");

                    b.Property<decimal>("RecyclableTons")
                        .HasColumnType("numeric")
                        .HasColumnName("C12_TONRESAPR");

                    b.Property<string>("RecyclingArea")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("CE_NOMBRE_AREA");

                    b.Property<decimal>("RejectedTons")
                        .HasColumnType("numeric")
                        .HasColumnName("C11_TONRECHAPR");

                    b.Property<long?>("ServiceTicketId")
                        .HasColumnType("bigint")
                        .HasColumnName("CE_ID_TICKET");

                    b.Property<decimal>("SweepingTons")
                        .HasColumnType("numeric")
                        .HasColumnName("C9_TON_BARRIDO");

                    b.Property<decimal>("Toll")
                        .HasColumnType("numeric")
                        .HasColumnName("C14_VLRPEAJ");

                    b.Property<decimal>("TotalTons")
                        .HasColumnType("numeric")
                        .HasColumnName("CE_TON_TOTAL");

                    b.Property<decimal>("UrbanCleaningTons")
                        .HasColumnType("numeric")
                        .HasColumnName("C8_TON_LIMP_URB");

                    b.Property<DateOnly>("VehicleArrival")
                        .HasColumnType("date")
                        .HasColumnName("C5_FECHA");

                    b.Property<TimeOnly>("VehicleArrivalTime")
                        .HasColumnType("time without time zone")
                        .HasColumnName("C6_HORA");

                    b.ToView("REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos");
                });

            modelBuilder.Entity("Reports.Domain.Entities.ReportFormat14AditionCLU", b =>
                {
                    b.Property<string>("DestinationCode")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("C3_NUSD");

                    b.Property<int>("DestinationType")
                        .HasColumnType("integer")
                        .HasColumnName("C2_TIPO_SITIO");

                    b.Property<string>("ExtendedRouteCode")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("CE_RUTA_LARGA");

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("C4_PLACA");

                    b.Property<int>("MeasuringUnit")
                        .HasColumnType("integer")
                        .HasColumnName("C13_SISTEMA_MEDICION");

                    b.Property<long>("MicrorouteId")
                        .HasColumnType("bigint")
                        .HasColumnName("C7_NUMICRO");

                    b.Property<long>("NUAP")
                        .HasColumnType("bigint")
                        .HasColumnName("C1_NUAP");

                    b.Property<double>("NonRecyclableTons")
                        .HasColumnType("double precision")
                        .HasColumnName("C10_TONRESNA");

                    b.Property<double>("RecyclableTons")
                        .HasColumnType("double precision")
                        .HasColumnName("C12_TONRESAPR");

                    b.Property<double>("RejectedTons")
                        .HasColumnType("double precision")
                        .HasColumnName("C11_TONRECHAPR");

                    b.Property<double>("SweepingTons")
                        .HasColumnType("double precision")
                        .HasColumnName("C9_TON_BARRIDO");

                    b.Property<double>("Toll")
                        .HasColumnType("double precision")
                        .HasColumnName("C14_VLRPEAJ");

                    b.Property<double>("TotalTons")
                        .HasColumnType("double precision")
                        .HasColumnName("CE_TON_TOTAL");

                    b.Property<double>("UrbanCleaningTons")
                        .HasColumnType("double precision")
                        .HasColumnName("C8_TON_LIMP_URB");

                    b.Property<DateOnly>("VehicleArrival")
                        .HasColumnType("date")
                        .HasColumnName("C5_FECHA");

                    b.Property<TimeOnly>("VehicleArrivalTime")
                        .HasColumnType("time without time zone")
                        .HasColumnName("C6_HORA");

                    b.ToTable("Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.ReportFormat34", b =>
                {
                    b.Property<DateOnly>("ArrivalDate")
                        .HasColumnType("date")
                        .HasColumnName("C8_FECHA_INGRESO");

                    b.Property<TimeOnly>("ArrivalTime")
                        .HasColumnType("time without time zone")
                        .HasColumnName("C10_HORA_INGRESO");

                    b.Property<long>("CompanyNIT")
                        .HasColumnType("bigint")
                        .HasColumnName("CE_NIT_EMPRESA");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text")
                        .HasColumnName("C4_NOMBRE_EMPRESA");

                    b.Property<string>("DaneCode")
                        .HasColumnType("text")
                        .HasColumnName("C6_COD_DANE_ORI");

                    b.Property<DateOnly>("DepartureDate")
                        .HasColumnType("date")
                        .HasColumnName("C9_FECHA_SALIDA");

                    b.Property<TimeOnly>("DepartureTime")
                        .HasColumnType("time without time zone")
                        .HasColumnName("C11_HORA_SALIDA");

                    b.Property<bool>("ExistsInReport14")
                        .HasColumnType("boolean")
                        .HasColumnName("CE_EXISTE_EN_F14");

                    b.Property<DateOnly>("FilteredDate")
                        .HasColumnType("date")
                        .HasColumnName("CE_FECHA_FILTRO");

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("C7_PLACA");

                    b.Property<long?>("NIT")
                        .HasColumnType("bigint")
                        .HasColumnName("C5_NIT_EMPRESA");

                    b.Property<long?>("NUAP")
                        .HasColumnType("bigint")
                        .HasColumnName("CE_NUAP");

                    b.Property<long>("NUSD")
                        .HasColumnType("bigint")
                        .HasColumnName("C1_NUSD");

                    b.Property<int>("OriginType")
                        .HasColumnType("integer")
                        .HasColumnName("C2_TIPO_ORIGEN");

                    b.Property<long?>("PlaceOriginNumber")
                        .HasColumnType("bigint")
                        .HasColumnName("C3_NUSITIO_ORI");

                    b.Property<decimal>("RejectedTonnage")
                        .HasColumnType("numeric")
                        .HasColumnName("CE_TONELADAS_RECHAZADAS");

                    b.Property<long>("ServiceTicketId")
                        .HasColumnType("bigint")
                        .HasColumnName("CE_ID_TICKET");

                    b.Property<decimal>("ServiceTicketTonnage")
                        .HasColumnType("numeric")
                        .HasColumnName("CE_VALOR_TON_TICKET_LEGACY");

                    b.Property<decimal>("ServiceTicketWeight")
                        .HasColumnType("numeric")
                        .HasColumnName("CE_VALOR_TICKET_LEGACY");

                    b.Property<decimal>("Tons")
                        .HasColumnType("numeric")
                        .HasColumnName("C12_TONELADAS");

                    b.ToView("REPEM_Reporte_SUI_Recolecciones_F34");
                });

            modelBuilder.Entity("Reports.Domain.Entities.ReportsFormat14AditionRecyclables", b =>
                {
                    b.Property<string>("DestinationCode")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("C3_NUSD");

                    b.Property<int>("DestinationType")
                        .HasColumnType("integer")
                        .HasColumnName("C2_TIPO_SITIO");

                    b.Property<string>("ExtendedRouteCode")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("CE_RUTA_LARGA");

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("C4_PLACA");

                    b.Property<int>("MeasuringUnit")
                        .HasColumnType("integer")
                        .HasColumnName("C13_SISTEMA_MEDICION");

                    b.Property<long>("MicrorouteId")
                        .HasColumnType("bigint")
                        .HasColumnName("C7_NUMICRO");

                    b.Property<long>("NUAP")
                        .HasColumnType("bigint")
                        .HasColumnName("C1_NUAP");

                    b.Property<double>("NonRecyclableTons")
                        .HasColumnType("double precision")
                        .HasColumnName("C10_TONRESNA");

                    b.Property<double>("RecyclableTons")
                        .HasColumnType("double precision")
                        .HasColumnName("C12_TONRESAPR");

                    b.Property<double>("RejectedTons")
                        .HasColumnType("double precision")
                        .HasColumnName("C11_TONRECHAPR");

                    b.Property<double>("SweepingTons")
                        .HasColumnType("double precision")
                        .HasColumnName("C9_TON_BARRIDO");

                    b.Property<double>("Toll")
                        .HasColumnType("double precision")
                        .HasColumnName("C14_VLRPEAJ");

                    b.Property<double>("TotalTons")
                        .HasColumnType("double precision")
                        .HasColumnName("CE_TON_TOTAL");

                    b.Property<double>("UrbanCleaningTons")
                        .HasColumnType("double precision")
                        .HasColumnName("C8_TON_LIMP_URB");

                    b.Property<DateOnly>("VehicleArrival")
                        .HasColumnType("date")
                        .HasColumnName("C5_FECHA");

                    b.Property<TimeOnly>("VehicleArrivalTime")
                        .HasColumnType("time without time zone")
                        .HasColumnName("C6_HORA");

                    b.ToTable("Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.Toll", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("REPEM06_Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("character varying(6)")
                        .HasColumnName("REPEM06_Placa");

                    b.Property<DateTime>("ValidFromDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM06_Fecha_Validez");

                    b.Property<double>("Value")
                        .HasColumnType("double precision")
                        .HasColumnName("REPEM06_Valor");

                    b.Property<string>("VehicleType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("REPEM06_Codigo_Tipo_Vehiculo");

                    b.HasKey("Id")
                        .HasName("Reporting-Emvarias_06-Peajes_key");

                    b.ToTable("Reporting-Emvarias_06-Peajes", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.Town", b =>
                {
                    b.Property<string>("Code")
                        .HasMaxLength(5)
                        .HasColumnType("character varying(5)")
                        .HasColumnName("REPEM01_Codigo");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("REPEM01_Departamento");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("REPEM01_Nombre");

                    b.Property<string>("Province")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("REPEM01_Provincia");

                    b.HasKey("Code")
                        .HasName("Reporting-Emvarias_01-Municipios_key");

                    b.ToTable("Reporting-Emvarias_01-Municipios", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.VehicleRetrieval", b =>
                {
                    b.Property<long>("NUAP")
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM05_NUAP");

                    b.Property<int>("Year")
                        .HasColumnType("integer")
                        .HasColumnName("REPEM05_Año");

                    b.Property<int>("Month")
                        .HasColumnType("integer")
                        .HasColumnName("REPEM05_Mes");

                    b.Property<decimal>("Tons")
                        .HasColumnType("numeric")
                        .HasColumnName("REPEM05_Toneladas");

                    b.HasKey("NUAP", "Year", "Month")
                        .HasName("Reporting-Emvarias_05-Recoleccion_Vehicular_key");

                    b.ToTable("Reporting-Emvarias_05-Recoleccion_Vehicular", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.WeighingScale", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM03_Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<int>("ArrivingWeight")
                        .HasPrecision(18, 2)
                        .HasColumnType("integer")
                        .HasColumnName("REPEM03_Peso_de_entrada");

                    b.Property<DateTime?>("CancelDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM03_Fecha_de_cancelacion");

                    b.Property<long>("DepositPlace")
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM03_Lugar_de_deposito");

                    b.Property<DateTime>("EgressDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM03_Fecha_de_egreso");

                    b.Property<DateTime>("EntryDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM03_Fecha_de_entrada");

                    b.Property<int>("LeavingWeight")
                        .HasPrecision(18, 2)
                        .HasColumnType("integer")
                        .HasColumnName("REPEM03_Peso_de_salida");

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("character varying(6)")
                        .HasColumnName("REPEM03_Patente");

                    b.Property<DateTime>("LoadingDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM03_Fecha_de_carga");

                    b.Property<string>("LoadingType")
                        .IsRequired()
                        .HasColumnType("character(1)")
                        .HasColumnName("REPEM03_Tipo_de_carga");

                    b.Property<string>("MaterialType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("REPEM03_Tipo_de_material");

                    b.Property<long>("NIT")
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM03_Nro_Identificacion_Tributaria");

                    b.Property<long>("NUAP")
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM03_Nro_Unico_Area_Prestacion");

                    b.Property<string>("OriginType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("REPEM03_Tipo_de_origen");

                    b.Property<string>("TownCode")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("character varying(5)")
                        .HasColumnName("REPEM03_Municipio");

                    b.HasKey("Id")
                        .HasName("Reporting-Emvarias_03-Pesaje_de_Balanza_key");

                    b.HasIndex("TownCode");

                    b.ToTable("Reporting-Emvarias_03-Pesaje_de_Balanza", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.WeighingScale", b =>
                {
                    b.HasOne("Reports.Domain.Entities.Town", "Town")
                        .WithMany()
                        .HasForeignKey("TownCode")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("Reporting-Emvarias_REPEM03-REPEM01_fkey");

                    b.Navigation("Town");
                });
#pragma warning restore 612, 618
        }
    }
}
