﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Common.Infrastructure.Data.EFCore.Migrations
{
    public partial class Remove_DDV : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "Recycling-ECA_RECYECA01-OrionDomainValue_fkey",
                table: "Recycling-ECA-01_Materiales");

            migrationBuilder.DropIndex(
                name: "IX_Recycling-ECA-01_Materiales_RECYECA01_Grupo",
                table: "Recycling-ECA-01_Materiales");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_Recycling-ECA-01_Materiales_RECYECA01_Grupo",
                table: "Recycling-ECA-01_Materiales",
                column: "RECYECA01_Grupo");

            migrationBuilder.AddForeign<PERSON>ey(
                name: "Recycling-ECA_RECYECA01-OrionDomainValue_fkey",
                table: "Recycling-ECA-01_Materiales",
                column: "RECYECA01_Grupo",
                principalTable: "Orion-DomainValue",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
