﻿using Orion.SharedKernel.Domain.Entities.Http;
using System.ComponentModel.DataAnnotations;

namespace Common.Domain.Entities.Http.Orion.Response
{
    public class DomainValue : SerializableModel
    {
        public int Id { get; set; }
        public string Value { get; set; } = null!;
        public string? Code { get; set; }
        public string? Description { get; set; }
        public int? Order { get; set; }
        public int? ParentDomainValueId { get; set; }
        public int DomainId { get; set; }
    }
}
