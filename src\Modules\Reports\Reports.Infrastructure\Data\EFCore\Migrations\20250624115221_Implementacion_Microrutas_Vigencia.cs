﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Implementacion_Microrutas_Vigencia : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            DropDependantViews(migrationBuilder);
            
            RecreateViewsWithChanges(migrationBuilder);
            
            RecreateMaterializedViewsWithChangesAndSchedule(migrationBuilder);
        }
        
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            DropDependantViews(migrationBuilder);
            
            RecreateViewsWithoutChanges(migrationBuilder);
            
            RecreateMaterializedViewsWithChangesAndSchedule(migrationBuilder);
        }
        
        private static void DropDependantViews(MigrationBuilder migrationBuilder)
        {
            //Se borra el scheduled job
            migrationBuilder.Sql(@"
                 SELECT cron.unschedule('JOB_REPEM_Reportes_SUI_F14_F34');
            ");
            
            //Se borran las vistas materializadas
            migrationBuilder.Sql(@"
                drop materialized view if exists ""REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos"";
                drop materialized view if exists ""REPEM_Reporte_SUI_Recolecciones_F34"";
            ");
            
            //Se borran TODAS las vistas normales
            migrationBuilder.Sql(@"
                drop view if exists public.""REPEM_Sumatoria_Pesos_Rutas_Compartidas"" cascade;
                drop view if exists public.""REPEM_Distribuciones_de_Microrutas"" cascade;
                drop view if exists public.""REPEM_Dependencia_Toneladas_A_Compensar"" cascade;
                drop view if exists public.""REPEM_Detalle_Compensacion_Tickets"" cascade;
                drop view if exists public.""REPEM_Tickets_Compensables"" cascade;
                drop view if exists public.""REPEM_Tickets_Compensables_Por_Area"" cascade;
                drop view if exists public.""REPEM_Compensaciones_por_Redondeo"" cascade;
                drop view if exists public.""REPEM_Descuento_Por_Ticket"" cascade;
                drop view if exists public.""REPEM_Toneladas_Descuento_Excepcional"" cascade;
                drop view if exists public.""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" cascade;
                drop view if exists public.""REPEM_Descuento_de_Barrido_Itagui_Por_Ticket"" cascade;
                drop view if exists public.""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" cascade;
                drop view if exists public.""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"" cascade;
                drop view if exists public.""REPEM_Reporte_SUI_Recolecciones_F14"" cascade;
                drop view if exists public.""REPEM_Toneladas_Rechazos_Por_Ticket"" cascade;
            ");
        }
        
        private static void RecreateViewsWithoutChanges(MigrationBuilder migrationBuilder)
        {
            //Recreate Non-materialized Views
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Toneladas_Descuento_Excepcional""
                            (""Año"", ""Mes"", ""Rut_LMV"", ""Rut_MJS"", ""Nro_LMV"", ""Nro_MJS"", ""Nro_Domingo"", ""Total_Dias"", ""Klmts_LMV"",
                             ""Klmts_MJS"", ""Klmts_Total"", ""Densidad"", ""Kilos_Total_Descuento"", ""Toneladas_Total_Descuento"")
                as
                SELECT a.""Año"",
                       a.""Mes"",
                       f.""Rut_LMV"",
                       f.""Rut_MJS"",
                       f.""Nro_LMV"",
                       f.""Nro_MJS"",
                       f.""Nro_Domingo"",
                       f.""Total_Dias"",
                       (f.""Rut_LMV"" * f.""Nro_LMV""::numeric)::numeric(18, 3)                                                        AS ""Klmts_LMV"",
                       (f.""Rut_MJS"" * f.""Nro_MJS""::numeric)::numeric(18, 3)                                                        AS ""Klmts_MJS"",
                       (f.""Rut_LMV"" * f.""Nro_LMV""::numeric)::numeric(18, 3) +
                       (f.""Rut_MJS"" * f.""Nro_MJS""::numeric)::numeric(18, 3)                                                        AS ""Klmts_Total"",
                       40::numeric                                                                                                 AS ""Densidad"",
                       (((f.""Rut_LMV"" * f.""Nro_LMV""::numeric)::numeric(18, 3) + (f.""Rut_MJS"" * f.""Nro_MJS""::numeric)::numeric(18, 3)) *
                        40::numeric)::numeric(18, 3)                                                                               AS ""Kilos_Total_Descuento"",
                       ((((f.""Rut_LMV"" * f.""Nro_LMV""::numeric)::numeric(18, 3) + (f.""Rut_MJS"" * f.""Nro_MJS""::numeric)::numeric(18, 3)) *
                         40::numeric)::numeric(18, 3) /
                        1000::numeric)::numeric(18, 3)                                                                             AS ""Toneladas_Total_Descuento""
                FROM (SELECT EXTRACT(year FROM
                                     ""Reporting-Emvarias_04-Recoleccion_por_Microruta"".""REPEM04_FechaHora_Pesaje"")::integer AS ""Año"",
                             EXTRACT(month FROM
                                     ""Reporting-Emvarias_04-Recoleccion_por_Microruta"".""REPEM04_FechaHora_Pesaje"")::integer AS ""Mes""
                      FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta""
                      GROUP BY (EXTRACT(year FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"".""REPEM04_FechaHora_Pesaje"")),
                               (EXTRACT(month FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"".""REPEM04_FechaHora_Pesaje""))) a
                         LEFT JOIN LATERAL ( SELECT 4.00             AS ""Rut_LMV"",
                                                    4.02             AS ""Rut_MJS"",
                                                    sum(
                                                            CASE
                                                                WHEN EXTRACT(dow FROM final_subquery.date) = ANY
                                                                     (ARRAY [0::numeric, 2::numeric, 4::numeric]) THEN 1
                                                                ELSE 0
                                                                END) AS ""Nro_LMV"",
                                                    sum(
                                                            CASE
                                                                WHEN EXTRACT(dow FROM final_subquery.date) = ANY
                                                                     (ARRAY [1::numeric, 3::numeric, 5::numeric]) THEN 1
                                                                ELSE 0
                                                                END) AS ""Nro_MJS"",
                                                    sum(
                                                            CASE
                                                                WHEN EXTRACT(dow FROM final_subquery.date) = 6::numeric THEN 1
                                                                ELSE 0
                                                                END) AS ""Nro_Domingo"",
                                                    count(*)         AS ""Total_Dias""
                                             FROM (SELECT date.date::date AS date
                                                   FROM generate_series(date_trunc('month'::text,
                                                                                   make_date(a.""Año"", a.""Mes"", 1)::timestamp with time zone),
                                                                        date_trunc('month'::text,
                                                                                   make_date(a.""Año"", a.""Mes"", 1)::timestamp with time zone) +
                                                                        '1 mon -1 days'::interval,
                                                                        '1 day'::interval) date(date)) final_subquery) f ON true;
        
                create or replace view ""REPEM_Sumatoria_Pesos_Rutas_Compartidas""(""Año"", ""Mes"", ""Suma_Pesos_M3"", ""Suma_Pesos_Toneladas"") as
                SELECT ""Año"",
                       ""Mes"",
                       sum(""PesoTotal"")                 AS ""Suma_Pesos_M3"",
                       sum(""PesoTotal"") / 1000::numeric AS ""Suma_Pesos_Toneladas""
                FROM (SELECT rpm.""REPEM04_Id""                                   AS ""IdTicket"",
                             EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")  AS ""Año"",
                             EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") AS ""Mes"",
                             rpm.""REPEM04_PesoTotal""                            AS ""PesoTotal""
                      FROM ""Reporting-Emvarias_07-Microrutas"" mr
                               JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                    ON mr.""REPEM07_Numero_de_Microruta""::text = rpm.""REPEM04_RutaCodigo""::text
                      WHERE mr.""REPEM07_NUAP"" <> 440405001
                        AND mr.""REPEM07_Ruta_Larga"" <> '0614001'::bpchar
                      GROUP BY rpm.""REPEM04_Id"", (EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")),
                               (EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje""))) subquery
                GROUP BY ""Año"", ""Mes""
                ORDER BY ""Año"", ""Mes"";
        
                create or replace view ""REPEM_Distribuciones_de_Microrutas""
                            (""Año"", ""Mes"", ""NUAP"", ""Area_Aprovechamiento"", ""Cantidad_de_Viajes"", ""Toneladas_Reportadas"",
                             ""Toneladas_Distribuidas"", ""Toneladas_Desviacion"", ""Porcentaje_Distribucion_Peaje"",
                             ""Toneladas_Rutas_Compartidas"")
                as
                SELECT rv.""REPEM05_Año""                                                                       AS ""Año"",
                       rv.""REPEM05_Mes""                                                                       AS ""Mes"",
                       ap.""REPEM02_Codigo""                                                                    AS ""NUAP"",
                       ap.""REPEM02_Nombre""                                                                    AS ""Area_Aprovechamiento"",
                       count(*)                                                                               AS ""Cantidad_de_Viajes"",
                       rv.""REPEM05_Toneladas""                                                                 AS ""Toneladas_Reportadas"",
                       round(round((rv.""REPEM05_Toneladas"" / count(*)::numeric),
                          4), 3)::numeric(18, 3)                                                             AS ""Toneladas_Distribuidas"",
                       round(round(
                            (rv.""REPEM05_Toneladas"" / count(*)::numeric - round(round((rv.""REPEM05_Toneladas"" / count(*)::numeric), 4), 3)::numeric(18, 3)
                          ) * count(*)::numeric,
                    4),3)::numeric(18, 3)                                                             AS ""Toneladas_Desviacion"",
                       (rv.""REPEM05_Toneladas"" / src.""Suma_Pesos_Toneladas"" * 100::numeric)::numeric(18, 5)   AS ""Porcentaje_Distribucion_Peaje"",
                       src.""Suma_Pesos_Toneladas""                                                             AS ""Toneladas_Rutas_Compartidas""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm2
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic2
                              ON rpm2.""REPEM04_RutaCodigo""::text = mic2.""REPEM07_Numero_de_Microruta""::character varying(16)::text
                         JOIN ""Reporting-Emvarias_05-Recoleccion_Vehicular"" rv
                              ON rv.""REPEM05_Año""::numeric = EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND
                                 rv.""REPEM05_Mes""::numeric = EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND
                                 rv.""REPEM05_NUAP"" = mic2.""REPEM07_NUAP""
                         JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic2.""REPEM07_NUAP""
                         JOIN ""REPEM_Sumatoria_Pesos_Rutas_Compartidas"" src
                              ON src.""Año"" = EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND
                                 src.""Mes"" = EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"")
                WHERE mic2.""REPEM07_Porcentaje_No_Aforado""::numeric = 0::numeric
                  AND ap.""REPEM02_Codigo"" <> 440405001
                  AND NOT (mic2.""REPEM07_NUAP"" = 441005380 AND mic2.""REPEM07_Ruta_Larga"" = '0911504'::bpchar)
                GROUP BY rv.""REPEM05_Toneladas"", src.""Suma_Pesos_Toneladas"", ap.""REPEM02_Nombre"", rv.""REPEM05_Año"", rv.""REPEM05_Mes"",
                         ap.""REPEM02_Codigo""
                ORDER BY rv.""REPEM05_Año"", rv.""REPEM05_Mes"", ap.""REPEM02_Nombre"";
        
                create or replace view ""REPEM_Dependencia_Toneladas_A_Compensar""
                            (""Id_Ticket"", ""NUAP"", ""Numero_de_Microruta"", ""Numero_Ruta_Intendencia"", ""Patente"", ""Toneladas_Distribuidas"",
                             ""PesoTotal_Toneladas"", ""Porcentaje_No_Aforado"", ""Porcentaje_Limpieza_Urbana"", ""Porcentaje_Barrido"",
                             ""Porcentaje_Residuos_Aprovechables"", ""Fecha_Hora_Pesaje"", ""Sumatoria_Acumulada"")
                as
                SELECT rpm.""REPEM04_Id""                                                                                                   AS ""Id_Ticket"",
                       mic.""REPEM07_NUAP""                                                                                                 AS ""NUAP"",
                       mic.""REPEM07_Numero_de_Microruta""                                                                                  AS ""Numero_de_Microruta"",
                       mic.""REPEM07_Numero_Ruta_Intendencia""                                                                              AS ""Numero_Ruta_Intendencia"",
                       rpm.""REPEM04_Patente""                                                                                              AS ""Patente"",
                       dpm.""Toneladas_Distribuidas"",
                       rpm.""REPEM04_PesoTotal_Toneladas""                                                                                  AS ""PesoTotal_Toneladas"",
                       mic.""REPEM07_Porcentaje_No_Aforado""                                                                                AS ""Porcentaje_No_Aforado"",
                       mic.""REPEM07_Porcentaje_Limpieza_Urbana""                                                                           AS ""Porcentaje_Limpieza_Urbana"",
                       mic.""REPEM07_Porcentaje_Barrido""                                                                                   AS ""Porcentaje_Barrido"",
                       mic.""REPEM07_Porcentaje_Residuos_Aprovechables""                                                                    AS ""Porcentaje_Residuos_Aprovechables"",
                       rpm.""REPEM04_FechaHora_Pesaje""                                                                                     AS ""Fecha_Hora_Pesaje"",
                       sum(dpm.""Toneladas_Distribuidas"")
                       OVER (PARTITION BY rpm.""REPEM04_Id"" ORDER BY mic.""REPEM07_NUAP"" RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS ""Sumatoria_Acumulada""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                              ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                         JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                         JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND dpm.""Año""::numeric =
                                                                                                              EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                          dpm.""Mes""::numeric =
                                                                          EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"");
        
                create or replace view ""REPEM_Detalle_Compensacion_Tickets""
                            (""Id_Ticket"", ""NUAP"", ""Fecha_Hora_Pesaje"", ""Toneladas_Resultantes"", ""Tipo_Compensacion"") as
                WITH cte AS (SELECT dtc.""Id_Ticket"",
                                    dtc.""NUAP"",
                                    dtc.""Numero_de_Microruta"",
                                    dtc.""Numero_Ruta_Intendencia"",
                                    dtc.""Patente"",
                                    dtc.""Toneladas_Distribuidas"",
                                    dtc.""PesoTotal_Toneladas"",
                                    dtc.""Porcentaje_No_Aforado"",
                                    dtc.""Porcentaje_Limpieza_Urbana"",
                                    dtc.""Porcentaje_Barrido"",
                                    dtc.""Porcentaje_Residuos_Aprovechables"",
                                    dtc.""Fecha_Hora_Pesaje"",
                                    dtc.""Sumatoria_Acumulada"",
                                    CASE
                                        WHEN dtc.""Sumatoria_Acumulada"" < dtc.""PesoTotal_Toneladas"" THEN 0::numeric
                                        ELSE dtc.""Sumatoria_Acumulada"" - dtc.""PesoTotal_Toneladas""::numeric
                                        END AS ""Exceso_Acumulado""
                             FROM ""REPEM_Dependencia_Toneladas_A_Compensar"" dtc),
                     cnt AS (SELECT cte.""Id_Ticket"",
                                    count(cte.""Id_Ticket"") AS ""Cantidad""
                             FROM cte
                             GROUP BY cte.""Id_Ticket""),
                     rst AS (SELECT DISTINCT ON (cte.""Id_Ticket"") cte.""Id_Ticket"",
                                                                  cte.""PesoTotal_Toneladas"" -
                                                                  max(cte.""Sumatoria_Acumulada"") OVER (PARTITION BY cte.""Id_Ticket"") AS ""Resto_Disponible""
                             FROM cte
                                      JOIN cnt ON cnt.""Id_Ticket"" = cte.""Id_Ticket""
                             WHERE cte.""Exceso_Acumulado"" = 0::numeric
                               AND cnt.""Cantidad"" > 1
                             ORDER BY cte.""Id_Ticket"", cte.""Toneladas_Distribuidas"" DESC),
                     sprst AS (SELECT DISTINCT ON (cte.""Id_Ticket"") cte.""Id_Ticket"",
                                                                    cte.""Toneladas_Distribuidas"" - cte.""PesoTotal_Toneladas""::numeric AS ""Resto_Disponible""
                               FROM cte
                                        JOIN cnt ON cnt.""Id_Ticket"" = cte.""Id_Ticket""
                               WHERE cte.""Exceso_Acumulado"" > 0::numeric
                                 AND cnt.""Cantidad"" = 1
                               ORDER BY cte.""Id_Ticket"", cte.""Toneladas_Distribuidas"" DESC),
                     pcomp AS (SELECT cte.""Id_Ticket"",
                                      cte.""NUAP"",
                                      cte.""Numero_de_Microruta"",
                                      cte.""Numero_Ruta_Intendencia"",
                                      cte.""Patente"",
                                      cte.""Toneladas_Distribuidas"",
                                      cte.""PesoTotal_Toneladas"",
                                      cte.""Porcentaje_No_Aforado"",
                                      cte.""Porcentaje_Limpieza_Urbana"",
                                      cte.""Porcentaje_Barrido"",
                                      cte.""Porcentaje_Residuos_Aprovechables"",
                                      cte.""Fecha_Hora_Pesaje"",
                                      cte.""Sumatoria_Acumulada"",
                                      cte.""Exceso_Acumulado"",
                                      cte.""Toneladas_Distribuidas"" - rst.""Resto_Disponible"" AS ""Toneladas_Resultantes""
                               FROM cte
                                        JOIN (SELECT icte.""Id_Ticket"",
                                                     max(icte.""Toneladas_Distribuidas"") AS max_toneladas
                                              FROM cte icte
                                              GROUP BY icte.""Id_Ticket"") sub
                                             ON cte.""Id_Ticket"" = sub.""Id_Ticket"" AND cte.""Toneladas_Distribuidas"" = sub.max_toneladas
                                        JOIN rst ON rst.""Id_Ticket"" = cte.""Id_Ticket""
                               WHERE cte.""Exceso_Acumulado"" > 0::numeric
                               UNION ALL
                               SELECT cte.""Id_Ticket"",
                                      cte.""NUAP"",
                                      cte.""Numero_de_Microruta"",
                                      cte.""Numero_Ruta_Intendencia"",
                                      cte.""Patente"",
                                      cte.""Toneladas_Distribuidas"",
                                      cte.""PesoTotal_Toneladas"",
                                      cte.""Porcentaje_No_Aforado"",
                                      cte.""Porcentaje_Limpieza_Urbana"",
                                      cte.""Porcentaje_Barrido"",
                                      cte.""Porcentaje_Residuos_Aprovechables"",
                                      cte.""Fecha_Hora_Pesaje"",
                                      cte.""Sumatoria_Acumulada"",
                                      cte.""Exceso_Acumulado"",
                                      sprst.""Resto_Disponible"" AS ""Toneladas_Resultantes""
                               FROM cte
                                        JOIN sprst ON sprst.""Id_Ticket"" = cte.""Id_Ticket"")
                SELECT cte.""Id_Ticket"",
                       cte.""NUAP"",
                       cte.""Fecha_Hora_Pesaje"",
                       cte.""Toneladas_Distribuidas"" + COALESCE(pcomp.""Toneladas_Resultantes"", 0::numeric) AS ""Toneladas_Resultantes"",
                       CASE
                           WHEN cte.""Exceso_Acumulado"" = 0::numeric OR pcomp.* IS NOT NULL THEN 'ORIGINAL'::text
                           ELSE 'TOTAL'::text
                           END                                                                            AS ""Tipo_Compensacion""
                FROM cte
                         LEFT JOIN pcomp ON pcomp.""Id_Ticket"" = cte.""Id_Ticket"" AND cte.""NUAP"" = pcomp.""NUAP""
                WHERE pcomp.* IS NULL
                UNION ALL
                SELECT pcomp.""Id_Ticket"",
                       pcomp.""NUAP"",
                       pcomp.""Fecha_Hora_Pesaje"",
                       pcomp.""Toneladas_Distribuidas"" AS ""Toneladas_Resultantes"",
                       'TOTAL'::text                  AS ""Tipo_Compensacion""
                FROM pcomp;
        
                create or replace view public.""REPEM_Tickets_Compensables""
                            (""Id_Ticket"", ""Suma_Agrupacion_Toneladas_Por_Compensar"", ""Nro_Ticket_Compensacion"",
                             ""Maximo_Toneladas_Compensables"", ""Fecha_Pesaje"")
                as
                WITH RECURSIVE
                    tickets_to_compensate AS (SELECT td.""Id_Ticket"",
                                                     td.""Fecha_Hora_Pesaje""::date                                                                                                                                                      AS ""Fecha_Pesaje"",
                                                     sum(td.""Toneladas_Resultantes"")                                                                                                                                                   AS ""Suma_Toneladas_Agrupadas"",
                                                     EXTRACT(year FROM td.""Fecha_Hora_Pesaje""::date)::integer                                                                                                                          AS year,
                                                     EXTRACT(month FROM td.""Fecha_Hora_Pesaje""::date)::integer                                                                                                                         AS month,
                                                     row_number()
                                                     OVER (PARTITION BY (EXTRACT(year FROM td.""Fecha_Hora_Pesaje""::date)), (EXTRACT(month FROM td.""Fecha_Hora_Pesaje""::date)) ORDER BY (td.""Fecha_Hora_Pesaje""::date), td.""Id_Ticket"") AS demand_seq,
                                                     array_agg(td.""NUAP"") AS ""Areas_Por_Compensar""
                                              FROM ""REPEM_Detalle_Compensacion_Tickets"" td
                                              WHERE td.""Tipo_Compensacion"" = ANY (ARRAY ['PARCIAL'::text, 'TOTAL'::text])
                                              GROUP BY (td.""Fecha_Hora_Pesaje""::date), td.""Id_Ticket""),
                    compensation_posibilities AS (SELECT rpm2.""REPEM04_Id"",
                                                         rpm2.""REPEM04_FechaHora_Pesaje""::date                                                                                                                                            AS ""RPM_Fecha_Pesaje"",
                                                         rpm2.""REPEM04_PesoTotal_Toneladas"",
                                                         EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"")::integer                                                                                                                      AS year,
                                                         EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"")::integer                                                                                                                     AS month,
                                                         row_number()
                                                         OVER (PARTITION BY (EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"")), (EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"")) ORDER BY rpm2.""REPEM04_PesoTotal_Toneladas"" DESC) AS supply_seq
                                                  FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm2
                                                  WHERE (rpm2.""REPEM04_RutaCodigo""::text = ANY
                                                         (ARRAY ['615618'::text, '618219'::text, '618119'::text, '618319'::text]))
                                                    AND rpm2.""REPEM04_PesoTotal_Toneladas"" > 0::numeric),
                    ticket_assignment(year, month, assigned_demand_seq, assigned_supply_seq, current_supply_id, current_supply_max_tons,
                                      remaining_capacity) AS (SELECT d_1.year,
                                                                     d_1.month,
                                                                     d_1.demand_seq,
                                                                     s.supply_seq,
                                                                     s.""REPEM04_Id"",
                                                                     s.""REPEM04_PesoTotal_Toneladas"",
                                                                     s.""REPEM04_PesoTotal_Toneladas"" - d_1.""Suma_Toneladas_Agrupadas"" AS remaining_capacity
                                                              FROM tickets_to_compensate d_1
                                                                       JOIN compensation_posibilities s ON d_1.year = s.year AND d_1.month = s.month
                                                              WHERE d_1.demand_seq = 1
                                                                AND s.supply_seq = 1
                                                              UNION ALL
                                                              SELECT prev.year,
                                                                     prev.month,
                                                                     curr_d.demand_seq,
                                                                     CASE
                                                                         WHEN prev.remaining_capacity >= curr_d.""Suma_Toneladas_Agrupadas""
                                                                             THEN prev.assigned_supply_seq
                                                                         ELSE prev.assigned_supply_seq + 1
                                                                         END AS assigned_supply_seq,
                                                                     CASE
                                                                         WHEN prev.remaining_capacity >= curr_d.""Suma_Toneladas_Agrupadas""
                                                                             THEN prev.current_supply_id
                                                                         ELSE next_s.""REPEM04_Id""
                                                                         END AS current_supply_id,
                                                                     CASE
                                                                         WHEN prev.remaining_capacity >= curr_d.""Suma_Toneladas_Agrupadas""
                                                                             THEN prev.current_supply_max_tons
                                                                         ELSE next_s.""REPEM04_PesoTotal_Toneladas""
                                                                         END AS current_supply_max_tons,
                                                                     CASE
                                                                         WHEN prev.remaining_capacity >= curr_d.""Suma_Toneladas_Agrupadas""
                                                                             THEN prev.remaining_capacity - curr_d.""Suma_Toneladas_Agrupadas""
                                                                         ELSE next_s.""REPEM04_PesoTotal_Toneladas"" -
                                                                              curr_d.""Suma_Toneladas_Agrupadas""
                                                                         END AS remaining_capacity
                                                              FROM ticket_assignment prev
                                                                       JOIN tickets_to_compensate curr_d
                                                                            ON prev.year = curr_d.year AND prev.month = curr_d.month AND
                                                                               (prev.assigned_demand_seq + 1) = curr_d.demand_seq
                                                                       LEFT JOIN compensation_posibilities next_s
                                                                                 ON prev.year = next_s.year AND
                                                                                    prev.month = next_s.month AND
                                                                                    (prev.assigned_supply_seq + 1) = next_s.supply_seq
                                                              WHERE prev.remaining_capacity >= curr_d.""Suma_Toneladas_Agrupadas""
                                                                 OR prev.remaining_capacity < curr_d.""Suma_Toneladas_Agrupadas"" AND
                                                                    next_s.supply_seq IS NOT NULL)
                SELECT d.""Id_Ticket"",
                       d.""Suma_Toneladas_Agrupadas"" AS ""Suma_Agrupacion_Toneladas_Por_Compensar"",
                       ga.current_supply_id         AS ""Nro_Ticket_Compensacion"",
                       ga.current_supply_max_tons   AS ""Maximo_Toneladas_Compensables"",
                       d.""Fecha_Pesaje"",
                       d.""Areas_Por_Compensar""
                FROM ticket_assignment ga
                         JOIN tickets_to_compensate d
                              ON ga.year = d.year AND ga.month = d.month AND ga.assigned_demand_seq = d.demand_seq
                ORDER BY d.year, d.month, d.demand_seq;
        
                create or replace view ""REPEM_Descuento_Por_Ticket""
                            (""Id_Ticket"", ""Toneladas_Descuento_Medellin"", ""Peaje_Descuento_Medellin"") as
                SELECT rpm.""REPEM04_Id""          AS ""Id_Ticket"",
                       sum(
                               CASE
                                   WHEN mic.""REPEM07_NUAP"" = 440405001 THEN 0::numeric
                                   WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                       THEN mic.""REPEM07_Porcentaje_No_Aforado""::numeric / 100::numeric *
                                            rpm.""REPEM04_PesoTotal_Toneladas""::numeric
                                   ELSE td.""Toneladas_Resultantes""
                                   END)          AS ""Toneladas_Descuento_Medellin"",
                       sum(
                               CASE
                                   WHEN mic.""REPEM07_NUAP"" = 440405001 THEN 0::numeric
                                   WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                       THEN
                                       (mic.""REPEM07_Porcentaje_No_Aforado""::numeric / 100::numeric * 2::numeric) *
                                       pe.""REPEM06_Valor""
                                   ELSE pe.""REPEM06_Valor"" * 2::numeric *
                                        (dpm.""Porcentaje_Distribucion_Peaje"" / 100::numeric)::numeric
                                   END)::numeric AS ""Peaje_Descuento_Medellin""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                              ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                         JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND dpm.""Año""::numeric =
                                                                                                              EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                          dpm.""Mes""::numeric =
                                                                          EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                         JOIN ""REPEM_Detalle_Compensacion_Tickets"" td
                              ON td.""Id_Ticket"" = rpm.""REPEM04_Id"" AND td.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                 td.""Tipo_Compensacion"" = 'ORIGINAL'::text
                         LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe ON pe.""REPEM06_Placa""::text = rpm.""REPEM04_Patente""::text AND
                                                                        pe.""REPEM06_Fecha_Validez"" = ((SELECT p.""REPEM06_Fecha_Validez""
                                                                                                       FROM ""Reporting-Emvarias_06-Peajes"" p
                                                                                                       WHERE p.""REPEM06_Placa""::text = rpm.""REPEM04_Patente""::text
                                                                                                       ORDER BY (abs(EXTRACT(epoch FROM
                                                                                                                             rpm.""REPEM04_FechaHora_Pesaje"" -
                                                                                                                             p.""REPEM06_Fecha_Validez"")))
                                                                                                       LIMIT 1))
                WHERE mic.""REPEM07_NUAP"" <> '440405001'::bigint
                GROUP BY rpm.""REPEM04_Id"";
        
                create or replace view ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket""
                            (""Ticket_Asignable"", ""Toneladas_Rechazadas"", ""Cantidad_Rechazos"", ""Fecha_Rechazo"") as
                SELECT rpm.""REPEM04_Id""                           AS ""Ticket_Asignable"",
                       sum(r.""REPEM09_Toneladas"")::numeric(18, 3) AS ""Toneladas_Rechazadas"",
                       count(r.""REPEM09_Id"")                      AS ""Cantidad_Rechazos"",
                       max(r.""REPEM09_Fecha_Corta"")               AS ""Fecha_Rechazo""
                FROM ""Reporting-Emvarias_09-Rechazos"" r
                         LEFT JOIN LATERAL ( SELECT rpm_1.""REPEM04_Id"",
                                                    rpm_1.""REPEM04_Patente"",
                                                    rpm_1.""REPEM04_EstadoServicio"",
                                                    rpm_1.""REPEM04_GrupoTurno"",
                                                    rpm_1.""REPEM04_TipoServicio"",
                                                    rpm_1.""REPEM04_RutaCodigo"",
                                                    rpm_1.""REPEM04_EsRefuerzo"",
                                                    rpm_1.""REPEM04_Interno"",
                                                    rpm_1.""REPEM04_PesoTotal"",
                                                    rpm_1.""REPEM04_IdServicio"",
                                                    rpm_1.""REPEM04_FechaHora_EntradaRuta"",
                                                    rpm_1.""REPEM04_FechaHora_SalidaRuta"",
                                                    rpm_1.""REPEM04_Observaciones"",
                                                    rpm_1.""REPEM04_PesoTotal_Toneladas"",
                                                    rpm_1.""REPEM04_FechaHora_Pesaje"",
                                                    rpm_1.""REPEM04_Fecha_de_Servicio"",
                                                    rpm_1.""REPEM04_FechaHora_InicioServicio"",
                                                    rpm_1.""REPEM04_FechaHora_LlegadaBase"",
                                                    rpm_1.""REPEM04_FechaHora_SalidaBase"",
                                                    mic.""REPEM07_Numero_de_Microruta"",
                                                    mic.""REPEM07_NUAP"",
                                                    mic.""REPEM07_Numero_Ruta_Intendencia"",
                                                    mic.""REPEM07_Porcentaje_Barrido"",
                                                    mic.""REPEM07_Porcentaje_Limpieza_Urbana"",
                                                    mic.""REPEM07_Porcentaje_No_Aforado"",
                                                    mic.""REPEM07_Porcentaje_Residuos_Aprovechables"",
                                                    mic.""REPEM07_Ruta_Larga""
                                             FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm_1
                                                      JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                           ON rpm_1.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                             WHERE r.""REPEM09_Placa""::text = rpm_1.""REPEM04_Patente""::text
                                               AND r.""REPEM09_Fecha_Corta"" = rpm_1.""REPEM04_FechaHora_SalidaBase""::date
                                               AND mic.""REPEM07_Ruta_Larga"" = r.""REPEM09_Ruta_Larga""
                                             LIMIT 1) rpm ON true
                GROUP BY rpm.""REPEM04_Id"";
        
                create or replace view ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket""
                            (""NroTicket"", ""NUAP"", ""RutaCodigo"", ""Toneladas_Descuento"", ""Año"", ""Mes"") as
                SELECT max(rpm.""REPEM04_Id"")                              AS ""NroTicket"",
                       440705360                                          AS ""NUAP"",
                       614001                                             AS ""RutaCodigo"",
                       tde.""Toneladas_Total_Descuento""                    AS ""Toneladas_Descuento"",
                       EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")  AS ""Año"",
                       EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") AS ""Mes""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                              ON rpm.""REPEM04_RutaCodigo""::bigint = mic.""REPEM07_Numero_de_Microruta""
                         JOIN ""REPEM_Toneladas_Descuento_Excepcional"" tde
                              ON EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") = tde.""Año""::numeric AND
                                 EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") = tde.""Mes""::numeric
                         LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND dpm.""Año""::numeric =
                                                                                                                   EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                               dpm.""Mes""::numeric =
                                                                               EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                         LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                WHERE (rpm.""REPEM04_RutaCodigo""::text <> ALL
                       (ARRAY ['N9911111'::character varying::text, 'N0000133'::character varying::text, 'T9911111'::character varying::text, 'T0000133'::character varying::text, '3RECDON0312201F2'::character varying::text]))
                  AND mic.""REPEM07_Numero_de_Microruta"" = 614001
                  AND mic.""REPEM07_NUAP"" = 440705360
                  AND (
                          CASE
                              WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                  THEN tr.""Toneladas_Descuento_Medellin""
                              ELSE dpm.""Toneladas_Distribuidas""
                              END -
                          CASE
                              WHEN mic.""REPEM07_Porcentaje_Barrido"" <> 0 THEN
                                  CASE
                                      WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas"" -
                                                                               COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                      WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                          THEN tr.""Toneladas_Descuento_Medellin""
                                      ELSE dpm.""Toneladas_Distribuidas""
                                      END * (mic.""REPEM07_Porcentaje_Barrido"" / 100)::numeric
                              ELSE 0::numeric
                              END) >= tde.""Toneladas_Total_Descuento""
                GROUP BY (EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")), (EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")),
                         tde.""Toneladas_Total_Descuento"";
        
                create or replace view ""REPEM_Descuento_de_Barrido_Itagui_Por_Ticket""
                            (""Id_Ticket"", ""Id_Ticket_A_Descontar"", ""Valor_A_Descontar"", ""Valor_Descontable"", ""Fecha_Pesaje"",
                             ""Fecha_Pesaje_A_Descontar"", ""Numero_de_Microruta"", ""Numero_de_Microruta_A_Descontar"")
                as
                WITH cte AS (SELECT rpm.""REPEM04_Id""                                                          AS ""Id_Ticket"",
                                    mic.""REPEM07_NUAP""                                                        AS ""NUAP"",
                                    mic.""REPEM07_Numero_de_Microruta""                                         AS ""Numero_de_Microruta"",
                                    mic.""REPEM07_Ruta_Larga""                                                  AS ""Ruta_Larga"",
                                    rpm.""REPEM04_FechaHora_Pesaje""                                            AS ""FechaHora_Pesaje"",
                                    CASE
                                        WHEN mic.""REPEM07_Porcentaje_Barrido""::numeric <> 0::numeric THEN (
                                            CASE
                                                WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas"" -
                                                                                         COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                    THEN tr.""Toneladas_Descuento_Medellin""
                                                ELSE tdha.""Toneladas_Resultantes""
                                                END * (mic.""REPEM07_Porcentaje_Barrido""::numeric / 100::numeric))::numeric(18, 3)
                                        ELSE 0::numeric
                                        END::numeric(18, 3) + COALESCE(tde.""Toneladas_Descuento"", 0::numeric) AS ""Calculo_Barrido""
                             FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                      JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                           ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::character varying(20)::text
                                      LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                            dpm.""Año""::numeric =
                                                                                            EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                            dpm.""Mes""::numeric =
                                                                                            EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                      LEFT JOIN ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" tde
                                                ON rpm.""REPEM04_Id"" = tde.""NroTicket"" AND
                                                   EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") = tde.""Año"" AND
                                                   EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") = tde.""Mes"" AND
                                                   mic.""REPEM07_Numero_de_Microruta"" = tde.""RutaCodigo"" AND
                                                   mic.""REPEM07_NUAP"" = tde.""NUAP""
                                      LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                                      LEFT JOIN ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                                ON tdha.""Id_Ticket"" = rpm.""REPEM04_Id"" AND tdha.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                   tdha.""Tipo_Compensacion"" = 'ORIGINAL'::text)
                SELECT cte.""Id_Ticket"",
                       cte2.""Id_Ticket""           AS ""Id_Ticket_A_Descontar"",
                       cte.""Calculo_Barrido""      AS ""Valor_A_Descontar"",
                       cte2.""Calculo_Barrido""     AS ""Valor_Descontable"",
                       cte.""FechaHora_Pesaje""     AS ""Fecha_Pesaje"",
                       cte2.""FechaHora_Pesaje""    AS ""Fecha_Pesaje_A_Descontar"",
                       cte.""Numero_de_Microruta"",
                       cte2.""Numero_de_Microruta"" AS ""Numero_de_Microruta_A_Descontar""
                FROM cte
                         JOIN LATERAL ( SELECT cte2_1.""Id_Ticket"",
                                               cte2_1.""NUAP"",
                                               cte2_1.""Numero_de_Microruta"",
                                               cte2_1.""Ruta_Larga"",
                                               cte2_1.""FechaHora_Pesaje"",
                                               cte2_1.""Calculo_Barrido""
                                        FROM cte cte2_1
                                        WHERE cte2_1.""Numero_de_Microruta"" = 690121
                                          AND cte2_1.""Calculo_Barrido"" > cte.""Calculo_Barrido""
                                          AND EXTRACT(year FROM cte.""FechaHora_Pesaje"") = EXTRACT(year FROM cte2_1.""FechaHora_Pesaje"")
                                          AND EXTRACT(month FROM cte.""FechaHora_Pesaje"") = EXTRACT(month FROM cte2_1.""FechaHora_Pesaje"")
                                        ORDER BY cte2_1.""FechaHora_Pesaje"", cte2_1.""Calculo_Barrido"" DESC
                                        LIMIT 1) cte2 ON true
                WHERE cte.""NUAP"" = 440705360
                  AND cte.""Calculo_Barrido"" > 0::numeric;
        
                create or replace view ""REPEM_Toneladas_Rechazos_Por_Ticket""
                            (""Id_Ticket"", ""NUAP"", ""Toneladas_Rechazadas"", ""Fecha_Rechazo"", ""Num_ECA"") as
                SELECT rpm.""REPEM04_Id""                      AS ""Id_Ticket"",
                       rpm.""REPEM07_NUAP""                    AS ""NUAP"",
                       r.""REPEM09_Toneladas""::numeric(18, 3) AS ""Toneladas_Rechazadas"",
                       r.""REPEM09_Fecha_Corta""               AS ""Fecha_Rechazo"",
                       r.""REPEM09_ECA""                       AS ""Num_ECA""
                FROM ""Reporting-Emvarias_09-Rechazos"" r
                         JOIN LATERAL ( SELECT irpm.""REPEM04_Id"",
                                               irpm.""REPEM04_Patente"",
                                               irpm.""REPEM04_EstadoServicio"",
                                               irpm.""REPEM04_GrupoTurno"",
                                               irpm.""REPEM04_TipoServicio"",
                                               irpm.""REPEM04_RutaCodigo"",
                                               irpm.""REPEM04_EsRefuerzo"",
                                               irpm.""REPEM04_Interno"",
                                               irpm.""REPEM04_PesoTotal"",
                                               irpm.""REPEM04_IdServicio"",
                                               irpm.""REPEM04_FechaHora_EntradaRuta"",
                                               irpm.""REPEM04_FechaHora_SalidaRuta"",
                                               irpm.""REPEM04_Observaciones"",
                                               irpm.""REPEM04_PesoTotal_Toneladas"",
                                               irpm.""REPEM04_FechaHora_Pesaje"",
                                               irpm.""REPEM04_Fecha_de_Servicio"",
                                               irpm.""REPEM04_FechaHora_InicioServicio"",
                                               irpm.""REPEM04_FechaHora_LlegadaBase"",
                                               irpm.""REPEM04_FechaHora_SalidaBase"",
                                               mic.""REPEM07_Numero_de_Microruta"",
                                               mic.""REPEM07_NUAP"",
                                               mic.""REPEM07_Numero_Ruta_Intendencia"",
                                               mic.""REPEM07_Ruta_Larga"",
                                               mic.""REPEM07_Porcentaje_Limpieza_Urbana"",
                                               mic.""REPEM07_Porcentaje_Barrido"",
                                               mic.""REPEM07_Porcentaje_No_Aforado"",
                                               mic.""REPEM07_Porcentaje_Residuos_Aprovechables""
                                        FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" irpm
                                                 JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                      ON irpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                        WHERE r.""REPEM09_Placa""::text = irpm.""REPEM04_Patente""::text
                                          AND r.""REPEM09_Fecha_Corta"" = irpm.""REPEM04_FechaHora_SalidaBase""::date
                                          AND mic.""REPEM07_Ruta_Larga"" = r.""REPEM09_Ruta_Larga""
                                        LIMIT 1) rpm ON true;
        
                create or replace view ""REPEM_Compensaciones_por_Redondeo""
                            (""Nro_Ticket_Ajustable"", ""Nro_Ticket_Ajustable_Original"", ""Fecha_Pesaje"", ""NUAP"", ""Area_Aprovechamiento"",
                             ""Peso_Ticket"", ""Toneladas_Ajuste"", ""Mes"", ""Año"")
                as
                WITH cte AS (WITH exclusions AS (WITH variables AS (SELECT 0.5 AS margen_tolerancia)
                                                 SELECT tc.""Nro_Ticket_Compensacion"",
                                                        sum(tc.""Suma_Agrupacion_Toneladas_Por_Compensar"") AS ""Suma_Agrupacion_Toneladas_Por_Compensar"",
                                                        max(tc.""Maximo_Toneladas_Compensables"")           AS ""Maximo_Toneladas_Compensables"",
                                                        (sum(tc.""Suma_Agrupacion_Toneladas_Por_Compensar"") + v.margen_tolerancia) <
                                                        max(tc.""Maximo_Toneladas_Compensables"")           AS ""Valido""
                                                 FROM ""REPEM_Tickets_Compensables"" tc,
                                                      variables v
                                                 GROUP BY tc.""Nro_Ticket_Compensacion"", v.margen_tolerancia),
                                  posibilities AS (SELECT rpm.""REPEM04_Id""               AS ""Nro_Ticket_Ajustable"",
                                                          rpm.""REPEM04_FechaHora_Pesaje"" AS ""Fecha_Pesaje"",
                                                          dct.""NUAP"",
                                                          dct.""Toneladas_Resultantes""    AS ""Peso_Ticket"",
                                                          rpm.""REPEM04_PesoTotal_Toneladas"",
                                                          dpm.""Toneladas_Desviacion""     AS ""Toneladas_Ajuste"",
                                                          ap.""REPEM02_Nombre""            AS ""Area_Aprovechamiento""
                                                   FROM ""REPEM_Detalle_Compensacion_Tickets"" dct
                                                            JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                                                 ON dct.""Id_Ticket"" = rpm.""REPEM04_Id""
                                                            JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                                 ON pb.""REPEM03_Id"" = rpm.""REPEM04_Id""
                                                            JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                                                 ON ap.""REPEM02_Codigo"" = dct.""NUAP""
                                                            JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = dct.""NUAP"" AND
                                                                                                             dpm.""Año""::numeric =
                                                                                                             EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                             dpm.""Mes""::numeric =
                                                                                                             EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                             dct.""Toneladas_Resultantes"" >=
                                                                                                             dpm.""Toneladas_Desviacion""
                                                   WHERE pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                                     AND NOT (rpm.""REPEM04_Id"" IN (SELECT exclusions.""Nro_Ticket_Compensacion""
                                                                                   FROM exclusions
                                                                                   WHERE exclusions.""Valido"" = false))
                                                     AND rpm.""REPEM04_RutaCodigo""::text <> '614001'::text),
                                  ranked_posibilities AS (SELECT p.""Nro_Ticket_Ajustable"",
                                                                 p.""Fecha_Pesaje"",
                                                                 p.""NUAP"",
                                                                 p.""Peso_Ticket"",
                                                                 p.""REPEM04_PesoTotal_Toneladas"",
                                                                 p.""Toneladas_Ajuste"",
                                                                 p.""Area_Aprovechamiento"",
                                                                 row_number()
                                                                 OVER (PARTITION BY p.""Toneladas_Ajuste"", p.""Nro_Ticket_Ajustable"" ORDER BY p.""REPEM04_PesoTotal_Toneladas"" DESC) AS rn
                                                          FROM posibilities p
                                                          ORDER BY p.""REPEM04_PesoTotal_Toneladas"" DESC)
                             SELECT DISTINCT ON (rp.""NUAP"", (EXTRACT(year FROM rp.""Fecha_Pesaje"")), (EXTRACT(month FROM rp.""Fecha_Pesaje""))) COALESCE(tcpa.""Nro_Ticket_Compensacion"", rp.""Nro_Ticket_Ajustable"") AS ""Nro_Ticket_Ajustable"",
                                                                                                                                             CASE
                                                                                                                                                 WHEN tcpa.""Nro_Ticket_Compensacion"" IS NOT NULL
                                                                                                                                                     THEN rp.""Nro_Ticket_Ajustable""
                                                                                                                                                 ELSE NULL::bigint
                                                                                                                                                 END                                                             AS ""Nro_Ticket_Ajustable_Original"",
                                                                                                                                             rp.""Fecha_Pesaje"",
                                                                                                                                             EXTRACT(year FROM rp.""Fecha_Pesaje"")                                AS ""Año"",
                                                                                                                                             EXTRACT(month FROM rp.""Fecha_Pesaje"")                               AS ""Mes"",
                                                                                                                                             rp.""NUAP"",
                                                                                                                                             rp.""Peso_Ticket"",
                                                                                                                                             rp.""REPEM04_PesoTotal_Toneladas"",
                                                                                                                                             rp.""Toneladas_Ajuste"",
                                                                                                                                             rp.""Area_Aprovechamiento""
                             FROM ranked_posibilities rp
                                      LEFT JOIN ""REPEM_Tickets_Compensables"" tcpa
                                                ON tcpa.""Id_Ticket"" = rp.""Nro_Ticket_Ajustable"" AND rp.""NUAP"" = ANY(tcpa.""Areas_Por_Compensar"")
                             WHERE rp.rn = 1
                             ORDER BY rp.""NUAP"", (EXTRACT(year FROM rp.""Fecha_Pesaje"")), (EXTRACT(month FROM rp.""Fecha_Pesaje"")),
                                      rp.""REPEM04_PesoTotal_Toneladas"" DESC),
                     restas AS (SELECT cte.""Nro_Ticket_Ajustable"",
                                       max(cte.""Nro_Ticket_Ajustable_Original"") AS ""Nro_Ticket_Ajustable_Original"",
                                       max(cte.""Fecha_Pesaje"")                  AS max,
                                       '440405001'::bigint                      AS ""NUAP"",
                                       'Medellín'::character varying(20)        AS ""Area_Aprovechamiento"",
                                       sum(cte.""Peso_Ticket"")::numeric(18, 2)   AS ""Peso_Ticket"",
                                       - sum(cte.""Toneladas_Ajuste"")            AS ""Toneladas_Ajuste"",
                                       max(cte.""Mes"")                           AS ""Mes"",
                                       max(cte.""Año"")                           AS ""Año""
                                FROM cte
                                GROUP BY cte.""Nro_Ticket_Ajustable"")
                SELECT cte.""Nro_Ticket_Ajustable"",
                       cte.""Nro_Ticket_Ajustable_Original"",
                       cte.""Fecha_Pesaje"",
                       cte.""NUAP"",
                       cte.""Area_Aprovechamiento"",
                       cte.""Peso_Ticket""::numeric(18, 2) AS ""Peso_Ticket"",
                       dm.""Toneladas_Desviacion""         AS ""Toneladas_Ajuste"",
                       cte.""Mes"",
                       cte.""Año""
                FROM cte
                         JOIN ""REPEM_Distribuciones_de_Microrutas"" dm
                              ON dm.""NUAP"" = cte.""NUAP"" AND dm.""Año""::numeric = EXTRACT(year FROM cte.""Fecha_Pesaje"") AND
                                 dm.""Mes""::numeric = EXTRACT(month FROM cte.""Fecha_Pesaje"")
                UNION ALL
                SELECT restas.""Nro_Ticket_Ajustable"",
                       restas.""Nro_Ticket_Ajustable_Original"",
                       restas.max AS ""Fecha_Pesaje"",
                       restas.""NUAP"",
                       restas.""Area_Aprovechamiento"",
                       restas.""Peso_Ticket"",
                       restas.""Toneladas_Ajuste"",
                       restas.""Mes"",
                       restas.""Año""
                FROM restas;
        
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones""
                            (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                             ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                             ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                             ""CE_REL_COMPENSACION_ID_TICKET"", ""CE_AJUSTE_DECIMAL"")
                as
                WITH cte_comp AS (SELECT tdha.""Id_Ticket"",
                                         tdha.""NUAP"",
                                         tdha.""Toneladas_Resultantes"",
                                         tdha.""Tipo_Compensacion"",
                                         tdha.""Tipo_Compensacion"",
                                         rpm_1.""REPEM04_PesoTotal_Toneladas"",
                                         mic.""REPEM07_Numero_Ruta_Intendencia"" AS ""Numero_Ruta_Indendencia"",
                                         mic.""REPEM07_Ruta_Larga"",
                                         mic.""REPEM07_Porcentaje_No_Aforado"",
                                         mic.""REPEM07_Porcentaje_Barrido"",
                                         mic.""REPEM07_Porcentaje_Residuos_Aprovechables"",
                                         ap.""REPEM02_Nombre""                   AS ""Area_Aprovechamiento"",
                                         dm.""Porcentaje_Distribucion_Peaje"",
                                         pe.""REPEM06_Valor""::numeric           AS ""Valor_Peaje"",
                                         tr.""Toneladas_Descuento_Medellin"",
                                         tr.""Peaje_Descuento_Medellin""
                                  FROM ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                           JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm_1
                                                ON tdha.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                           JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                ON rpm_1.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                                   mic.""REPEM07_NUAP"" = tdha.""NUAP""
                                           JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                                ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                                           LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                           LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dm
                                                     ON dm.""Año""::numeric = EXTRACT(year FROM rpm_1.""REPEM04_FechaHora_Pesaje"") AND
                                                        dm.""Mes""::numeric = EXTRACT(month FROM rpm_1.""REPEM04_FechaHora_Pesaje"") AND
                                                        dm.""NUAP"" = tdha.""NUAP""
                                           LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe
                                                     ON pe.""REPEM06_Placa""::text = rpm_1.""REPEM04_Patente""::text AND
                                                        EXTRACT(year FROM pe.""REPEM06_Fecha_Validez"") =
                                                        EXTRACT(year FROM rpm_1.""REPEM04_FechaHora_Pesaje"")
                                  WHERE tdha.""Tipo_Compensacion"" = ANY (ARRAY ['TOTAL'::text, 'PARCIAL'::text])),
                     calculated_comp AS (SELECT c.""Id_Ticket"",
                                                c.""NUAP"",
                                                c.""Toneladas_Resultantes"",
                                                c.""Tipo_Compensacion"",
                                                c.""Tipo_Compensacion_1"" AS ""Tipo_Compensacion"",
                                                c.""REPEM04_PesoTotal_Toneladas"",
                                                c.""Numero_Ruta_Indendencia"",
                                                c.""REPEM07_Ruta_Larga"",
                                                c.""REPEM07_Porcentaje_No_Aforado"",
                                                c.""REPEM07_Porcentaje_Barrido"",
                                                c.""REPEM07_Porcentaje_Residuos_Aprovechables"",
                                                c.""Area_Aprovechamiento"",
                                                c.""Porcentaje_Distribucion_Peaje"",
                                                c.""Valor_Peaje"",
                                                c.""Toneladas_Descuento_Medellin"",
                                                c.""Peaje_Descuento_Medellin"",
                                                CASE
                                                    WHEN c.""NUAP"" = 440405001 THEN c.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                                   COALESCE(c.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                    WHEN c.""NUAP"" = 440705360 AND c.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                                        THEN COALESCE(c.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                    ELSE c.""Toneladas_Resultantes""
                                                    END                 AS ""Calculo_Toneladas"",
                                                CASE
                                                    WHEN c.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                                    ELSE (
                                                        CASE
                                                            WHEN c.""NUAP"" = 440405001 THEN c.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                                           COALESCE(c.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                            WHEN c.""NUAP"" = 440705360 AND c.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                                THEN c.""Toneladas_Descuento_Medellin""
                                                            ELSE c.""Toneladas_Resultantes""
                                                            END *
                                                        (c.""REPEM07_Porcentaje_Barrido""::numeric / 100::numeric))::numeric(18, 3)
                                                    END::numeric(18, 3) AS ""Calculo_Barrido"",
                                                ROUND(ROUND(CASE
                                                    WHEN c.""Porcentaje_Distribucion_Peaje"" IS NOT NULL AND
                                                         c.""Valor_Peaje"" IS NOT NULL AND c.""NUAP"" = '440705360'::bigint AND
                                                         c.""REPEM07_Porcentaje_No_Aforado"" <> 0 THEN c.""Peaje_Descuento_Medellin""
                                                    WHEN c.""Porcentaje_Distribucion_Peaje"" IS NOT NULL AND c.""Valor_Peaje"" IS NOT NULL
                                                        THEN c.""Valor_Peaje"" * 2::numeric * c.""Porcentaje_Distribucion_Peaje""::numeric /
                                                             100::numeric
                                                    ELSE 0::numeric
                                                    END::numeric, 1), 0)::numeric  AS ""Calculo_Peaje""
                                         FROM cte_comp c(""Id_Ticket"", ""NUAP"", ""Toneladas_Resultantes"", ""Tipo_Compensacion"",
                                                         ""Tipo_Compensacion_1"", ""REPEM04_PesoTotal_Toneladas"",
                                                         ""Numero_Ruta_Indendencia"", ""REPEM07_Ruta_Larga"",
                                                         ""REPEM07_Porcentaje_No_Aforado"", ""REPEM07_Porcentaje_Barrido"",
                                                         ""REPEM07_Porcentaje_Residuos_Aprovechables"", ""Area_Aprovechamiento"",
                                                         ""Porcentaje_Distribucion_Peaje"", ""Valor_Peaje"", ""Toneladas_Descuento_Medellin"",
                                                         ""Peaje_Descuento_Medellin""))
                SELECT cc.""NUAP""                                                              AS ""C1_NUAP"",
                       1                                                                      AS ""C2_TIPO_SITIO"",
                       720105237                                                              AS ""C3_NUSD"",
                       compensation.""REPEM04_Patente""                                         AS ""C4_PLACA"",
                       compensation.""REPEM04_FechaHora_Pesaje""::date                          AS ""C5_FECHA"",
                       compensation.""REPEM04_FechaHora_Pesaje""::time without time zone        AS ""C6_HORA"",
                       cc.""Numero_Ruta_Indendencia""                                           AS ""C7_NUMICRO"",
                       0                                                                      AS ""C8_TON_LIMP_URB"",
                       round(cc.""Calculo_Barrido"", 3)                                         AS ""C9_TON_BARRIDO"",
                       cc.""Calculo_Toneladas"" + COALESCE(cred.""Toneladas_Ajuste"", 0::numeric) AS ""C10_TONRESNA"",
                       COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                       AS ""C11_TONRECHAPR"",
                       CASE
                           WHEN cc.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN cc.""Calculo_Toneladas"" *
                                                                                                           (cc.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric / 100::numeric)
                           ELSE 0::numeric
                           END                                                                AS ""C12_TONRESAPR"",
                       '1'::text                                                              AS ""C13_SISTEMA_MEDICION"",
                       cc.""Calculo_Peaje""                                                     AS ""C14_VLRPEAJ"",
                       rtc.""Nro_Ticket_Compensacion""                                          AS ""CE_ID_TICKET"",
                       cc.""Area_Aprovechamiento""                                              AS ""CE_NOMBRE_AREA"",
                       cc.""REPEM07_Ruta_Larga""                                                AS ""CE_RUTA_LARGA"",
                       rtc.""Maximo_Toneladas_Compensables""                                    AS ""CE_TON_TOTAL"",
                       true                                                                   AS ""CE_REL_COMPENSACION"",
                       rtc.""Id_Ticket""                                                        AS ""CE_REL_COMPENSACION_ID_TICKET"",
                       cred.* IS NOT NULL                                                     AS ""CE_AJUSTE_DECIMAL""
                FROM calculated_comp cc(""Id_Ticket"", ""NUAP"", ""Toneladas_Resultantes"", ""Tipo_Compensacion"", ""Tipo_Compensacion_1"",
                                        ""REPEM04_PesoTotal_Toneladas"", ""Numero_Ruta_Indendencia"", ""REPEM07_Ruta_Larga"",
                                        ""REPEM07_Porcentaje_No_Aforado"", ""REPEM07_Porcentaje_Barrido"",
                                        ""REPEM07_Porcentaje_Residuos_Aprovechables"", ""Area_Aprovechamiento"",
                                        ""Porcentaje_Distribucion_Peaje"", ""Valor_Peaje"", ""Toneladas_Descuento_Medellin"",
                                        ""Peaje_Descuento_Medellin"", ""Calculo_Toneladas"", ""Calculo_Barrido"", ""Calculo_Peaje"")
                         JOIN ""REPEM_Tickets_Compensables"" rtc ON cc.""Id_Ticket"" = rtc.""Id_Ticket""
                         JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" compensation
                              ON compensation.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                         LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                   ON cc.""Id_Ticket"" = tre.""Ticket_Asignable"" AND cc.""NUAP"" = 440405001
                         LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" cred
                                   ON rtc.""Nro_Ticket_Compensacion"" = cred.""Nro_Ticket_Ajustable"" AND cc.""NUAP"" = cred.""NUAP"" AND
                                      rtc.""Id_Ticket"" = cred.""Nro_Ticket_Ajustable_Original"";
        
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14""
                            (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                             ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                             ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                             ""CE_REL_COMPENSACION_ID_TICKET"", ""CE_AJUSTE_DECIMAL"")
                as
                WITH cte AS (SELECT rpm.""REPEM04_Id""                                     AS ""Id_Ticket"",
                                    mic.""REPEM07_NUAP""                                   AS ""NUAP"",
                                    CASE
                                        WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                                 COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                        WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                            THEN tr.""Toneladas_Descuento_Medellin""
                                        ELSE tdha.""Toneladas_Resultantes""
                                        END                                              AS ""Calculo_Toneladas"",
                                    CASE
                                        WHEN mic.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                        ELSE (
                                            CASE
                                                WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas"" -
                                                                                         COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                    THEN tr.""Toneladas_Descuento_Medellin""
                                                ELSE tdha.""Toneladas_Resultantes""
                                                END * (mic.""REPEM07_Porcentaje_Barrido""::numeric / 100::numeric))::numeric(18, 3)
                                        END::numeric(18, 3)                              AS ""Calculo_Barrido"",
                                    ROUND(ROUND(CASE
                                        WHEN pe.* IS NULL THEN 0::numeric
                                        WHEN mic.""REPEM07_NUAP"" = 440405001 THEN 0::numeric
                                        WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                            THEN 0::numeric
                                        ELSE (pe.""REPEM06_Valor"" * 2::numeric *
                                              (COALESCE(dpm.""Porcentaje_Distribucion_Peaje"", 100::numeric) /
                                               100::numeric))::numeric
                                        END::numeric, 1), 0)::numeric                      AS ""Calculo_Peaje"",
                                    COALESCE(tdha.""Tipo_Compensacion"", 'ORIGINAL'::text) AS ""Tipo_Compensacion""
                             FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                      JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                           ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                      LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                            dpm.""Año""::numeric =
                                                                                            EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                            dpm.""Mes""::numeric =
                                                                                            EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                      LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe
                                                ON pe.""REPEM06_Placa""::text = rpm.""REPEM04_Patente""::text AND
                                                   EXTRACT(year FROM pe.""REPEM06_Fecha_Validez"") =
                                                   EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                      LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                                      LEFT JOIN ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                                ON tdha.""Id_Ticket"" = rpm.""REPEM04_Id"" AND tdha.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                   tdha.""Tipo_Compensacion"" = 'ORIGINAL'::text),
                     aggregated_compensations AS (SELECT 0                                                                           AS ""Id_Ticket"",
                                                         ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"",
                                                         sum(""REPEM_Tickets_Compensables"".""Suma_Agrupacion_Toneladas_Por_Compensar"") AS ""Suma_Agrupacion_Toneladas_Por_Compensar""
                                                  FROM ""REPEM_Tickets_Compensables""
                                                  GROUP BY ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"")
                SELECT mic.""REPEM07_NUAP""                                         AS ""C1_NUAP"",
                       1                                                          AS ""C2_TIPO_SITIO"",
                       720105237                                                  AS ""C3_NUSD"",
                       rpm.""REPEM04_Patente""                                      AS ""C4_PLACA"",
                       rpm.""REPEM04_FechaHora_Pesaje""::date                       AS ""C5_FECHA"",
                       rpm.""REPEM04_FechaHora_Pesaje""::time without time zone     AS ""C6_HORA"",
                       mic.""REPEM07_Numero_Ruta_Intendencia""                      AS ""C7_NUMICRO"",
                       0                                                          AS ""C8_TON_LIMP_URB"",
                       round(bs.""Calculo_Barrido"" + COALESCE(tde.""Toneladas_Descuento"", 0::numeric), 3) -
                       COALESCE(dbi.""Valor_A_Descontar"", 0::numeric)              AS ""C9_TON_BARRIDO"",
                       CASE
                           WHEN mic.""REPEM07_Numero_de_Microruta"" = 614001 THEN round(
                                   bs.""Calculo_Toneladas"" - COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                           ELSE round(bs.""Calculo_Toneladas"" - bs.""Calculo_Barrido"" - COALESCE(tde.""Toneladas_Descuento"", 0::numeric) -
                                      COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                           END + COALESCE(compred.""Toneladas_Ajuste"", 0::numeric) AS ""C10_TONRESNA"",
                       COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)           AS ""C11_TONRECHAPR"",
                       CASE
                           WHEN mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN bs.""Calculo_Toneladas"" *
                                                                                                            (mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric /
                                                                                                             100::numeric)
                           ELSE 0::numeric
                           END                                                    AS ""C12_TONRESAPR"",
                       '1'::text                                                  AS ""C13_SISTEMA_MEDICION"",
                       bs.""Calculo_Peaje""                                         AS ""C14_VLRPEAJ"",
                       rpm.""REPEM04_Id""                                           AS ""CE_ID_TICKET"",
                       ap.""REPEM02_Nombre""                                        AS ""CE_NOMBRE_AREA"",
                       mic.""REPEM07_Ruta_Larga""                                   AS ""CE_RUTA_LARGA"",
                       rpm.""REPEM04_PesoTotal_Toneladas""                          AS ""CE_TON_TOTAL"",
                       rtc.* IS NOT NULL                                          AS ""CE_REL_COMPENSACION"",
                       rtc.""Id_Ticket""                                            AS ""CE_REL_COMPENSACION_ID_TICKET"",
                       compred.* IS NOT NULL                                      AS ""CE_AJUSTE_DECIMAL""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                         JOIN cte bs ON bs.""Calculo_Toneladas"" IS NOT NULL AND rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                              ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                 mic.""REPEM07_NUAP"" = bs.""NUAP""
                         JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                         LEFT JOIN ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" tde ON rpm.""REPEM04_Id"" = tde.""NroTicket"" AND
                                                                                          EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                          tde.""Año"" AND
                                                                                          EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                          tde.""Mes"" AND
                                                                                          mic.""REPEM07_Numero_de_Microruta"" =
                                                                                          tde.""RutaCodigo"" AND
                                                                                          mic.""REPEM07_NUAP"" = tde.""NUAP""
                         LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                   ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mic.""REPEM07_NUAP"" = 440405001
                         LEFT JOIN aggregated_compensations rtc ON rpm.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                         LEFT JOIN ""REPEM_Descuento_de_Barrido_Itagui_Por_Ticket"" dbi ON dbi.""Id_Ticket_A_Descontar"" = rpm.""REPEM04_Id""
                         LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" compred
                                   ON compred.""Nro_Ticket_Ajustable"" = rpm.""REPEM04_Id"" AND compred.""NUAP"" = mic.""REPEM07_NUAP""
                UNION ALL
                SELECT ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C1_NUAP"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C2_TIPO_SITIO"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C3_NUSD"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C4_PLACA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C5_FECHA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C6_HORA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C7_NUMICRO"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C8_TON_LIMP_URB"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C9_TON_BARRIDO"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C10_TONRESNA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C11_TONRECHAPR"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C12_TONRESAPR"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C13_SISTEMA_MEDICION"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C14_VLRPEAJ"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_ID_TICKET"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_NOMBRE_AREA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_RUTA_LARGA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_TON_TOTAL"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_REL_COMPENSACION"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_REL_COMPENSACION_ID_TICKET"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_AJUSTE_DECIMAL""
                FROM ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"";
            ");
        }
        
        private static void RecreateViewsWithChanges(MigrationBuilder migrationBuilder)
        {
            //Recreate Non-materialized Views
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Toneladas_Descuento_Excepcional""
                            (""Año"", ""Mes"", ""Rut_LMV"", ""Rut_MJS"", ""Nro_LMV"", ""Nro_MJS"", ""Nro_Domingo"", ""Total_Dias"", ""Klmts_LMV"",
                             ""Klmts_MJS"", ""Klmts_Total"", ""Densidad"", ""Kilos_Total_Descuento"", ""Toneladas_Total_Descuento"")
                as
                SELECT a.""Año"",
                       a.""Mes"",
                       f.""Rut_LMV"",
                       f.""Rut_MJS"",
                       f.""Nro_LMV"",
                       f.""Nro_MJS"",
                       f.""Nro_Domingo"",
                       f.""Total_Dias"",
                       (f.""Rut_LMV"" * f.""Nro_LMV""::numeric)::numeric(18, 3)                                                        AS ""Klmts_LMV"",
                       (f.""Rut_MJS"" * f.""Nro_MJS""::numeric)::numeric(18, 3)                                                        AS ""Klmts_MJS"",
                       (f.""Rut_LMV"" * f.""Nro_LMV""::numeric)::numeric(18, 3) +
                       (f.""Rut_MJS"" * f.""Nro_MJS""::numeric)::numeric(18, 3)                                                        AS ""Klmts_Total"",
                       40::numeric                                                                                                 AS ""Densidad"",
                       (((f.""Rut_LMV"" * f.""Nro_LMV""::numeric)::numeric(18, 3) + (f.""Rut_MJS"" * f.""Nro_MJS""::numeric)::numeric(18, 3)) *
                        40::numeric)::numeric(18, 3)                                                                               AS ""Kilos_Total_Descuento"",
                       ((((f.""Rut_LMV"" * f.""Nro_LMV""::numeric)::numeric(18, 3) + (f.""Rut_MJS"" * f.""Nro_MJS""::numeric)::numeric(18, 3)) *
                         40::numeric)::numeric(18, 3) /
                        1000::numeric)::numeric(18, 3)                                                                             AS ""Toneladas_Total_Descuento""
                FROM (SELECT EXTRACT(year FROM
                                     ""Reporting-Emvarias_04-Recoleccion_por_Microruta"".""REPEM04_FechaHora_Pesaje"")::integer AS ""Año"",
                             EXTRACT(month FROM
                                     ""Reporting-Emvarias_04-Recoleccion_por_Microruta"".""REPEM04_FechaHora_Pesaje"")::integer AS ""Mes""
                      FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta""
                      GROUP BY (EXTRACT(year FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"".""REPEM04_FechaHora_Pesaje"")),
                               (EXTRACT(month FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"".""REPEM04_FechaHora_Pesaje""))) a
                         LEFT JOIN LATERAL ( SELECT 4.00             AS ""Rut_LMV"",
                                                    4.02             AS ""Rut_MJS"",
                                                    sum(
                                                            CASE
                                                                WHEN EXTRACT(dow FROM final_subquery.date) = ANY
                                                                     (ARRAY [0::numeric, 2::numeric, 4::numeric]) THEN 1
                                                                ELSE 0
                                                                END) AS ""Nro_LMV"",
                                                    sum(
                                                            CASE
                                                                WHEN EXTRACT(dow FROM final_subquery.date) = ANY
                                                                     (ARRAY [1::numeric, 3::numeric, 5::numeric]) THEN 1
                                                                ELSE 0
                                                                END) AS ""Nro_MJS"",
                                                    sum(
                                                            CASE
                                                                WHEN EXTRACT(dow FROM final_subquery.date) = 6::numeric THEN 1
                                                                ELSE 0
                                                                END) AS ""Nro_Domingo"",
                                                    count(*)         AS ""Total_Dias""
                                             FROM (SELECT date.date::date AS date
                                                   FROM generate_series(date_trunc('month'::text,
                                                                                   make_date(a.""Año"", a.""Mes"", 1)::timestamp with time zone),
                                                                        date_trunc('month'::text,
                                                                                   make_date(a.""Año"", a.""Mes"", 1)::timestamp with time zone) +
                                                                        '1 mon -1 days'::interval,
                                                                        '1 day'::interval) date(date)) final_subquery) f ON true;
        
                create or replace view ""REPEM_Sumatoria_Pesos_Rutas_Compartidas""(""Año"", ""Mes"", ""Suma_Pesos_M3"", ""Suma_Pesos_Toneladas"") as
                SELECT ""Año"",
                       ""Mes"",
                       sum(""PesoTotal"")                 AS ""Suma_Pesos_M3"",
                       sum(""PesoTotal"") / 1000::numeric AS ""Suma_Pesos_Toneladas""
                FROM (SELECT rpm.""REPEM04_Id""                                   AS ""IdTicket"",
                             EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")  AS ""Año"",
                             EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") AS ""Mes"",
                             rpm.""REPEM04_PesoTotal""                            AS ""PesoTotal""
                      FROM ""Reporting-Emvarias_07-Microrutas"" mr
                               JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                    ON mr.""REPEM07_Numero_de_Microruta""::text = rpm.""REPEM04_RutaCodigo""::text
                      WHERE mr.""REPEM07_NUAP"" <> 440405001
                        AND mr.""REPEM07_Ruta_Larga"" <> '0614001'::bpchar
                        AND rpm.""REPEM04_FechaHora_Pesaje"" >= mr.""REPEM07_Fecha_Inicio_Vigencia""
                        AND (mr.""REPEM07_Fecha_Fin_Vigencia"" IS NULL OR rpm.""REPEM04_FechaHora_Pesaje"" <= mr.""REPEM07_Fecha_Fin_Vigencia"")
                      GROUP BY rpm.""REPEM04_Id"", (EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")),
                               (EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje""))) subquery
                GROUP BY ""Año"", ""Mes""
                ORDER BY ""Año"", ""Mes"";
        
                create or replace view ""REPEM_Distribuciones_de_Microrutas""
                        (""Año"", ""Mes"", ""NUAP"", ""Area_Aprovechamiento"", ""Cantidad_de_Viajes"", ""Toneladas_Reportadas"",
                        ""Toneladas_Distribuidas"", ""Toneladas_Desviacion"", ""Porcentaje_Distribucion_Peaje"",
                        ""Toneladas_Rutas_Compartidas"")
                as
                SELECT rv.""REPEM05_Año""                                                                     AS ""Año"",
                       rv.""REPEM05_Mes""                                                                     AS ""Mes"",
                       ap.""REPEM02_Codigo""                                                                  AS ""NUAP"",
                       ap.""REPEM02_Nombre""                                                                  AS ""Area_Aprovechamiento"",
                       count(*)                                                                             AS ""Cantidad_de_Viajes"",
                       rv.""REPEM05_Toneladas""                                                               AS ""Toneladas_Reportadas"",
                       round(round(rv.""REPEM05_Toneladas"" / count(*)::numeric, 4), 3)::numeric(18, 3)       AS ""Toneladas_Distribuidas"",
                       round(round((rv.""REPEM05_Toneladas"" / count(*)::numeric -
                                    round(round(rv.""REPEM05_Toneladas"" / count(*)::numeric, 4), 3)::numeric(18, 3)) * count(*)::numeric,
                                   4), 3)::numeric(18, 3)                                                   AS ""Toneladas_Desviacion"",
                       (rv.""REPEM05_Toneladas"" / src.""Suma_Pesos_Toneladas"" * 100::numeric)::numeric(18, 5) AS ""Porcentaje_Distribucion_Peaje"",
                       src.""Suma_Pesos_Toneladas""                                                           AS ""Toneladas_Rutas_Compartidas""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm2
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic2
                              ON rpm2.""REPEM04_RutaCodigo""::text = mic2.""REPEM07_Numero_de_Microruta""::character varying(16)::text
                              AND rpm2.""REPEM04_FechaHora_Pesaje"" >= mic2.""REPEM07_Fecha_Inicio_Vigencia""
                              AND (mic2.""REPEM07_Fecha_Fin_Vigencia"" IS NULL OR rpm2.""REPEM04_FechaHora_Pesaje"" <= mic2.""REPEM07_Fecha_Fin_Vigencia"")
                         JOIN ""Reporting-Emvarias_05-Recoleccion_Vehicular"" rv
                              ON rv.""REPEM05_Año""::numeric = EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND
                                 rv.""REPEM05_Mes""::numeric = EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND
                                 rv.""REPEM05_NUAP"" = mic2.""REPEM07_NUAP""
                         JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic2.""REPEM07_NUAP""
                         JOIN ""REPEM_Sumatoria_Pesos_Rutas_Compartidas"" src
                              ON src.""Año"" = EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"") AND
                                 src.""Mes"" = EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"")
                WHERE mic2.""REPEM07_Porcentaje_No_Aforado""::numeric = 0::numeric
                  AND ap.""REPEM02_Codigo"" <> 440405001
                  AND NOT (mic2.""REPEM07_NUAP"" = 441005380 AND mic2.""REPEM07_Ruta_Larga"" = '0911504'::bpchar)
                GROUP BY rv.""REPEM05_Toneladas"", src.""Suma_Pesos_Toneladas"", ap.""REPEM02_Nombre"", rv.""REPEM05_Año"", rv.""REPEM05_Mes"",
                         ap.""REPEM02_Codigo""
                ORDER BY rv.""REPEM05_Año"", rv.""REPEM05_Mes"", ap.""REPEM02_Nombre"";
        
                create or replace view ""REPEM_Dependencia_Toneladas_A_Compensar""
                        (""Id_Ticket"", ""NUAP"", ""Numero_de_Microruta"", ""Numero_Ruta_Intendencia"", ""Patente"", ""Toneladas_Distribuidas"",
                        ""PesoTotal_Toneladas"", ""Porcentaje_No_Aforado"", ""Porcentaje_Limpieza_Urbana"", ""Porcentaje_Barrido"",
                        ""Porcentaje_Residuos_Aprovechables"", ""Fecha_Hora_Pesaje"", ""Sumatoria_Acumulada"")
                as
                SELECT rpm.""REPEM04_Id""                                                                                                   AS ""Id_Ticket"",
                       mic.""REPEM07_NUAP""                                                                                                 AS ""NUAP"",
                       mic.""REPEM07_Numero_de_Microruta""                                                                                  AS ""Numero_de_Microruta"",
                       mic.""REPEM07_Numero_Ruta_Intendencia""                                                                              AS ""Numero_Ruta_Intendencia"",
                       rpm.""REPEM04_Patente""                                                                                              AS ""Patente"",
                       dpm.""Toneladas_Distribuidas"",
                       rpm.""REPEM04_PesoTotal_Toneladas""                                                                                  AS ""PesoTotal_Toneladas"",
                       mic.""REPEM07_Porcentaje_No_Aforado""                                                                                AS ""Porcentaje_No_Aforado"",
                       mic.""REPEM07_Porcentaje_Limpieza_Urbana""                                                                           AS ""Porcentaje_Limpieza_Urbana"",
                       mic.""REPEM07_Porcentaje_Barrido""                                                                                   AS ""Porcentaje_Barrido"",
                       mic.""REPEM07_Porcentaje_Residuos_Aprovechables""                                                                    AS ""Porcentaje_Residuos_Aprovechables"",
                       rpm.""REPEM04_FechaHora_Pesaje""                                                                                     AS ""Fecha_Hora_Pesaje"",
                       sum(dpm.""Toneladas_Distribuidas"")
                       OVER (PARTITION BY rpm.""REPEM04_Id"" ORDER BY mic.""REPEM07_NUAP"" RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS ""Sumatoria_Acumulada""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                              ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                AND rpm.""REPEM04_FechaHora_Pesaje"" >= mic.""REPEM07_Fecha_Inicio_Vigencia""
                                AND (mic.""REPEM07_Fecha_Fin_Vigencia"" IS NULL OR rpm.""REPEM04_FechaHora_Pesaje"" <= mic.""REPEM07_Fecha_Fin_Vigencia"")
                         JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                         JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND dpm.""Año""::numeric =
                                                                                                              EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                          dpm.""Mes""::numeric =
                                                                          EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"");
        
                create or replace view ""REPEM_Detalle_Compensacion_Tickets""
                            (""Id_Ticket"", ""NUAP"", ""Fecha_Hora_Pesaje"", ""Toneladas_Resultantes"", ""Tipo_Compensacion"") as
                WITH cte AS (SELECT dtc.""Id_Ticket"",
                                    dtc.""NUAP"",
                                    dtc.""Numero_de_Microruta"",
                                    dtc.""Numero_Ruta_Intendencia"",
                                    dtc.""Patente"",
                                    dtc.""Toneladas_Distribuidas"",
                                    dtc.""PesoTotal_Toneladas"",
                                    dtc.""Porcentaje_No_Aforado"",
                                    dtc.""Porcentaje_Limpieza_Urbana"",
                                    dtc.""Porcentaje_Barrido"",
                                    dtc.""Porcentaje_Residuos_Aprovechables"",
                                    dtc.""Fecha_Hora_Pesaje"",
                                    dtc.""Sumatoria_Acumulada"",
                                    CASE
                                        WHEN dtc.""Sumatoria_Acumulada"" < dtc.""PesoTotal_Toneladas"" THEN 0::numeric
                                        ELSE dtc.""Sumatoria_Acumulada"" - dtc.""PesoTotal_Toneladas""::numeric
                                        END AS ""Exceso_Acumulado""
                             FROM ""REPEM_Dependencia_Toneladas_A_Compensar"" dtc),
                     cnt AS (SELECT cte.""Id_Ticket"",
                                    count(cte.""Id_Ticket"") AS ""Cantidad""
                             FROM cte
                             GROUP BY cte.""Id_Ticket""),
                     rst AS (SELECT DISTINCT ON (cte.""Id_Ticket"") cte.""Id_Ticket"",
                                                                  cte.""PesoTotal_Toneladas"" -
                                                                  max(cte.""Sumatoria_Acumulada"") OVER (PARTITION BY cte.""Id_Ticket"") AS ""Resto_Disponible""
                             FROM cte
                                      JOIN cnt ON cnt.""Id_Ticket"" = cte.""Id_Ticket""
                             WHERE cte.""Exceso_Acumulado"" = 0::numeric
                               AND cnt.""Cantidad"" > 1
                             ORDER BY cte.""Id_Ticket"", cte.""Toneladas_Distribuidas"" DESC),
                     sprst AS (SELECT DISTINCT ON (cte.""Id_Ticket"") cte.""Id_Ticket"",
                                                                    cte.""Toneladas_Distribuidas"" - cte.""PesoTotal_Toneladas""::numeric AS ""Resto_Disponible""
                               FROM cte
                                        JOIN cnt ON cnt.""Id_Ticket"" = cte.""Id_Ticket""
                               WHERE cte.""Exceso_Acumulado"" > 0::numeric
                                 AND cnt.""Cantidad"" = 1
                               ORDER BY cte.""Id_Ticket"", cte.""Toneladas_Distribuidas"" DESC),
                     pcomp AS (SELECT cte.""Id_Ticket"",
                                      cte.""NUAP"",
                                      cte.""Numero_de_Microruta"",
                                      cte.""Numero_Ruta_Intendencia"",
                                      cte.""Patente"",
                                      cte.""Toneladas_Distribuidas"",
                                      cte.""PesoTotal_Toneladas"",
                                      cte.""Porcentaje_No_Aforado"",
                                      cte.""Porcentaje_Limpieza_Urbana"",
                                      cte.""Porcentaje_Barrido"",
                                      cte.""Porcentaje_Residuos_Aprovechables"",
                                      cte.""Fecha_Hora_Pesaje"",
                                      cte.""Sumatoria_Acumulada"",
                                      cte.""Exceso_Acumulado"",
                                      cte.""Toneladas_Distribuidas"" - rst.""Resto_Disponible"" AS ""Toneladas_Resultantes""
                               FROM cte
                                        JOIN (SELECT icte.""Id_Ticket"",
                                                     max(icte.""Toneladas_Distribuidas"") AS max_toneladas
                                              FROM cte icte
                                              GROUP BY icte.""Id_Ticket"") sub
                                             ON cte.""Id_Ticket"" = sub.""Id_Ticket"" AND cte.""Toneladas_Distribuidas"" = sub.max_toneladas
                                        JOIN rst ON rst.""Id_Ticket"" = cte.""Id_Ticket""
                               WHERE cte.""Exceso_Acumulado"" > 0::numeric
                               UNION ALL
                               SELECT cte.""Id_Ticket"",
                                      cte.""NUAP"",
                                      cte.""Numero_de_Microruta"",
                                      cte.""Numero_Ruta_Intendencia"",
                                      cte.""Patente"",
                                      cte.""Toneladas_Distribuidas"",
                                      cte.""PesoTotal_Toneladas"",
                                      cte.""Porcentaje_No_Aforado"",
                                      cte.""Porcentaje_Limpieza_Urbana"",
                                      cte.""Porcentaje_Barrido"",
                                      cte.""Porcentaje_Residuos_Aprovechables"",
                                      cte.""Fecha_Hora_Pesaje"",
                                      cte.""Sumatoria_Acumulada"",
                                      cte.""Exceso_Acumulado"",
                                      sprst.""Resto_Disponible"" AS ""Toneladas_Resultantes""
                               FROM cte
                                        JOIN sprst ON sprst.""Id_Ticket"" = cte.""Id_Ticket"")
                SELECT cte.""Id_Ticket"",
                       cte.""NUAP"",
                       cte.""Fecha_Hora_Pesaje"",
                       cte.""Toneladas_Distribuidas"" + COALESCE(pcomp.""Toneladas_Resultantes"", 0::numeric) AS ""Toneladas_Resultantes"",
                       CASE
                           WHEN cte.""Exceso_Acumulado"" = 0::numeric OR pcomp.* IS NOT NULL THEN 'ORIGINAL'::text
                           ELSE 'TOTAL'::text
                           END                                                                            AS ""Tipo_Compensacion""
                FROM cte
                         LEFT JOIN pcomp ON pcomp.""Id_Ticket"" = cte.""Id_Ticket"" AND cte.""NUAP"" = pcomp.""NUAP""
                WHERE pcomp.* IS NULL
                UNION ALL
                SELECT pcomp.""Id_Ticket"",
                       pcomp.""NUAP"",
                       pcomp.""Fecha_Hora_Pesaje"",
                       pcomp.""Toneladas_Distribuidas"" AS ""Toneladas_Resultantes"",
                       'TOTAL'::text                  AS ""Tipo_Compensacion""
                FROM pcomp;
        
                create or replace view public.""REPEM_Tickets_Compensables""
                            (""Id_Ticket"", ""Suma_Agrupacion_Toneladas_Por_Compensar"", ""Nro_Ticket_Compensacion"",
                             ""Maximo_Toneladas_Compensables"", ""Fecha_Pesaje"")
                as
                WITH RECURSIVE
                    tickets_to_compensate AS (SELECT td.""Id_Ticket"",
                                                     td.""Fecha_Hora_Pesaje""::date                                                                                                                                                      AS ""Fecha_Pesaje"",
                                                     sum(td.""Toneladas_Resultantes"")                                                                                                                                                   AS ""Suma_Toneladas_Agrupadas"",
                                                     EXTRACT(year FROM td.""Fecha_Hora_Pesaje""::date)::integer                                                                                                                          AS year,
                                                     EXTRACT(month FROM td.""Fecha_Hora_Pesaje""::date)::integer                                                                                                                         AS month,
                                                     row_number()
                                                     OVER (PARTITION BY (EXTRACT(year FROM td.""Fecha_Hora_Pesaje""::date)), (EXTRACT(month FROM td.""Fecha_Hora_Pesaje""::date)) ORDER BY (td.""Fecha_Hora_Pesaje""::date), td.""Id_Ticket"") AS demand_seq,
                                                     array_agg(td.""NUAP"") AS ""Areas_Por_Compensar""
                                              FROM ""REPEM_Detalle_Compensacion_Tickets"" td
                                              WHERE td.""Tipo_Compensacion"" = ANY (ARRAY ['PARCIAL'::text, 'TOTAL'::text])
                                              GROUP BY (td.""Fecha_Hora_Pesaje""::date), td.""Id_Ticket""),
                    compensation_posibilities AS (SELECT rpm2.""REPEM04_Id"",
                                                         rpm2.""REPEM04_FechaHora_Pesaje""::date                                                                                                                                            AS ""RPM_Fecha_Pesaje"",
                                                         rpm2.""REPEM04_PesoTotal_Toneladas"",
                                                         EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"")::integer                                                                                                                      AS year,
                                                         EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"")::integer                                                                                                                     AS month,
                                                         row_number()
                                                         OVER (PARTITION BY (EXTRACT(year FROM rpm2.""REPEM04_FechaHora_Pesaje"")), (EXTRACT(month FROM rpm2.""REPEM04_FechaHora_Pesaje"")) ORDER BY rpm2.""REPEM04_PesoTotal_Toneladas"" DESC) AS supply_seq
                                                  FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm2
                                                  WHERE (rpm2.""REPEM04_RutaCodigo""::text = ANY
                                                         (ARRAY ['615618'::text, '618219'::text, '618119'::text, '618319'::text]))
                                                    AND rpm2.""REPEM04_PesoTotal_Toneladas"" > 0::numeric),
                    ticket_assignment(year, month, assigned_demand_seq, assigned_supply_seq, current_supply_id, current_supply_max_tons,
                                      remaining_capacity) AS (SELECT d_1.year,
                                                                     d_1.month,
                                                                     d_1.demand_seq,
                                                                     s.supply_seq,
                                                                     s.""REPEM04_Id"",
                                                                     s.""REPEM04_PesoTotal_Toneladas"",
                                                                     s.""REPEM04_PesoTotal_Toneladas"" - d_1.""Suma_Toneladas_Agrupadas"" AS remaining_capacity
                                                              FROM tickets_to_compensate d_1
                                                                       JOIN compensation_posibilities s ON d_1.year = s.year AND d_1.month = s.month
                                                              WHERE d_1.demand_seq = 1
                                                                AND s.supply_seq = 1
                                                              UNION ALL
                                                              SELECT prev.year,
                                                                     prev.month,
                                                                     curr_d.demand_seq,
                                                                     CASE
                                                                         WHEN prev.remaining_capacity >= curr_d.""Suma_Toneladas_Agrupadas""
                                                                             THEN prev.assigned_supply_seq
                                                                         ELSE prev.assigned_supply_seq + 1
                                                                         END AS assigned_supply_seq,
                                                                     CASE
                                                                         WHEN prev.remaining_capacity >= curr_d.""Suma_Toneladas_Agrupadas""
                                                                             THEN prev.current_supply_id
                                                                         ELSE next_s.""REPEM04_Id""
                                                                         END AS current_supply_id,
                                                                     CASE
                                                                         WHEN prev.remaining_capacity >= curr_d.""Suma_Toneladas_Agrupadas""
                                                                             THEN prev.current_supply_max_tons
                                                                         ELSE next_s.""REPEM04_PesoTotal_Toneladas""
                                                                         END AS current_supply_max_tons,
                                                                     CASE
                                                                         WHEN prev.remaining_capacity >= curr_d.""Suma_Toneladas_Agrupadas""
                                                                             THEN prev.remaining_capacity - curr_d.""Suma_Toneladas_Agrupadas""
                                                                         ELSE next_s.""REPEM04_PesoTotal_Toneladas"" -
                                                                              curr_d.""Suma_Toneladas_Agrupadas""
                                                                         END AS remaining_capacity
                                                              FROM ticket_assignment prev
                                                                       JOIN tickets_to_compensate curr_d
                                                                            ON prev.year = curr_d.year AND prev.month = curr_d.month AND
                                                                               (prev.assigned_demand_seq + 1) = curr_d.demand_seq
                                                                       LEFT JOIN compensation_posibilities next_s
                                                                                 ON prev.year = next_s.year AND
                                                                                    prev.month = next_s.month AND
                                                                                    (prev.assigned_supply_seq + 1) = next_s.supply_seq
                                                              WHERE prev.remaining_capacity >= curr_d.""Suma_Toneladas_Agrupadas""
                                                                 OR prev.remaining_capacity < curr_d.""Suma_Toneladas_Agrupadas"" AND
                                                                    next_s.supply_seq IS NOT NULL)
                SELECT d.""Id_Ticket"",
                       d.""Suma_Toneladas_Agrupadas"" AS ""Suma_Agrupacion_Toneladas_Por_Compensar"",
                       ga.current_supply_id         AS ""Nro_Ticket_Compensacion"",
                       ga.current_supply_max_tons   AS ""Maximo_Toneladas_Compensables"",
                       d.""Fecha_Pesaje"",
                       d.""Areas_Por_Compensar""
                FROM ticket_assignment ga
                         JOIN tickets_to_compensate d
                              ON ga.year = d.year AND ga.month = d.month AND ga.assigned_demand_seq = d.demand_seq
                ORDER BY d.year, d.month, d.demand_seq;
        
                create or replace view ""REPEM_Descuento_Por_Ticket""
                        (""Id_Ticket"", ""Toneladas_Descuento_Medellin"", ""Peaje_Descuento_Medellin"") as
                SELECT rpm.""REPEM04_Id"" AS ""Id_Ticket"",
                       sum(
                               CASE
                                   WHEN mic.""REPEM07_NUAP"" = 440405001 THEN 0::numeric
                                   WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                       THEN mic.""REPEM07_Porcentaje_No_Aforado""::numeric / 100::numeric *
                                            rpm.""REPEM04_PesoTotal_Toneladas""::numeric
                                   ELSE td.""Toneladas_Resultantes""
                                   END) AS ""Toneladas_Descuento_Medellin"",
                       sum(
                               CASE
                                   WHEN mic.""REPEM07_NUAP"" = 440405001 THEN 0::numeric
                                   WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                       THEN mic.""REPEM07_Porcentaje_No_Aforado""::numeric / 100::numeric * 2::numeric *
                                            pe.""REPEM06_Valor""
                                   ELSE pe.""REPEM06_Valor"" * 2::numeric * (dpm.""Porcentaje_Distribucion_Peaje"" / 100::numeric)
                                   END) AS ""Peaje_Descuento_Medellin""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                              ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                AND rpm.""REPEM04_FechaHora_Pesaje"" >= mic.""REPEM07_Fecha_Inicio_Vigencia""
                                AND (mic.""REPEM07_Fecha_Fin_Vigencia"" IS NULL OR rpm.""REPEM04_FechaHora_Pesaje"" <= mic.""REPEM07_Fecha_Fin_Vigencia"")
                         JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND dpm.""Año""::numeric =
                                                                                                              EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                          dpm.""Mes""::numeric =
                                                                          EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                         JOIN ""REPEM_Detalle_Compensacion_Tickets"" td
                              ON td.""Id_Ticket"" = rpm.""REPEM04_Id""
                                AND td.""NUAP"" = mic.""REPEM07_NUAP""
                                AND td.""Tipo_Compensacion"" = 'ORIGINAL'::text
                         LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe ON pe.""REPEM06_Placa""::text = rpm.""REPEM04_Patente""::text AND
                                                                        pe.""REPEM06_Fecha_Validez"" = ((SELECT p.""REPEM06_Fecha_Validez""
                                                                                                       FROM ""Reporting-Emvarias_06-Peajes"" p
                                                                                                       WHERE p.""REPEM06_Placa""::text = rpm.""REPEM04_Patente""::text
                                                                                                       ORDER BY (abs(EXTRACT(epoch FROM
                                                                                                                             rpm.""REPEM04_FechaHora_Pesaje"" -
                                                                                                                             p.""REPEM06_Fecha_Validez"")))
                                                                                                       LIMIT 1))
                WHERE mic.""REPEM07_NUAP"" <> '440405001'::bigint
                GROUP BY rpm.""REPEM04_Id"";
        
                create or replace view ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket""
                        (""Ticket_Asignable"", ""Toneladas_Rechazadas"", ""Cantidad_Rechazos"", ""Fecha_Rechazo"") as
                SELECT rpm.""REPEM04_Id""                           AS ""Ticket_Asignable"",
                       sum(r.""REPEM09_Toneladas"")::numeric(18, 3) AS ""Toneladas_Rechazadas"",
                       count(r.""REPEM09_Id"")                      AS ""Cantidad_Rechazos"",
                       max(r.""REPEM09_Fecha_Corta"")               AS ""Fecha_Rechazo""
                FROM ""Reporting-Emvarias_09-Rechazos"" r
                         LEFT JOIN LATERAL ( SELECT rpm_1.""REPEM04_Id"",
                                                    rpm_1.""REPEM04_Patente"",
                                                    rpm_1.""REPEM04_EstadoServicio"",
                                                    rpm_1.""REPEM04_GrupoTurno"",
                                                    rpm_1.""REPEM04_TipoServicio"",
                                                    rpm_1.""REPEM04_RutaCodigo"",
                                                    rpm_1.""REPEM04_EsRefuerzo"",
                                                    rpm_1.""REPEM04_Interno"",
                                                    rpm_1.""REPEM04_PesoTotal"",
                                                    rpm_1.""REPEM04_IdServicio"",
                                                    rpm_1.""REPEM04_FechaHora_EntradaRuta"",
                                                    rpm_1.""REPEM04_FechaHora_SalidaRuta"",
                                                    rpm_1.""REPEM04_Observaciones"",
                                                    rpm_1.""REPEM04_PesoTotal_Toneladas"",
                                                    rpm_1.""REPEM04_FechaHora_Pesaje"",
                                                    rpm_1.""REPEM04_Fecha_de_Servicio"",
                                                    rpm_1.""REPEM04_FechaHora_InicioServicio"",
                                                    rpm_1.""REPEM04_FechaHora_LlegadaBase"",
                                                    rpm_1.""REPEM04_FechaHora_SalidaBase"",
                                                    mic.""REPEM07_Numero_de_Microruta"",
                                                    mic.""REPEM07_NUAP"",
                                                    mic.""REPEM07_Numero_Ruta_Intendencia"",
                                                    mic.""REPEM07_Porcentaje_Barrido"",
                                                    mic.""REPEM07_Porcentaje_Limpieza_Urbana"",
                                                    mic.""REPEM07_Porcentaje_No_Aforado"",
                                                    mic.""REPEM07_Porcentaje_Residuos_Aprovechables"",
                                                    mic.""REPEM07_Ruta_Larga""
                                             FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm_1
                                                      JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                           ON rpm_1.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                                                AND rpm_1.""REPEM04_FechaHora_Pesaje"" >= mic.""REPEM07_Fecha_Inicio_Vigencia""
                                                                AND (mic.""REPEM07_Fecha_Fin_Vigencia"" IS NULL OR rpm_1.""REPEM04_FechaHora_Pesaje"" <= mic.""REPEM07_Fecha_Fin_Vigencia"")
                                             WHERE r.""REPEM09_Placa""::text = rpm_1.""REPEM04_Patente""::text
                                               AND r.""REPEM09_Fecha_Corta"" = rpm_1.""REPEM04_FechaHora_SalidaBase""::date
                                               AND mic.""REPEM07_Ruta_Larga"" = r.""REPEM09_Ruta_Larga""
                                             LIMIT 1) rpm ON true
                GROUP BY rpm.""REPEM04_Id"";

                create or replace view ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket""
                        (""NroTicket"", ""NUAP"", ""RutaCodigo"", ""Toneladas_Descuento"", ""Año"", ""Mes"") as
                SELECT max(rpm.""REPEM04_Id"")                              AS ""NroTicket"",
                       440705360                                          AS ""NUAP"",
                       614001                                             AS ""RutaCodigo"",
                       tde.""Toneladas_Total_Descuento""                    AS ""Toneladas_Descuento"",
                       EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")  AS ""Año"",
                       EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") AS ""Mes""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                              ON rpm.""REPEM04_RutaCodigo""::bigint = mic.""REPEM07_Numero_de_Microruta""
                                AND rpm.""REPEM04_FechaHora_Pesaje"" >= mic.""REPEM07_Fecha_Inicio_Vigencia""
                                AND (mic.""REPEM07_Fecha_Fin_Vigencia"" IS NULL OR rpm.""REPEM04_FechaHora_Pesaje"" <= mic.""REPEM07_Fecha_Fin_Vigencia"")
                         JOIN ""REPEM_Toneladas_Descuento_Excepcional"" tde
                              ON EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") = tde.""Año""::numeric AND
                                 EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") = tde.""Mes""::numeric
                         LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND dpm.""Año""::numeric =
                                                                                                                   EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                               dpm.""Mes""::numeric =
                                                                               EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                         LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                WHERE (rpm.""REPEM04_RutaCodigo""::text <> ALL
                       (ARRAY ['N9911111'::character varying::text, 'N0000133'::character varying::text, 'T9911111'::character varying::text, 'T0000133'::character varying::text, '3RECDON0312201F2'::character varying::text]))
                  AND mic.""REPEM07_Numero_de_Microruta"" = 614001
                  AND mic.""REPEM07_NUAP"" = 440705360
                  AND (
                          CASE
                              WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                  THEN tr.""Toneladas_Descuento_Medellin""
                              ELSE dpm.""Toneladas_Distribuidas""
                              END -
                          CASE
                              WHEN mic.""REPEM07_Porcentaje_Barrido"" <> 0 THEN
                                  CASE
                                      WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas"" -
                                                                               COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                      WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                          THEN tr.""Toneladas_Descuento_Medellin""
                                      ELSE dpm.""Toneladas_Distribuidas""
                                      END * (mic.""REPEM07_Porcentaje_Barrido"" / 100)::numeric
                              ELSE 0::numeric
                              END) >= tde.""Toneladas_Total_Descuento""
                GROUP BY (EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")), (EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")),
                         tde.""Toneladas_Total_Descuento"";
        
                CREATE OR REPLACE VIEW ""REPEM_Descuento_de_Barrido_Itagui_Por_Ticket""
                        (""Id_Ticket"", ""Id_Ticket_A_Descontar"", ""Valor_A_Descontar"", ""Valor_Descontable"", ""Fecha_Pesaje"",
                        ""Fecha_Pesaje_A_Descontar"", ""Numero_de_Microruta"", ""Numero_de_Microruta_A_Descontar"")
                AS
                WITH cte AS (SELECT rpm.""REPEM04_Id""                                                          AS ""Id_Ticket"",
                                    mic.""REPEM07_NUAP""                                                        AS ""NUAP"",
                                    mic.""REPEM07_Numero_de_Microruta""                                         AS ""Numero_de_Microruta"",
                                    mic.""REPEM07_Ruta_Larga""                                                  AS ""Ruta_Larga"",
                                    rpm.""REPEM04_FechaHora_Pesaje""                                            AS ""FechaHora_Pesaje"",
                                    CASE
                                        WHEN mic.""REPEM07_Porcentaje_Barrido""::numeric <> 0::numeric THEN (
                                            CASE
                                                WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas"" -
                                                                                         COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                    THEN tr.""Toneladas_Descuento_Medellin""
                                                ELSE tdha.""Toneladas_Resultantes""
                                                END * (mic.""REPEM07_Porcentaje_Barrido""::numeric / 100::numeric))::numeric(18, 3)
                                        ELSE 0::numeric
                                        END::numeric(18, 3) + COALESCE(tde.""Toneladas_Descuento"", 0::numeric) AS ""Calculo_Barrido""
                             FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                      JOIN  ""Reporting-Emvarias_07-Microrutas"" mic
                                            ON mic.""REPEM07_Numero_de_Microruta""::text = rpm.""REPEM04_RutaCodigo""::text
                                                AND rpm.""REPEM04_Fecha_de_Servicio"" >= mic.""REPEM07_Fecha_Inicio_Vigencia""
                                                AND (
                                                   mic.""REPEM07_Fecha_Fin_Vigencia"" IS NULL
                                                       OR rpm.""REPEM04_Fecha_de_Servicio"" <= mic.""REPEM07_Fecha_Fin_Vigencia""
                                                   )
                                      LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                            dpm.""Año""::numeric =
                                                                                            EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                            dpm.""Mes""::numeric =
                                                                                            EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                      LEFT JOIN ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" tde
                                                ON rpm.""REPEM04_Id"" = tde.""NroTicket"" AND
                                                   EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") = tde.""Año"" AND
                                                   EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") = tde.""Mes"" AND
                                                   mic.""REPEM07_Numero_de_Microruta"" = tde.""RutaCodigo"" AND
                                                   mic.""REPEM07_NUAP"" = tde.""NUAP""
                                      LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                                      LEFT JOIN ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                                ON tdha.""Id_Ticket"" = rpm.""REPEM04_Id"" AND tdha.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                   tdha.""Tipo_Compensacion"" = 'ORIGINAL'::text)
                SELECT cte.""Id_Ticket"",
                       cte2.""Id_Ticket""           AS ""Id_Ticket_A_Descontar"",
                       cte.""Calculo_Barrido""      AS ""Valor_A_Descontar"",
                       cte2.""Calculo_Barrido""     AS ""Valor_Descontable"",
                       cte.""FechaHora_Pesaje""     AS ""Fecha_Pesaje"",
                       cte2.""FechaHora_Pesaje""    AS ""Fecha_Pesaje_A_Descontar"",
                       cte.""Numero_de_Microruta"",
                       cte2.""Numero_de_Microruta"" AS ""Numero_de_Microruta_A_Descontar""
                FROM cte
                         JOIN LATERAL ( SELECT cte2_1.""Id_Ticket"",
                                               cte2_1.""NUAP"",
                                               cte2_1.""Numero_de_Microruta"",
                                               cte2_1.""Ruta_Larga"",
                                               cte2_1.""FechaHora_Pesaje"",
                                               cte2_1.""Calculo_Barrido""
                                        FROM cte cte2_1
                                        WHERE cte2_1.""Numero_de_Microruta"" = 690121
                                          AND cte2_1.""Calculo_Barrido"" > cte.""Calculo_Barrido""
                                          AND EXTRACT(year FROM cte.""FechaHora_Pesaje"") = EXTRACT(year FROM cte2_1.""FechaHora_Pesaje"")
                                          AND EXTRACT(month FROM cte.""FechaHora_Pesaje"") = EXTRACT(month FROM cte2_1.""FechaHora_Pesaje"")
                                        ORDER BY cte2_1.""FechaHora_Pesaje"", cte2_1.""Calculo_Barrido"" DESC
                                        LIMIT 1) cte2 ON TRUE
                WHERE cte.""NUAP"" = 440705360
                  AND cte.""Calculo_Barrido"" > 0::numeric;
        
                CREATE OR REPLACE VIEW ""REPEM_Toneladas_Rechazos_Por_Ticket""
                         (""Id_Ticket"", ""NUAP"", ""Toneladas_Rechazadas"", ""Fecha_Rechazo"", ""Num_ECA"") AS
                SELECT rpm.""REPEM04_Id""                      AS ""Id_Ticket"",
                    rpm.""REPEM07_NUAP""                    AS ""NUAP"",
                    r.""REPEM09_Toneladas""::numeric(18, 3) AS ""Toneladas_Rechazadas"",
                    r.""REPEM09_Fecha_Corta""               AS ""Fecha_Rechazo"",
                    r.""REPEM09_ECA""                       AS ""Num_ECA""
                FROM ""Reporting-Emvarias_09-Rechazos"" r
                         JOIN LATERAL ( SELECT irpm.""REPEM04_Id"",
                                               irpm.""REPEM04_Patente"",
                                               irpm.""REPEM04_EstadoServicio"",
                                               irpm.""REPEM04_GrupoTurno"",
                                               irpm.""REPEM04_TipoServicio"",
                                               irpm.""REPEM04_RutaCodigo"",
                                               irpm.""REPEM04_EsRefuerzo"",
                                               irpm.""REPEM04_Interno"",
                                               irpm.""REPEM04_PesoTotal"",
                                               irpm.""REPEM04_IdServicio"",
                                               irpm.""REPEM04_FechaHora_EntradaRuta"",
                                               irpm.""REPEM04_FechaHora_SalidaRuta"",
                                               irpm.""REPEM04_Observaciones"",
                                               irpm.""REPEM04_PesoTotal_Toneladas"",
                                               irpm.""REPEM04_FechaHora_Pesaje"",
                                               irpm.""REPEM04_Fecha_de_Servicio"",
                                               irpm.""REPEM04_FechaHora_InicioServicio"",
                                               irpm.""REPEM04_FechaHora_LlegadaBase"",
                                               irpm.""REPEM04_FechaHora_SalidaBase"",
                                               mic.""REPEM07_Numero_de_Microruta"",
                                               mic.""REPEM07_NUAP"",
                                               mic.""REPEM07_Numero_Ruta_Intendencia"",
                                               mic.""REPEM07_Ruta_Larga"",
                                               mic.""REPEM07_Porcentaje_Limpieza_Urbana"",
                                               mic.""REPEM07_Porcentaje_Barrido"",
                                               mic.""REPEM07_Porcentaje_No_Aforado"",
                                               mic.""REPEM07_Porcentaje_Residuos_Aprovechables""
                                        FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" irpm
                                            JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                            ON mic.""REPEM07_Numero_de_Microruta""::text = irpm.""REPEM04_RutaCodigo""::text
                                              AND irpm.""REPEM04_Fecha_de_Servicio"" >= mic.""REPEM07_Fecha_Inicio_Vigencia""
                                              AND (
                                                mic.""REPEM07_Fecha_Fin_Vigencia"" IS NULL
                                                    OR irpm.""REPEM04_Fecha_de_Servicio"" <= mic.""REPEM07_Fecha_Fin_Vigencia""
                                                )
                                        WHERE r.""REPEM09_Placa""::text = irpm.""REPEM04_Patente""::text
                                          AND r.""REPEM09_Fecha_Corta"" = irpm.""REPEM04_FechaHora_SalidaBase""::date
                                          AND mic.""REPEM07_Ruta_Larga"" = r.""REPEM09_Ruta_Larga""
                                        LIMIT 1) rpm ON true;
        
                create or replace view ""REPEM_Compensaciones_por_Redondeo""
                            (""Nro_Ticket_Ajustable"", ""Nro_Ticket_Ajustable_Original"", ""Fecha_Pesaje"", ""NUAP"", ""Area_Aprovechamiento"",
                             ""Peso_Ticket"", ""Toneladas_Ajuste"", ""Mes"", ""Año"")
                as
                WITH cte AS (WITH exclusions AS (WITH variables AS (SELECT 0.5 AS margen_tolerancia)
                                                 SELECT tc.""Nro_Ticket_Compensacion"",
                                                        sum(tc.""Suma_Agrupacion_Toneladas_Por_Compensar"") AS ""Suma_Agrupacion_Toneladas_Por_Compensar"",
                                                        max(tc.""Maximo_Toneladas_Compensables"")           AS ""Maximo_Toneladas_Compensables"",
                                                        (sum(tc.""Suma_Agrupacion_Toneladas_Por_Compensar"") + v.margen_tolerancia) <
                                                        max(tc.""Maximo_Toneladas_Compensables"")           AS ""Valido""
                                                 FROM ""REPEM_Tickets_Compensables"" tc,
                                                      variables v
                                                 GROUP BY tc.""Nro_Ticket_Compensacion"", v.margen_tolerancia),
                                  posibilities AS (SELECT rpm.""REPEM04_Id""               AS ""Nro_Ticket_Ajustable"",
                                                          rpm.""REPEM04_FechaHora_Pesaje"" AS ""Fecha_Pesaje"",
                                                          dct.""NUAP"",
                                                          dct.""Toneladas_Resultantes""    AS ""Peso_Ticket"",
                                                          rpm.""REPEM04_PesoTotal_Toneladas"",
                                                          dpm.""Toneladas_Desviacion""     AS ""Toneladas_Ajuste"",
                                                          ap.""REPEM02_Nombre""            AS ""Area_Aprovechamiento""
                                                   FROM ""REPEM_Detalle_Compensacion_Tickets"" dct
                                                            JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                                                 ON dct.""Id_Ticket"" = rpm.""REPEM04_Id""
                                                            JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                                 ON pb.""REPEM03_Id"" = rpm.""REPEM04_Id""
                                                            JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                                                 ON ap.""REPEM02_Codigo"" = dct.""NUAP""
                                                            JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = dct.""NUAP"" AND
                                                                                                             dpm.""Año""::numeric =
                                                                                                             EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                             dpm.""Mes""::numeric =
                                                                                                             EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                                             dct.""Toneladas_Resultantes"" >=
                                                                                                             dpm.""Toneladas_Desviacion""
                                                   WHERE pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                                     AND NOT (rpm.""REPEM04_Id"" IN (SELECT exclusions.""Nro_Ticket_Compensacion""
                                                                                   FROM exclusions
                                                                                   WHERE exclusions.""Valido"" = false))
                                                     AND rpm.""REPEM04_RutaCodigo""::text <> '614001'::text),
                                  ranked_posibilities AS (SELECT p.""Nro_Ticket_Ajustable"",
                                                                 p.""Fecha_Pesaje"",
                                                                 p.""NUAP"",
                                                                 p.""Peso_Ticket"",
                                                                 p.""REPEM04_PesoTotal_Toneladas"",
                                                                 p.""Toneladas_Ajuste"",
                                                                 p.""Area_Aprovechamiento"",
                                                                 row_number()
                                                                 OVER (PARTITION BY p.""Toneladas_Ajuste"", p.""Nro_Ticket_Ajustable"" ORDER BY p.""REPEM04_PesoTotal_Toneladas"" DESC) AS rn
                                                          FROM posibilities p
                                                          ORDER BY p.""REPEM04_PesoTotal_Toneladas"" DESC)
                             SELECT DISTINCT ON (rp.""NUAP"", (EXTRACT(year FROM rp.""Fecha_Pesaje"")), (EXTRACT(month FROM rp.""Fecha_Pesaje""))) COALESCE(tcpa.""Nro_Ticket_Compensacion"", rp.""Nro_Ticket_Ajustable"") AS ""Nro_Ticket_Ajustable"",
                                                                                                                                             CASE
                                                                                                                                                 WHEN tcpa.""Nro_Ticket_Compensacion"" IS NOT NULL
                                                                                                                                                     THEN rp.""Nro_Ticket_Ajustable""
                                                                                                                                                 ELSE NULL::bigint
                                                                                                                                                 END                                                             AS ""Nro_Ticket_Ajustable_Original"",
                                                                                                                                             rp.""Fecha_Pesaje"",
                                                                                                                                             EXTRACT(year FROM rp.""Fecha_Pesaje"")                                AS ""Año"",
                                                                                                                                             EXTRACT(month FROM rp.""Fecha_Pesaje"")                               AS ""Mes"",
                                                                                                                                             rp.""NUAP"",
                                                                                                                                             rp.""Peso_Ticket"",
                                                                                                                                             rp.""REPEM04_PesoTotal_Toneladas"",
                                                                                                                                             rp.""Toneladas_Ajuste"",
                                                                                                                                             rp.""Area_Aprovechamiento""
                             FROM ranked_posibilities rp
                                      LEFT JOIN ""REPEM_Tickets_Compensables"" tcpa
                                                ON tcpa.""Id_Ticket"" = rp.""Nro_Ticket_Ajustable"" AND rp.""NUAP"" = ANY(tcpa.""Areas_Por_Compensar"")
                             WHERE rp.rn = 1
                             ORDER BY rp.""NUAP"", (EXTRACT(year FROM rp.""Fecha_Pesaje"")), (EXTRACT(month FROM rp.""Fecha_Pesaje"")),
                                      rp.""REPEM04_PesoTotal_Toneladas"" DESC),
                     restas AS (SELECT cte.""Nro_Ticket_Ajustable"",
                                       max(cte.""Nro_Ticket_Ajustable_Original"") AS ""Nro_Ticket_Ajustable_Original"",
                                       max(cte.""Fecha_Pesaje"")                  AS max,
                                       '440405001'::bigint                      AS ""NUAP"",
                                       'Medellín'::character varying(20)        AS ""Area_Aprovechamiento"",
                                       sum(cte.""Peso_Ticket"")::numeric(18, 2)   AS ""Peso_Ticket"",
                                       - sum(cte.""Toneladas_Ajuste"")            AS ""Toneladas_Ajuste"",
                                       max(cte.""Mes"")                           AS ""Mes"",
                                       max(cte.""Año"")                           AS ""Año""
                                FROM cte
                                GROUP BY cte.""Nro_Ticket_Ajustable"")
                SELECT cte.""Nro_Ticket_Ajustable"",
                       cte.""Nro_Ticket_Ajustable_Original"",
                       cte.""Fecha_Pesaje"",
                       cte.""NUAP"",
                       cte.""Area_Aprovechamiento"",
                       cte.""Peso_Ticket""::numeric(18, 2) AS ""Peso_Ticket"",
                       dm.""Toneladas_Desviacion""         AS ""Toneladas_Ajuste"",
                       cte.""Mes"",
                       cte.""Año""
                FROM cte
                         JOIN ""REPEM_Distribuciones_de_Microrutas"" dm
                              ON dm.""NUAP"" = cte.""NUAP"" AND dm.""Año""::numeric = EXTRACT(year FROM cte.""Fecha_Pesaje"") AND
                                 dm.""Mes""::numeric = EXTRACT(month FROM cte.""Fecha_Pesaje"")
                UNION ALL
                SELECT restas.""Nro_Ticket_Ajustable"",
                       restas.""Nro_Ticket_Ajustable_Original"",
                       restas.max AS ""Fecha_Pesaje"",
                       restas.""NUAP"",
                       restas.""Area_Aprovechamiento"",
                       restas.""Peso_Ticket"",
                       restas.""Toneladas_Ajuste"",
                       restas.""Mes"",
                       restas.""Año""
                FROM restas;
        
                CREATE OR REPLACE VIEW ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones""
                        (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                        ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                        ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                        ""CE_REL_COMPENSACION_ID_TICKET"", ""CE_AJUSTE_DECIMAL"")
                AS
                WITH cte_comp AS (SELECT tdha.""Id_Ticket"",
                                      tdha.""NUAP"",
                                      tdha.""Toneladas_Resultantes"",
                                      tdha.""Tipo_Compensacion"",
                                      tdha.""Tipo_Compensacion"",
                                      rpm_1.""REPEM04_PesoTotal_Toneladas"",
                                      mic.""REPEM07_Numero_Ruta_Intendencia"" AS ""Numero_Ruta_Indendencia"",
                                      mic.""REPEM07_Ruta_Larga"",
                                      mic.""REPEM07_Porcentaje_No_Aforado"",
                                      mic.""REPEM07_Porcentaje_Barrido"",
                                      mic.""REPEM07_Porcentaje_Residuos_Aprovechables"",
                                      ap.""REPEM02_Nombre""                   AS ""Area_Aprovechamiento"",
                                      dm.""Porcentaje_Distribucion_Peaje"",
                                      pe.""REPEM06_Valor""::numeric           AS ""Valor_Peaje"",
                                      tr.""Toneladas_Descuento_Medellin"",
                                      tr.""Peaje_Descuento_Medellin""
                                  FROM ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                                           JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm_1
                                  ON tdha.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                      JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                      ON rpm_1.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                      mic.""REPEM07_NUAP"" = tdha.""NUAP""
                                      AND rpm_1.""REPEM04_Fecha_de_Servicio"" >= mic.""REPEM07_Fecha_Inicio_Vigencia""
                                       AND (
                                            mic.""REPEM07_Fecha_Fin_Vigencia"" IS NULL
                                        OR rpm_1.""REPEM04_Fecha_de_Servicio"" <= mic.""REPEM07_Fecha_Fin_Vigencia""
                                      )
                                      JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                      ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                                      LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                      LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dm
                                      ON dm.""Año""::numeric = EXTRACT(year FROM rpm_1.""REPEM04_FechaHora_Pesaje"") AND
                                      dm.""Mes""::numeric = EXTRACT(month FROM rpm_1.""REPEM04_FechaHora_Pesaje"") AND
                                      dm.""NUAP"" = tdha.""NUAP""
                                      LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe
                                      ON pe.""REPEM06_Placa""::text = rpm_1.""REPEM04_Patente""::text AND
                                      EXTRACT(year FROM pe.""REPEM06_Fecha_Validez"") =
                                      EXTRACT(year FROM rpm_1.""REPEM04_FechaHora_Pesaje"")
                                  WHERE tdha.""Tipo_Compensacion"" = ANY (ARRAY ['TOTAL'::text, 'PARCIAL'::text])),
                     calculated_comp AS (SELECT c.""Id_Ticket"",
                                             c.""NUAP"",
                                             c.""Toneladas_Resultantes"",
                                             c.""Tipo_Compensacion"",
                                             c.""Tipo_Compensacion_1"" AS ""Tipo_Compensacion"",
                                             c.""REPEM04_PesoTotal_Toneladas"",
                                             c.""Numero_Ruta_Indendencia"",
                                             c.""REPEM07_Ruta_Larga"",
                                             c.""REPEM07_Porcentaje_No_Aforado"",
                                             c.""REPEM07_Porcentaje_Barrido"",
                                             c.""REPEM07_Porcentaje_Residuos_Aprovechables"",
                                             c.""Area_Aprovechamiento"",
                                             c.""Porcentaje_Distribucion_Peaje"",
                                             c.""Valor_Peaje"",
                                             c.""Toneladas_Descuento_Medellin"",
                                             c.""Peaje_Descuento_Medellin"",
                                             CASE
                                                 WHEN c.""NUAP"" = 440405001 THEN c.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                                                   COALESCE(c.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                 WHEN c.""NUAP"" = 440705360 AND c.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                             THEN COALESCE(c.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                                    ELSE c.""Toneladas_Resultantes""
                                                                    END                 AS ""Calculo_Toneladas"",
                                                                CASE
                                                                    WHEN c.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                                                    ELSE (
                                                                        CASE
                                                                            WHEN c.""NUAP"" = 440405001 THEN c.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                                                           COALESCE(c.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                                            WHEN c.""NUAP"" = 440705360 AND c.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                                                THEN c.""Toneladas_Descuento_Medellin""
                                                                            ELSE c.""Toneladas_Resultantes""
                                                                            END *
                                                                        (c.""REPEM07_Porcentaje_Barrido""::numeric / 100::numeric))::numeric(18, 3)
                                                                    END::numeric(18, 3) AS ""Calculo_Barrido"",
                                                                ROUND(ROUND(CASE
                                                                    WHEN c.""Porcentaje_Distribucion_Peaje"" IS NOT NULL AND
                                                                         c.""Valor_Peaje"" IS NOT NULL AND c.""NUAP"" = '440705360'::bigint AND
                                                                         c.""REPEM07_Porcentaje_No_Aforado"" <> 0 THEN c.""Peaje_Descuento_Medellin""
                                                                    WHEN c.""Porcentaje_Distribucion_Peaje"" IS NOT NULL AND c.""Valor_Peaje"" IS NOT NULL
                                                                        THEN c.""Valor_Peaje"" * 2::numeric * c.""Porcentaje_Distribucion_Peaje""::numeric /
                                                                             100::numeric
                                                                    ELSE 0::numeric
                                                                    END::numeric, 1), 0)::numeric  AS ""Calculo_Peaje""
                                         FROM cte_comp c(""Id_Ticket"", ""NUAP"", ""Toneladas_Resultantes"", ""Tipo_Compensacion"",
                                             ""Tipo_Compensacion_1"", ""REPEM04_PesoTotal_Toneladas"",
                                             ""Numero_Ruta_Indendencia"", ""REPEM07_Ruta_Larga"",
                                             ""REPEM07_Porcentaje_No_Aforado"", ""REPEM07_Porcentaje_Barrido"",
                                             ""REPEM07_Porcentaje_Residuos_Aprovechables"", ""Area_Aprovechamiento"",
                                             ""Porcentaje_Distribucion_Peaje"", ""Valor_Peaje"", ""Toneladas_Descuento_Medellin"",
                                             ""Peaje_Descuento_Medellin""))
                SELECT cc.""NUAP""                                                              AS ""C1_NUAP"",
                    1                                                                      AS ""C2_TIPO_SITIO"",
                    720105237                                                              AS ""C3_NUSD"",
                    compensation.""REPEM04_Patente""                                         AS ""C4_PLACA"",
                    compensation.""REPEM04_FechaHora_Pesaje""::date                          AS ""C5_FECHA"",
                    compensation.""REPEM04_FechaHora_Pesaje""::time without time zone        AS ""C6_HORA"",
                    cc.""Numero_Ruta_Indendencia""                                           AS ""C7_NUMICRO"",
                    0                                                                      AS ""C8_TON_LIMP_URB"",
                    round(cc.""Calculo_Barrido"", 3)                                         AS ""C9_TON_BARRIDO"",
                    cc.""Calculo_Toneladas"" + COALESCE(cred.""Toneladas_Ajuste"", 0::numeric) AS ""C10_TONRESNA"",
                    COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                       AS ""C11_TONRECHAPR"",
                    CASE
                        WHEN cc.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN cc.""Calculo_Toneladas"" *
                                                                                                                           (cc.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric / 100::numeric)
                        ELSE 0::numeric
                        END                                                                AS ""C12_TONRESAPR"",
                    '1'::text                                                              AS ""C13_SISTEMA_MEDICION"",
                    cc.""Calculo_Peaje""                                                     AS ""C14_VLRPEAJ"",
                    rtc.""Nro_Ticket_Compensacion""                                          AS ""CE_ID_TICKET"",
                    cc.""Area_Aprovechamiento""                                              AS ""CE_NOMBRE_AREA"",
                    cc.""REPEM07_Ruta_Larga""                                                AS ""CE_RUTA_LARGA"",
                    rtc.""Maximo_Toneladas_Compensables""                                    AS ""CE_TON_TOTAL"",
                    true                                                                   AS ""CE_REL_COMPENSACION"",
                    rtc.""Id_Ticket""                                                        AS ""CE_REL_COMPENSACION_ID_TICKET"",
                    cred.* IS NOT NULL                                                     AS ""CE_AJUSTE_DECIMAL""
                FROM calculated_comp cc(""Id_Ticket"", ""NUAP"", ""Toneladas_Resultantes"", ""Tipo_Compensacion"", ""Tipo_Compensacion_1"",
                                        ""REPEM04_PesoTotal_Toneladas"", ""Numero_Ruta_Indendencia"", ""REPEM07_Ruta_Larga"",
                                        ""REPEM07_Porcentaje_No_Aforado"", ""REPEM07_Porcentaje_Barrido"",
                                        ""REPEM07_Porcentaje_Residuos_Aprovechables"", ""Area_Aprovechamiento"",
                                        ""Porcentaje_Distribucion_Peaje"", ""Valor_Peaje"", ""Toneladas_Descuento_Medellin"",
                                        ""Peaje_Descuento_Medellin"", ""Calculo_Toneladas"", ""Calculo_Barrido"", ""Calculo_Peaje"")
                         JOIN ""REPEM_Tickets_Compensables"" rtc ON cc.""Id_Ticket"" = rtc.""Id_Ticket""
                    JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" compensation
                    ON compensation.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                    LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                    ON cc.""Id_Ticket"" = tre.""Ticket_Asignable"" AND cc.""NUAP"" = 440405001
                    LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" cred
                    ON rtc.""Nro_Ticket_Compensacion"" = cred.""Nro_Ticket_Ajustable"" AND cc.""NUAP"" = cred.""NUAP"" AND
                    rtc.""Id_Ticket"" = cred.""Nro_Ticket_Ajustable_Original"";
        
                CREATE OR REPLACE VIEW ""REPEM_Reporte_SUI_Recolecciones_F14""
                        (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                        ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                        ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                        ""CE_REL_COMPENSACION_ID_TICKET"", ""CE_AJUSTE_DECIMAL"")
                AS
                WITH cte AS (SELECT rpm.""REPEM04_Id""                                     AS ""Id_Ticket"",
                                 mic.""REPEM07_NUAP""                                   AS ""NUAP"",
                                 CASE
                                     WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                                                 COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                     WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                 THEN tr.""Toneladas_Descuento_Medellin""
                                                        ELSE tdha.""Toneladas_Resultantes""
                                                        END                                              AS ""Calculo_Toneladas"",
                                                    CASE
                                                        WHEN mic.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                                        ELSE (
                                                            CASE
                                                                WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas"" -
                                                                                                         COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                                WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                                    THEN tr.""Toneladas_Descuento_Medellin""
                                                                ELSE tdha.""Toneladas_Resultantes""
                                                                END * (mic.""REPEM07_Porcentaje_Barrido""::numeric / 100::numeric))::numeric(18, 3)
                                                        END::numeric(18, 3)                              AS ""Calculo_Barrido"",
                                                    ROUND(ROUND(CASE
                                                        WHEN pe.* IS NULL THEN 0::numeric
                                                        WHEN mic.""REPEM07_NUAP"" = 440405001 THEN 0::numeric
                                                        WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                                            THEN 0::numeric
                                                        ELSE (pe.""REPEM06_Valor"" * 2::numeric *
                                                              (COALESCE(dpm.""Porcentaje_Distribucion_Peaje"", 100::numeric) /
                                                               100::numeric))::numeric
                                                        END::numeric, 1), 0)::numeric                      AS ""Calculo_Peaje"",
                                                    COALESCE(tdha.""Tipo_Compensacion"", 'ORIGINAL'::text) AS ""Tipo_Compensacion""
                             FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                 JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                    ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                     AND rpm.""REPEM04_Fecha_de_Servicio"" >= mic.""REPEM07_Fecha_Inicio_Vigencia""
                                     AND (
                                         mic.""REPEM07_Fecha_Fin_Vigencia"" IS NULL
                                         OR rpm.""REPEM04_Fecha_de_Servicio"" <= mic.""REPEM07_Fecha_Fin_Vigencia""
                                         )
                                 LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                 dpm.""Año""::numeric =
                                 EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                 dpm.""Mes""::numeric =
                                 EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                 LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe
                                 ON pe.""REPEM06_Placa""::text = rpm.""REPEM04_Patente""::text AND
                                 EXTRACT(year FROM pe.""REPEM06_Fecha_Validez"") =
                                 EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                 LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                                 LEFT JOIN ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                 ON tdha.""Id_Ticket"" = rpm.""REPEM04_Id"" AND tdha.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                 tdha.""Tipo_Compensacion"" = 'ORIGINAL'::text),
                     aggregated_compensations AS (SELECT 0                                                                           AS ""Id_Ticket"",
                                                      ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"",
                                                      sum(""REPEM_Tickets_Compensables"".""Suma_Agrupacion_Toneladas_Por_Compensar"") AS ""Suma_Agrupacion_Toneladas_Por_Compensar""
                                                  FROM ""REPEM_Tickets_Compensables""
                                                  GROUP BY ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"")
                SELECT mic.""REPEM07_NUAP""                                         AS ""C1_NUAP"",
                    1                                                          AS ""C2_TIPO_SITIO"",
                    720105237                                                  AS ""C3_NUSD"",
                    rpm.""REPEM04_Patente""                                      AS ""C4_PLACA"",
                    rpm.""REPEM04_FechaHora_Pesaje""::date                       AS ""C5_FECHA"",
                    rpm.""REPEM04_FechaHora_Pesaje""::time without time zone     AS ""C6_HORA"",
                    mic.""REPEM07_Numero_Ruta_Intendencia""                      AS ""C7_NUMICRO"",
                    0                                                          AS ""C8_TON_LIMP_URB"",
                    round(bs.""Calculo_Barrido"" + COALESCE(tde.""Toneladas_Descuento"", 0::numeric), 3) -
                    COALESCE(dbi.""Valor_A_Descontar"", 0::numeric)              AS ""C9_TON_BARRIDO"",
                    CASE
                        WHEN mic.""REPEM07_Numero_de_Microruta"" = 614001 THEN round(
                                bs.""Calculo_Toneladas"" - COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                        ELSE round(bs.""Calculo_Toneladas"" - bs.""Calculo_Barrido"" - COALESCE(tde.""Toneladas_Descuento"", 0::numeric) -
                                                                                  COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                        END + COALESCE(compred.""Toneladas_Ajuste"", 0::numeric) AS ""C10_TONRESNA"",
                    COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)           AS ""C11_TONRECHAPR"",
                    CASE
                        WHEN mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN bs.""Calculo_Toneladas"" *
                                                                                                                            (mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric /
                                                                                                                             100::numeric)
                        ELSE 0::numeric
                        END                                                    AS ""C12_TONRESAPR"",
                    '1'::text                                                  AS ""C13_SISTEMA_MEDICION"",
                    bs.""Calculo_Peaje""                                         AS ""C14_VLRPEAJ"",
                    rpm.""REPEM04_Id""                                           AS ""CE_ID_TICKET"",
                    ap.""REPEM02_Nombre""                                        AS ""CE_NOMBRE_AREA"",
                    mic.""REPEM07_Ruta_Larga""                                   AS ""CE_RUTA_LARGA"",
                    rpm.""REPEM04_PesoTotal_Toneladas""                          AS ""CE_TON_TOTAL"",
                    rtc.* IS NOT NULL                                          AS ""CE_REL_COMPENSACION"",
                    rtc.""Id_Ticket""                                            AS ""CE_REL_COMPENSACION_ID_TICKET"",
                    compred.* IS NOT NULL                                      AS ""CE_AJUSTE_DECIMAL""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                         JOIN cte bs ON bs.""Calculo_Toneladas"" IS NOT NULL AND rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                    JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                        ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                        mic.""REPEM07_NUAP"" = bs.""NUAP""
                        AND rpm.""REPEM04_Fecha_de_Servicio"" >= mic.""REPEM07_Fecha_Inicio_Vigencia""
                        AND (
                        mic.""REPEM07_Fecha_Fin_Vigencia"" IS NULL
                        OR rpm.""REPEM04_Fecha_de_Servicio"" <= mic.""REPEM07_Fecha_Fin_Vigencia""
                        )
                    JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                    LEFT JOIN ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" tde ON rpm.""REPEM04_Id"" = tde.""NroTicket"" AND
                    EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                    tde.""Año"" AND
                    EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                    tde.""Mes"" AND
                    mic.""REPEM07_Numero_de_Microruta"" =
                    tde.""RutaCodigo"" AND
                    mic.""REPEM07_NUAP"" = tde.""NUAP""
                    LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                    ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mic.""REPEM07_NUAP"" = 440405001
                    LEFT JOIN aggregated_compensations rtc ON rpm.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                    LEFT JOIN ""REPEM_Descuento_de_Barrido_Itagui_Por_Ticket"" dbi ON dbi.""Id_Ticket_A_Descontar"" = rpm.""REPEM04_Id""
                    LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" compred
                    ON compred.""Nro_Ticket_Ajustable"" = rpm.""REPEM04_Id"" AND compred.""NUAP"" = mic.""REPEM07_NUAP""
                UNION ALL
                SELECT ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C1_NUAP"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C2_TIPO_SITIO"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C3_NUSD"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C4_PLACA"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C5_FECHA"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C6_HORA"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C7_NUMICRO"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C8_TON_LIMP_URB"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C9_TON_BARRIDO"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C10_TONRESNA"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C11_TONRECHAPR"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C12_TONRESAPR"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C13_SISTEMA_MEDICION"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C14_VLRPEAJ"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_ID_TICKET"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_NOMBRE_AREA"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_RUTA_LARGA"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_TON_TOTAL"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_REL_COMPENSACION"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_REL_COMPENSACION_ID_TICKET"",
                    ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_AJUSTE_DECIMAL""
                FROM ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"";
            ");
        }
        
        private static void RecreateMaterializedViewsWithChangesAndSchedule(MigrationBuilder migrationBuilder)
        {
            //F14 Aditivos
            migrationBuilder.Sql(@"
                create materialized view ""REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos""
                            (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                             ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                             ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                             ""CE_REL_COMPENSACION_ID_TICKET"")
                as
                SELECT f14.""C1_NUAP"",
                       f14.""C2_TIPO_SITIO"",
                       f14.""C3_NUSD""::character varying(10) AS ""C3_NUSD"",
                       f14.""C4_PLACA"",
                       f14.""C5_FECHA"",
                       f14.""C6_HORA"",
                       f14.""C7_NUMICRO"",
                       f14.""C8_TON_LIMP_URB""::numeric       AS ""C8_TON_LIMP_URB"",
                       f14.""C9_TON_BARRIDO"",
                       f14.""C10_TONRESNA"",
                       f14.""C11_TONRECHAPR"",
                       f14.""C12_TONRESAPR"",
                       f14.""C13_SISTEMA_MEDICION""::integer  AS ""C13_SISTEMA_MEDICION"",
                       f14.""C14_VLRPEAJ"",
                       f14.""CE_ID_TICKET"",
                       f14.""CE_NOMBRE_AREA"",
                       f14.""CE_RUTA_LARGA"",
                       f14.""CE_TON_TOTAL""::numeric          AS ""CE_TON_TOTAL"",
                       f14.""CE_REL_COMPENSACION"",
                       f14.""CE_REL_COMPENSACION_ID_TICKET""
                FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" f14
                UNION ALL
                SELECT clu.""C1_NUAP"",
                       clu.""C2_TIPO_SITIO"",
                       clu.""C3_NUSD""::character varying(10) AS ""C3_NUSD"",
                       clu.""C4_PLACA"",
                       clu.""C5_FECHA"",
                       clu.""C6_HORA"",
                       clu.""C7_NUMICRO"",
                       clu.""C8_TON_LIMP_URB""::numeric       AS ""C8_TON_LIMP_URB"",
                       clu.""C9_TON_BARRIDO""::numeric        AS ""C9_TON_BARRIDO"",
                       clu.""C10_TONRESNA""::numeric          AS ""C10_TONRESNA"",
                       clu.""C11_TONRECHAPR""::numeric        AS ""C11_TONRECHAPR"",
                       clu.""C12_TONRESAPR""::numeric         AS ""C12_TONRESAPR"",
                       clu.""C13_SISTEMA_MEDICION"",
                       clu.""C14_VLRPEAJ""::numeric           AS ""C14_VLRPEAJ"",
                       NULL::bigint                         AS ""CE_ID_TICKET"",
                       'CLUS - CARGADO'::character varying  AS ""CE_NOMBRE_AREA"",
                       clu.""CE_RUTA_LARGA"",
                       clu.""CE_TON_TOTAL""::numeric          AS ""CE_TON_TOTAL"",
                       NULL::boolean                        AS ""CE_REL_COMPENSACION"",
                       NULL::bigint                         AS ""CE_REL_COMPENSACION_ID_TICKET""
                FROM ""Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU"" clu
                UNION ALL
                SELECT apr.""C1_NUAP"",
                       apr.""C2_TIPO_SITIO"",
                       apr.""C3_NUSD""::character varying(10)           AS ""C3_NUSD"",
                       apr.""C4_PLACA"",
                       apr.""C5_FECHA"",
                       apr.""C6_HORA"",
                       apr.""C7_NUMICRO"",
                       apr.""C8_TON_LIMP_URB""::numeric                 AS ""C8_TON_LIMP_URB"",
                       apr.""C9_TON_BARRIDO""::numeric                  AS ""C9_TON_BARRIDO"",
                       apr.""C10_TONRESNA""::numeric                    AS ""C10_TONRESNA"",
                       apr.""C11_TONRECHAPR""::numeric                  AS ""C11_TONRECHAPR"",
                       apr.""C12_TONRESAPR""::numeric                   AS ""C12_TONRESAPR"",
                       apr.""C13_SISTEMA_MEDICION"",
                       apr.""C14_VLRPEAJ""::numeric                     AS ""C14_VLRPEAJ"",
                       NULL::bigint                                   AS ""CE_ID_TICKET"",
                       'APROVECHAMIENTO - CARGADO'::character varying AS ""CE_NOMBRE_AREA"",
                       apr.""CE_RUTA_LARGA"",
                       apr.""CE_TON_TOTAL""::numeric                    AS ""CE_TON_TOTAL"",
                       NULL::boolean                                  AS ""CE_REL_COMPENSACION"",
                       NULL::bigint                                   AS ""CE_REL_COMPENSACION_ID_TICKET""
                FROM ""Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento"" apr;
            ");
            
            //Se crea la clave primaria
            migrationBuilder.Sql(@"
                CREATE UNIQUE INDEX ""idx_REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos_view_pk""
                ON ""REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos"" (""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_REL_COMPENSACION_ID_TICKET"");
            ");
            
            //F34
            migrationBuilder.Sql(@"
                            CREATE MATERIALIZED VIEW ""REPEM_Reporte_SUI_Recolecciones_F34""
                    (""C1_NUSD"", ""C2_TIPO_ORIGEN"", ""C3_NUSITIO_ORI"", ""C4_NOMBRE_EMPRESA"", ""C5_NIT_EMPRESA"", ""C6_COD_DANE_ORI"",
                    ""C7_PLACA"", ""C8_FECHA_INGRESO"", ""C9_FECHA_SALIDA"", ""C10_HORA_INGRESO"", ""C11_HORA_SALIDA"", ""C12_TONELADAS"",
                    ""CE_ID_TICKET"", ""CE_VALOR_TICKET_LEGACY"", ""CE_VALOR_TON_TICKET_LEGACY"", ""CE_TONELADAS_RECHAZADAS"",
                    ""CE_EXISTE_EN_F14"", ""CE_NIT_EMPRESA"", ""CE_NUAP"", ""CE_FECHA_FILTRO"")
            AS
            WITH combined_data AS (SELECT 720105237                                                                              AS ""C1_NUSD"",
                                       CASE
                                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                                           ELSE NULL::bigint
                                           END                                                                                AS ""C2_TIPO_ORIGEN"",
                                       CASE
                                           WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                                           ELSE r14.""C1_NUAP""
                                           END                                                                                AS ""C3_NUSITIO_ORI"",
                                       CASE
                                           WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                           ELSE cli.""REPEM08_Nombre_Completo""
                                           END                                                                                AS ""C4_NOMBRE_EMPRESA"",
                                       CASE
                                           WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                           ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                           END                                                                                AS ""C5_NIT_EMPRESA"",
                                       CASE
                                           WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                           ELSE mun.""REPEM01_Codigo""
                                           END                                                                                AS ""C6_COD_DANE_ORI"",
                                       pb.""REPEM03_Patente""                                                                   AS ""C7_PLACA"",
                                       pb.""REPEM03_Fecha_de_entrada""::date                                                    AS ""C8_FECHA_INGRESO"",
                                       pb.""REPEM03_Fecha_de_egreso""::date                                                     AS ""C9_FECHA_SALIDA"",
                                       pb.""REPEM03_Fecha_de_entrada""::time without time zone                                  AS ""C10_HORA_INGRESO"",
                                       pb.""REPEM03_Fecha_de_egreso""::time without time zone                                   AS ""C11_HORA_SALIDA"",
                                       r14.""C9_TON_BARRIDO"" + r14.""C10_TONRESNA"" -
                                                                      COALESCE(r14.""C11_TONRECHAPR"", 0::numeric)                                             AS ""C12_TONELADAS"",
                                       pb.""REPEM03_Id""                                                                        AS ""CE_ID_TICKET"",
                                       COALESCE(tct.""REPEM10_Valor"", 0::numeric)                                              AS ""CE_VALOR_TICKET_LEGACY"",
                                       COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric)                     AS ""CE_VALOR_TON_TICKET_LEGACY"",
                                       COALESCE(r14.""C11_TONRECHAPR"", 0::numeric)                                             AS ""CE_TONELADAS_RECHAZADAS"",
                                       true                                                                                   AS ""CE_EXISTE_EN_F14"",
                                       pb.""REPEM03_Nro_Identificacion_Tributaria""                                             AS ""CE_NIT_EMPRESA"",
                                       r14.""C1_NUAP""                                                                          AS ""CE_NUAP"",
                                       r14.""C5_FECHA""                                                                         AS ""CE_FECHA_FILTRO""
                                   FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                                                                        JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                   ON pb.""REPEM03_Id"" = r14.""CE_ID_TICKET"" AND
                                       pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                       LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                       ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                       LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                       ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                                       LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                       ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND
                                       pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                   UNION ALL
                                   SELECT 720105237                                                          AS ""C1_NUSD"",
                                       CASE
                                       WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                                       WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                                       WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                                       WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                                       ELSE NULL::bigint
                                       END                                                            AS ""C2_TIPO_ORIGEN"",
                                       CASE
                                       WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                                       ELSE pb.""REPEM03_Nro_Unico_Area_Prestacion""
                                       END                                                            AS ""C3_NUSITIO_ORI"",
                                       CASE
                                       WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                       ELSE cli.""REPEM08_Nombre_Completo""
                                       END                                                            AS ""C4_NOMBRE_EMPRESA"",
                                       CASE
                                       WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                       ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                       END                                                            AS ""C5_NIT_EMPRESA"",
                                       CASE
                                       WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                       ELSE mun.""REPEM01_Codigo""
                                       END                                                            AS ""C6_COD_DANE_ORI"",
                                       pb.""REPEM03_Patente""                                               AS ""C7_PLACA"",
                                       pb.""REPEM03_Fecha_de_entrada""::date                                AS ""C8_FECHA_INGRESO"",
                                       pb.""REPEM03_Fecha_de_egreso""::date                                 AS ""C9_FECHA_SALIDA"",
                                       pb.""REPEM03_Fecha_de_entrada""::time without time zone              AS ""C10_HORA_INGRESO"",
                                       pb.""REPEM03_Fecha_de_egreso""::time without time zone               AS ""C11_HORA_SALIDA"",
                                       tct.""REPEM10_Valor""::numeric / 1000::numeric -
                                       COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                   AS ""C12_TONELADAS"",
                                       pb.""REPEM03_Id""                                                    AS ""CE_ID_TICKET"",
                                       COALESCE(tct.""REPEM10_Valor"", 0::numeric)                          AS ""CE_VALOR_TICKET_LEGACY"",
                                       COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                                       COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                   AS ""CE_TONELADAS_RECHAZADAS"",
                                       false                                                              AS ""CE_EXISTE_EN_F14"",
                                       pb.""REPEM03_Nro_Identificacion_Tributaria""                         AS ""CE_NIT_EMPRESA"",
                                       pb.""REPEM03_Nro_Unico_Area_Prestacion""                             AS ""CE_NUAP"",
                                       pb.""REPEM03_Fecha_de_entrada""::date                                AS ""CE_FECHA_FILTRO""
                                   FROM ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                       LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                   ON pb.""REPEM03_Id"" = tct.""REPEM10_Id""
                                       LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                       ON pb.""REPEM03_Id"" = tre.""Ticket_Asignable"" AND
                                       pb.""REPEM03_Nro_Unico_Area_Prestacion"" = 440405001
                                       LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                       ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                       LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                       ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                                   WHERE pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                     AND NOT (pb.""REPEM03_Id"" IN (SELECT DISTINCT rpm.""REPEM04_Id"" AS id
                                       FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                       JOIN  ""Reporting-Emvarias_07-Microrutas"" mic
                                       ON mic.""REPEM07_Numero_de_Microruta""::text = rpm.""REPEM04_RutaCodigo""::text
                                     AND rpm.""REPEM04_Fecha_de_Servicio"" >= mic.""REPEM07_Fecha_Inicio_Vigencia""
                                     AND (
                                       mic.""REPEM07_Fecha_Fin_Vigencia"" IS NULL
                                      OR rpm.""REPEM04_Fecha_de_Servicio"" <= mic.""REPEM07_Fecha_Fin_Vigencia"")
                                       ))
                                   UNION ALL
                                   SELECT 720105237                                                          AS ""C1_NUSD"",
                                       '3'::bigint                                                        AS ""C2_TIPO_ORIGEN"",
                                       trept.""Num_ECA""                                                    AS ""C3_NUSITIO_ORI"",
                                       CASE
                                       WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                       ELSE cli.""REPEM08_Nombre_Completo""
                                       END                                                            AS ""C4_NOMBRE_EMPRESA"",
                                       CASE
                                       WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                       ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                       END                                                            AS ""C5_NIT_EMPRESA"",
                                       CASE
                                       WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                       ELSE mun.""REPEM01_Codigo""
                                       END                                                            AS ""C6_COD_DANE_ORI"",
                                       pb.""REPEM03_Patente""                                               AS ""C7_PLACA"",
                                       pb.""REPEM03_Fecha_de_entrada""::date                                AS ""C8_FECHA_INGRESO"",
                                       pb.""REPEM03_Fecha_de_egreso""::date                                 AS ""C9_FECHA_SALIDA"",
                                       pb.""REPEM03_Fecha_de_entrada""::time without time zone              AS ""C10_HORA_INGRESO"",
                                       pb.""REPEM03_Fecha_de_egreso""::time without time zone               AS ""C11_HORA_SALIDA"",
                                       COALESCE(trept.""Toneladas_Rechazadas""::numeric, 0::numeric)        AS ""C12_TONELADAS"",
                                       pb.""REPEM03_Id""                                                    AS ""CE_ID_TICKET"",
                                       COALESCE(tct.""REPEM10_Valor"", 0::numeric)                          AS ""CE_VALOR_TICKET_LEGACY"",
                                       COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                                       0                                                                  AS ""CE_TONELADAS_RECHAZADAS"",
                                       true                                                               AS ""CE_EXISTE_EN_F14"",
                                       pb.""REPEM03_Nro_Identificacion_Tributaria""                         AS ""CE_NIT_EMPRESA"",
                                       440405001                                                          AS ""CE_NUAP"",
                                       r14.""C5_FECHA""                                                     AS ""CE_FECHA_FILTRO""
                                   FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                                       JOIN ""REPEM_Toneladas_Rechazos_Por_Ticket"" trept
                                   ON r14.""CE_ID_TICKET"" = trept.""Id_Ticket"" AND trept.""NUAP"" = r14.""C1_NUAP""
                                       JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                       ON pb.""REPEM03_Id"" = r14.""CE_ID_TICKET"" AND
                                       pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                       LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                       ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                       LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                       ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                                       LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                       ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND
                                       pb.""REPEM03_Fecha_de_cancelacion"" IS NULL)
            SELECT ""C1_NUSD"",
                ""C2_TIPO_ORIGEN"",
                ""C3_NUSITIO_ORI"",
                ""C4_NOMBRE_EMPRESA"",
                ""C5_NIT_EMPRESA"",
                ""C6_COD_DANE_ORI"",
                ""C7_PLACA"",
                ""C8_FECHA_INGRESO"",
                ""C9_FECHA_SALIDA"",
                ""C10_HORA_INGRESO"",
                ""C11_HORA_SALIDA"",
                COALESCE(""C12_TONELADAS"", 0::numeric) AS ""C12_TONELADAS"",
                ""CE_ID_TICKET"",
                ""CE_VALOR_TICKET_LEGACY"",
                ""CE_VALOR_TON_TICKET_LEGACY"",
                ""CE_TONELADAS_RECHAZADAS"",
                ""CE_EXISTE_EN_F14"",
                ""CE_NIT_EMPRESA"",
                ""CE_NUAP"",
                ""CE_FECHA_FILTRO""
            FROM combined_data;
            ");
            
            //Se crea la clave primaria
            migrationBuilder.Sql(@"
                CREATE UNIQUE INDEX ""idx_REPEM_Reporte_SUI_Recolecciones_F34_view_pk""
                ON ""REPEM_Reporte_SUI_Recolecciones_F34"" (""CE_ID_TICKET"", ""CE_NUAP"", ""C6_COD_DANE_ORI"");
            ");
            
            //Scheduled Jobs
            migrationBuilder.Sql(@"
                SELECT cron.schedule(
                   'JOB_REPEM_Reportes_SUI_F14_F34',
                   '0 * * * *',
                   'REFRESH MATERIALIZED VIEW CONCURRENTLY REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos; REFRESH MATERIALIZED VIEW CONCURRENTLY REPEM_Reporte_SUI_Recolecciones_F34;'
                );
            ");
        }
    }
}
