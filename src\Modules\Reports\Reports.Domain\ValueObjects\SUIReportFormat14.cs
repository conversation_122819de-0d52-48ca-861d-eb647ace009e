using Reports.Domain.Common;
using Reports.Domain.Constants;

namespace Reports.Domain.ValueObjects;

public class SUIReportFormat14
{
    public string C1_NUAP { get; init; }
    public int C2_TIPO_SITIO { get; init; }
    public string C3_NUSD { get; init; }
    public string C4_PLACA { get; init; }
    public string C5_FECHA { get; init; }
    public string C6_HORA { get; init; }
    public long C7_NUMICRO { get; init; }
    public decimal C8_TON_LIMP_URB { get; init; }
    public decimal C9_TON_BARRIDO { get; init; }
    public decimal C10_TONRESNA { get; init; }
    public decimal C11_TONRECHAPR { get; init; }
    public decimal C12_TONRESAPR { get; init; }
    public int C13_SISTEMA_MEDICION { get; init; }
    public decimal C14_VLRPEAJ { get; init; }

    protected SUIReportFormat14(long NUAP, DestinationType DestinationType, string DestinationCode,
        string LicensePlate, DateOnly VehicleArrival, TimeOnly VehicleArrivalTime,
        long MicrorouteId, decimal UrbanCleaningTons, decimal SweepingTons,
        decimal NonRecyclableTons, decimal RejectedTons, decimal RecyclableTons,
        int MeasuringUnit, decimal Toll)
    {
        C1_NUAP = FormattedNUAP(NUAP);
        C2_TIPO_SITIO = (int)DestinationType;
        C3_NUSD = DestinationCode;
        C4_PLACA = LicensePlate;
        C5_FECHA = SUIFormats.FormattedDate(VehicleArrival);
        C6_HORA = SUIFormats.FormattedTime(VehicleArrivalTime);
        C7_NUMICRO = MicrorouteId;
        C8_TON_LIMP_URB = UrbanCleaningTons;
        C9_TON_BARRIDO = SweepingTons;
        C10_TONRESNA = NonRecyclableTons;
        C11_TONRECHAPR = RejectedTons;
        C12_TONRESAPR = RecyclableTons;
        C13_SISTEMA_MEDICION = MeasuringUnit;
        C14_VLRPEAJ = Toll;
    }
    
    private static string? FormattedNUAP(long nuap) => 
        nuap
            .ToString()
            .PadLeft(12, '0');
    
    public static SUIReportFormat14 Create(long NUAP, DestinationType DestinationType, string DestinationCode,
        string LicensePlate, DateOnly VehicleArrival, TimeOnly VehicleArrivalTime,
        long MicrorouteId, decimal UrbanCleaningTons, decimal SweepingTons,
        decimal NonRecyclableTons, decimal RejectedTons, decimal RecyclableTons,
        int MeasuringUnit, decimal Toll) =>
        new(NUAP, DestinationType, DestinationCode, LicensePlate, VehicleArrival, VehicleArrivalTime,
            MicrorouteId, UrbanCleaningTons, SweepingTons, NonRecyclableTons, RejectedTons, RecyclableTons,
            MeasuringUnit, Toll);
}