﻿using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Domain.Entities;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;
using Reports.Domain.Entities;
using Reports.Domain.Repositories;

namespace Reports.Infrastructure.Data.EFCore.Repositories;

public class ReportFormat34Repository : Repository<ReportFormat34, string, ReportsDbContext>, IReportFormat34Repository
{
    public ReportFormat34Repository(IDbContextProvider<ReportsDbContext> dbContextProvider, ICacheService cacheService) : base(dbContextProvider, cacheService) { }
    
    public async Task<PaginatedResult<ReportFormat34>> GetFilteredReportAsync(Expression<Func<ReportFormat34, bool>> predicate,
        CancellationToken cancellationToken,
        bool isPaginated = true,
        int pageNumber = 1,
        int pageSize = 25)
    {
        var query = _context.ReportsFormat34
            .AsQueryable()
            .AsNoTracking()
            .Where(predicate);
        
        if (!isPaginated)
            return new PaginatedResult<ReportFormat34> { Results = await query.ToListAsync(cancellationToken) };
        
        var totalCount = await query
            .CountAsync(cancellationToken);
        
        var result = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken: cancellationToken);

        return new PaginatedResult<ReportFormat34>
        {
            Results = result,
            TotalRecords = totalCount,
            PageSize = pageSize,
            PageNumber = pageNumber
        };
    }
}