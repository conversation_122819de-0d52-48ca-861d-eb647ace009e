using FluentValidation;

namespace Shared.Application.Common.ValidationExtensions;

public static class EnumValidator
{
    public static IRuleBuilderOptions<T, string?> IsValidEnum<T>(this IRuleBuilder<T, string?> ruleBuilder, Type type)
    {
        return ruleBuilder.Must(value =>
        {
            if (string.IsNullOrEmpty(value))
                return false;

            if (Enum.TryParse(type, value, true, out var enumValue) && Enum.IsDefined(type, enumValue!))
                return true;

            return false;
        });
    }
}