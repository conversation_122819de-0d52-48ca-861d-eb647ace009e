# ASP.NET Core (.NET Framework)
# Build and test ASP.NET Core projects targeting the full .NET Framework.
# Add steps that publish symbols, save build artifacts, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core

trigger:
- none

pool:
  vmImage: 'windows-latest'

variables:
  project: '**/src/Mercury.Api/Mercury.Api.csproj'
  solution: Mercury.sln
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'

steps:

# Restauramos los paquetes NuGet
- task: NuGetCommand@2
  displayName: dotnet restore
  inputs:
    command: 'restore'
    restoreSolution: '$(solution)'
    feedsToUse: 'config'
    nugetConfigPath: 'NuGet.Config'

# Ejecutamos el DotNet Publish
- task: DotNetCoreCLI@2
  displayName: dotnet publish -c Release -o ArtifactDirectory
  inputs:
    command: 'publish'
    publishWebProjects: false
    projects: '$(project)'
    arguments: '--no-restore -c Release -o $(Build.ArtifactStagingDirectory)'
    
    
# - task: VSTest@2
#   inputs:
#     platform: '$(buildPlatform)'
#     configuration: '$(buildConfiguration)'

# Extraemos los archivos zippeados en la carpeta src del WorkingDirectory del Artifact
- task: ExtractFiles@1
  inputs:
    archiveFilePatterns: '$(Build.ArtifactStagingDirectory)/*.zip'
    destinationFolder: '$(Build.ArtifactStagingDirectory)/src'
    cleanDestinationFolder: true
    overwriteExistingFiles: true
  displayName: 'Extraer archivos .zip y moverlos a src'

#  Publicamos el artifact
- task: PublishBuildArtifacts@1
  displayName: Publish Artifact - Web Api
  inputs:
    PathtoPublish: '$(Build.ArtifactStagingDirectory)\src'
    ArtifactName: 'api'
    publishLocation: 'Container'