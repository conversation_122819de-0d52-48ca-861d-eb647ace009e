using System.Linq.Expressions;
using Orion.SharedKernel.Domain.Entities;
using Orion.SharedKernel.Domain.Repositories.Entities;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Domain.Repositories;

/// <summary>
/// Repository interface for HistoricalAuditColumns entity.
/// Provides methods for storing and retrieving audit records.
/// </summary>
public interface IHistoricalAuditColumnsRepository : IRepository<HistoricalAuditColumns, long>
{
    /// <summary>
    /// Gets audit records for a specific entity
    /// </summary>
    /// <param name="entityId">The ID of the entity</param>
    /// <param name="tableName">The table/entity name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of audit records for the entity</returns>
    Task<IEnumerable<HistoricalAuditColumns>> GetAuditRecordsForEntityAsync(
        string entityId, 
        string tableName, 
        CancellationToken cancellationToken);

    /// <summary>
    /// Gets audit records for multiple entities
    /// </summary>
    /// <param name="entityIds">Collection of entity IDs</param>
    /// <param name="tableName">The table/entity name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of audit records for the entities</returns>
    Task<IEnumerable<HistoricalAuditColumns>> GetAuditRecordsForEntitiesAsync(
        IEnumerable<string> entityIds, 
        string tableName, 
        CancellationToken cancellationToken);

    /// <summary>
    /// Gets audit records by action type
    /// </summary>
    /// <param name="actionType">The type of action to filter by</param>
    /// <param name="tableName">Optional table name filter</param>
    /// <param name="fromDate">Optional start date filter</param>
    /// <param name="toDate">Optional end date filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated result of audit records</returns>
    Task<PaginatedResult<HistoricalAuditColumns>> GetAuditRecordsByActionTypeAsync(
        HistoricalMovementType actionType,
        string? tableName = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int pageNumber = 1,
        int pageSize = 25,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets audit records by user
    /// </summary>
    /// <param name="user">The user who performed the actions</param>
    /// <param name="tableName">Optional table name filter</param>
    /// <param name="fromDate">Optional start date filter</param>
    /// <param name="toDate">Optional end date filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated result of audit records</returns>
    Task<PaginatedResult<HistoricalAuditColumns>> GetAuditRecordsByUserAsync(
        string user,
        string? tableName = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int pageNumber = 1,
        int pageSize = 25,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk inserts audit records for performance
    /// </summary>
    /// <param name="auditRecords">Collection of audit records to insert</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task BulkInsertAsync(IEnumerable<HistoricalAuditColumns> auditRecords, CancellationToken cancellationToken);

    /// <summary>
    /// Checks if an entity has any audit records
    /// </summary>
    /// <param name="entityId">The ID of the entity</param>
    /// <param name="tableName">The table/entity name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if audit records exist, false otherwise</returns>
    Task<bool> HasAuditRecordsAsync(string entityId, string tableName, CancellationToken cancellationToken);

    /// <summary>
    /// Gets the latest audit record for an entity
    /// </summary>
    /// <param name="entityId">The ID of the entity</param>
    /// <param name="tableName">The table/entity name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The most recent audit record or null if none exists</returns>
    Task<HistoricalAuditColumns?> GetLatestAuditRecordAsync(
        string entityId, 
        string tableName, 
        CancellationToken cancellationToken);
}
