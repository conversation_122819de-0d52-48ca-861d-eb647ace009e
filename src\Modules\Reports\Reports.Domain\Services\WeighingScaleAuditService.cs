using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Repositories;

namespace Reports.Domain.Services;

/// <summary>
/// Domain service implementation for WeighingScale audit operations.
/// Encapsulates business logic specific to weighing scale auditing.
/// </summary>
public class WeighingScaleAuditService : IWeighingScaleAuditService
{
    private readonly IAuditService _auditService;
    private readonly IHistoricalAuditColumnsRepository _auditRepository;

    public WeighingScaleAuditService(
        IAuditService auditService,
        IHistoricalAuditColumnsRepository auditRepository)
    {
        _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
        _auditRepository = auditRepository ?? throw new ArgumentNullException(nameof(auditRepository));
    }

    public async Task<IEnumerable<HistoricalAuditColumns>> CreateWeighingScaleAuditRecordsAsync(
        IEnumerable<WeighingScale> insertedWeighins,
        IEnumerable<WeighingScale> updatedWeighins,
        IEnumerable<WeighingScale> cancelledWeighins,
        string user,
        string? operationMetadata = null)
    {
        if (string.IsNullOrWhiteSpace(user))
            throw new ArgumentException("User cannot be null or empty", nameof(user));

        var auditRecords = new List<HistoricalAuditColumns>();
        var tableName = ReportsTableNames.WeighingScale;

        // Create metadata with operation summary
        var metadata = CreateOperationMetadata(
            insertedWeighins.Count(),
            updatedWeighins.Count(),
            cancelledWeighins.Count(),
            operationMetadata);

        // Create audit records for insertions
        if (insertedWeighins.Any())
        {
            var insertAuditRecords = _auditService.CreateInsertAuditRecords(
                insertedWeighins,
                tableName,
                user,
                metadata);
            auditRecords.AddRange(insertAuditRecords);
        }

        // Create audit records for updates
        if (updatedWeighins.Any())
        {
            var updateAuditRecords = _auditService.CreateUpdateAuditRecords(
                updatedWeighins,
                tableName,
                user,
                metadata);
            auditRecords.AddRange(updateAuditRecords);
        }

        // Create audit records for cancellations
        if (cancelledWeighins.Any())
        {
            var cancellationAuditRecords = _auditService.CreateCancellationAuditRecords(
                cancelledWeighins,
                tableName,
                user,
                metadata);
            auditRecords.AddRange(cancellationAuditRecords);
        }

        return auditRecords;
    }

    public async Task StoreAuditRecordsAsync(IEnumerable<HistoricalAuditColumns> auditRecords, CancellationToken cancellationToken)
    {
        if (!auditRecords.Any())
            return;

        await _auditRepository.BulkInsertAsync(auditRecords, cancellationToken);
    }

    public IEnumerable<EntityChange<WeighingScale>> CreateEntityChanges(
        IEnumerable<WeighingScale> currentWeighins,
        IEnumerable<WeighingScale> previousWeighins)
    {
        var currentDict = currentWeighins.ToDictionary(w => w.Id, w => w);
        var previousDict = previousWeighins.ToDictionary(w => w.Id, w => w);

        var entityChanges = new List<EntityChange<WeighingScale>>();

        foreach (var current in currentWeighins)
        {
            if (previousDict.TryGetValue(current.Id, out var previous))
            {
                entityChanges.Add(new EntityChange<WeighingScale>
                {
                    EntityId = current.Id?.ToString() ?? string.Empty,
                    PreviousState = previous,
                    CurrentState = current
                });
            }
        }

        return entityChanges;
    }

    private string CreateOperationMetadata(
        int insertedCount,
        int updatedCount,
        int cancelledCount,
        string? additionalMetadata)
    {
        var metadata = new
        {
            OperationType = "WeighingScaleBulkOperation",
            Timestamp = DateTime.UtcNow,
            Summary = new
            {
                InsertedCount = insertedCount,
                UpdatedCount = updatedCount,
                CancelledCount = cancelledCount,
                TotalOperations = insertedCount + updatedCount + cancelledCount
            },
            AdditionalMetadata = additionalMetadata
        };

        return System.Text.Json.JsonSerializer.Serialize(metadata, new System.Text.Json.JsonSerializerOptions
        {
            PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase,
            WriteIndented = false
        });
    }
}
