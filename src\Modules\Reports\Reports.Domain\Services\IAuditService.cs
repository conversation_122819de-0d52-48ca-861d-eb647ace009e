using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Domain.Services;

/// <summary>
/// Domain service for handling audit logging operations.
/// Follows DDD principles by encapsulating audit business logic.
/// </summary>
public interface IAuditService
{
    /// <summary>
    /// Creates audit records for entity insertions
    /// </summary>
    /// <typeparam name="TEntity">The type of entity being audited</typeparam>
    /// <param name="entities">The entities that were inserted</param>
    /// <param name="tableName">The table/entity name</param>
    /// <param name="user">The user who performed the operation</param>
    /// <param name="metadata">Optional metadata about the operation</param>
    /// <returns>Collection of audit records</returns>
    IEnumerable<HistoricalAuditColumns> CreateInsertAuditRecords<TEntity>(
        IEnumerable<TEntity> entities,
        string tableName,
        string user,
        string? metadata = null) where TEntity : class;

    /// <summary>
    /// Creates audit records for entity updates
    /// </summary>
    /// <typeparam name="TEntity">The type of entity being audited</typeparam>
    /// <param name="entityChanges">Collection of entity changes (previous and current state)</param>
    /// <param name="tableName">The table/entity name</param>
    /// <param name="user">The user who performed the operation</param>
    /// <param name="metadata">Optional metadata about the operation</param>
    /// <returns>Collection of audit records</returns>
    IEnumerable<HistoricalAuditColumns> CreateUpdateAuditRecords<TEntity>(
        IEnumerable<EntityChange<TEntity>> entityChanges,
        string tableName,
        string user,
        string? metadata = null) where TEntity : class;

    /// <summary>
    /// Creates audit records for entity cancellations/deletions
    /// </summary>
    /// <typeparam name="TEntity">The type of entity being audited</typeparam>
    /// <param name="entities">The entities that were cancelled/deleted</param>
    /// <param name="tableName">The table/entity name</param>
    /// <param name="user">The user who performed the operation</param>
    /// <param name="metadata">Optional metadata about the operation</param>
    /// <returns>Collection of audit records</returns>
    IEnumerable<HistoricalAuditColumns> CreateCancellationAuditRecords<TEntity>(
        IEnumerable<TEntity> entities,
        string tableName,
        string user,
        string? metadata = null) where TEntity : class;

    /// <summary>
    /// Creates a comprehensive audit record for bulk operations
    /// </summary>
    /// <typeparam name="TEntity">The type of entity being audited</typeparam>
    /// <param name="bulkOperation">The bulk operation details</param>
    /// <returns>Collection of audit records</returns>
    IEnumerable<HistoricalAuditColumns> CreateBulkOperationAuditRecords<TEntity>(
        BulkAuditOperation<TEntity> bulkOperation) where TEntity : class;

    /// <summary>
    /// Extracts the entity ID from an entity using reflection or conventions
    /// </summary>
    /// <typeparam name="TEntity">The type of entity</typeparam>
    /// <param name="entity">The entity instance</param>
    /// <returns>The entity ID as string</returns>
    string ExtractEntityId<TEntity>(TEntity entity) where TEntity : class;

    /// <summary>
    /// Gets the table name for an entity type
    /// </summary>
    /// <typeparam name="TEntity">The type of entity</typeparam>
    /// <returns>The table name</returns>
    string GetTableName<TEntity>() where TEntity : class;
}

/// <summary>
/// Represents a change to an entity (before and after state)
/// </summary>
/// <typeparam name="TEntity">The type of entity</typeparam>
public class EntityChange<TEntity> where TEntity : class
{
    public TEntity PreviousState { get; set; } = null!;
    public TEntity CurrentState { get; set; } = null!;
    public string EntityId { get; set; } = string.Empty;
}

/// <summary>
/// Represents a bulk operation containing multiple types of changes
/// </summary>
/// <typeparam name="TEntity">The type of entity</typeparam>
public class BulkAuditOperation<TEntity> where TEntity : class
{
    public string TableName { get; set; } = string.Empty;
    public string User { get; set; } = string.Empty;
    public string? Metadata { get; set; }
    
    public IEnumerable<TEntity> InsertedEntities { get; set; } = Enumerable.Empty<TEntity>();
    public IEnumerable<EntityChange<TEntity>> UpdatedEntities { get; set; } = Enumerable.Empty<EntityChange<TEntity>>();
    public IEnumerable<TEntity> CancelledEntities { get; set; } = Enumerable.Empty<TEntity>();
}
