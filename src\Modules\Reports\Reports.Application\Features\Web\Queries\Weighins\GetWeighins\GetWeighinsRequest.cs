﻿using MediatR;

namespace Reports.Application.Features.Web.Queries.Weighins.GetWeighins;

public record GetWeighinsRequest : IRequest<GetWeighinsResponse>
{
    /// <summary>
    /// Fecha desde
    /// </summary>
    /// <example>2024-01-04 12:30:51</example>
    public DateTime? FromDate { get; set; }
    
    /// <summary>
    /// <PERSON><PERSON> hasta
    /// </summary>
    /// <example>2024-01-05 12:30:51</example>
    public DateTime? ToDate { get; set; }
    
    /// <summary>
    /// Placa del vehículo
    /// </summary>
    /// <example>ABC123</example>
    public string? LicensePlate { get; set; }
    
    /// <summary>
    /// Número de identificación tributaria
    /// </summary>
    /// <example>811007618</example>
    public long? NIT { get; set; }
}