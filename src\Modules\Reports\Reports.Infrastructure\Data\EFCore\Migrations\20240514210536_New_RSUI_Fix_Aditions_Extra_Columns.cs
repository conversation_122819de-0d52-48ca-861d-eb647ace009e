﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class New_RSUI_Fix_Aditions_Extra_Columns : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CE_ID_TICKET",
                table: "Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento");

            migrationBuilder.DropColumn(
                name: "CE_NOMBRE_AREA",
                table: "Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento");

            migrationBuilder.DropColumn(
                name: "CE_REL_COMPENSACION",
                table: "Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento");

            migrationBuilder.DropColumn(
                name: "CE_REL_COMPENSACION_ID_TICKET",
                table: "Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento");

            migrationBuilder.DropColumn(
                name: "CE_ID_TICKET",
                table: "Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU");

            migrationBuilder.DropColumn(
                name: "CE_NOMBRE_AREA",
                table: "Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU");

            migrationBuilder.DropColumn(
                name: "CE_REL_COMPENSACION",
                table: "Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU");

            migrationBuilder.DropColumn(
                name: "CE_REL_COMPENSACION_ID_TICKET",
                table: "Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "CE_ID_TICKET",
                table: "Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<string>(
                name: "CE_NOMBRE_AREA",
                table: "Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "CE_REL_COMPENSACION",
                table: "Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<long>(
                name: "CE_REL_COMPENSACION_ID_TICKET",
                table: "Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "CE_ID_TICKET",
                table: "Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<string>(
                name: "CE_NOMBRE_AREA",
                table: "Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "CE_REL_COMPENSACION",
                table: "Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<long>(
                name: "CE_REL_COMPENSACION_ID_TICKET",
                table: "Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU",
                type: "bigint",
                nullable: true);
        }
    }
}
