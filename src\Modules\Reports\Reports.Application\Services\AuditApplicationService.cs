using AutoMapper;
using Reports.Application.DTOs.Audit;
using Reports.Domain.Constants;
using Reports.Domain.Repositories;

namespace Reports.Application.Services;

/// <summary>
/// Application service implementation for audit operations.
/// Coordinates between domain services and provides DTOs for the presentation layer.
/// </summary>
public class AuditApplicationService : IAuditApplicationService
{
    private readonly IHistoricalAuditColumnsRepository _auditRepository;
    private readonly IMapper _mapper;

    public AuditApplicationService(
        IHistoricalAuditColumnsRepository auditRepository,
        IMapper mapper)
    {
        _auditRepository = auditRepository ?? throw new ArgumentNullException(nameof(auditRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    }

    public async Task<IEnumerable<AuditRecordDto>> GetAuditRecordsForEntityAsync(
        string entityId,
        string tableName,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(entityId))
            throw new ArgumentException("Entity ID cannot be null or empty", nameof(entityId));
        
        if (string.IsNullOrWhiteSpace(tableName))
            throw new ArgumentException("Table name cannot be null or empty", nameof(tableName));

        var auditRecords = await _auditRepository.GetAuditRecordsForEntityAsync(
            entityId, tableName, cancellationToken);

        return _mapper.Map<IEnumerable<AuditRecordDto>>(auditRecords);
    }

    public async Task<PaginatedAuditRecordsDto> GetAuditRecordsByActionTypeAsync(
        GetAuditRecordsByActionTypeRequest request,
        CancellationToken cancellationToken)
    {
        if (request == null)
            throw new ArgumentNullException(nameof(request));

        var paginatedResult = await _auditRepository.GetAuditRecordsByActionTypeAsync(
            request.ActionType,
            request.TableName,
            request.FromDate,
            request.ToDate,
            request.PageNumber,
            request.PageSize,
            cancellationToken);

        return new PaginatedAuditRecordsDto
        {
            Results = _mapper.Map<IEnumerable<AuditRecordDto>>(paginatedResult.Results),
            TotalRecords = paginatedResult.TotalRecords,
            PageNumber = paginatedResult.PageNumber,
            PageSize = paginatedResult.PageSize
        };
    }

    public async Task<PaginatedAuditRecordsDto> GetAuditRecordsByUserAsync(
        GetAuditRecordsByUserRequest request,
        CancellationToken cancellationToken)
    {
        if (request == null)
            throw new ArgumentNullException(nameof(request));

        if (string.IsNullOrWhiteSpace(request.User))
            throw new ArgumentException("User cannot be null or empty", nameof(request.User));

        var paginatedResult = await _auditRepository.GetAuditRecordsByUserAsync(
            request.User,
            request.TableName,
            request.FromDate,
            request.ToDate,
            request.PageNumber,
            request.PageSize,
            cancellationToken);

        return new PaginatedAuditRecordsDto
        {
            Results = _mapper.Map<IEnumerable<AuditRecordDto>>(paginatedResult.Results),
            TotalRecords = paginatedResult.TotalRecords,
            PageNumber = paginatedResult.PageNumber,
            PageSize = paginatedResult.PageSize
        };
    }

    public async Task<bool> HasAuditRecordsAsync(
        string entityId,
        string tableName,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(entityId))
            throw new ArgumentException("Entity ID cannot be null or empty", nameof(entityId));
        
        if (string.IsNullOrWhiteSpace(tableName))
            throw new ArgumentException("Table name cannot be null or empty", nameof(tableName));

        return await _auditRepository.HasAuditRecordsAsync(entityId, tableName, cancellationToken);
    }

    public async Task<AuditStatisticsDto> GetAuditStatisticsAsync(
        string tableName,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(tableName))
            throw new ArgumentException("Table name cannot be null or empty", nameof(tableName));

        // Get audit records for each action type
        var createdRecords = await _auditRepository.GetAuditRecordsByActionTypeAsync(
            HistoricalMovementType.Created, tableName, fromDate, toDate, 1, int.MaxValue, cancellationToken);
        
        var modifiedRecords = await _auditRepository.GetAuditRecordsByActionTypeAsync(
            HistoricalMovementType.Modified, tableName, fromDate, toDate, 1, int.MaxValue, cancellationToken);
        
        var annulledRecords = await _auditRepository.GetAuditRecordsByActionTypeAsync(
            HistoricalMovementType.Annulled, tableName, fromDate, toDate, 1, int.MaxValue, cancellationToken);

        var allRecords = createdRecords.Results
            .Concat(modifiedRecords.Results)
            .Concat(annulledRecords.Results)
            .ToList();

        // Calculate user statistics
        var userStats = allRecords
            .GroupBy(r => r.User)
            .Select(g => new UserAuditStatisticDto
            {
                User = g.Key,
                TotalOperations = g.Count(),
                CreatedRecords = g.Count(r => r.ActionType == HistoricalMovementType.Created),
                ModifiedRecords = g.Count(r => r.ActionType == HistoricalMovementType.Modified),
                AnnulledRecords = g.Count(r => r.ActionType == HistoricalMovementType.Annulled)
            })
            .OrderByDescending(u => u.TotalOperations)
            .ToList();

        // Calculate daily statistics
        var dailyStats = allRecords
            .GroupBy(r => r.ActionDate.Date)
            .Select(g => new DailyAuditStatisticDto
            {
                Date = g.Key,
                TotalOperations = g.Count(),
                CreatedRecords = g.Count(r => r.ActionType == HistoricalMovementType.Created),
                ModifiedRecords = g.Count(r => r.ActionType == HistoricalMovementType.Modified),
                AnnulledRecords = g.Count(r => r.ActionType == HistoricalMovementType.Annulled)
            })
            .OrderBy(d => d.Date)
            .ToList();

        return new AuditStatisticsDto
        {
            TableName = tableName,
            FromDate = fromDate,
            ToDate = toDate,
            TotalRecords = allRecords.Count,
            CreatedRecords = createdRecords.TotalRecords,
            ModifiedRecords = modifiedRecords.TotalRecords,
            AnnulledRecords = annulledRecords.TotalRecords,
            UserStatistics = userStats,
            DailyStatistics = dailyStats
        };
    }
}
