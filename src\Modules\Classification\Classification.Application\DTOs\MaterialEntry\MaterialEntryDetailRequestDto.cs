﻿using System.Text.Json.Serialization;

namespace Classification.Application.DTOs.MaterialEntry;

public class MaterialEntryDetailRequestDto
{
    /// <summary>
    /// Cliente al que corresponde el ingreso.
    /// </summary>
    /// <remarks>Nombre del cliente al que le corresponde el ingreso del registro.</remarks>
    /// <example><PERSON></example>
    [JsonPropertyName("client")]
    public string? Client { get; set; }
    
    /// <summary>
    /// Listado de bolsones del detalle a ingresar divididos por peso y presentación.
    /// </summary>
    /// <remarks>Debe poseer al menos un bolson.</remarks>
    /// <example cref="SackRequestDto">SackRequestDto</example>
    [JsonPropertyName("sacks")]
    public IEnumerable<SackRequestDto> Sacks { get; set; }
}