﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class DistributionConfiguration : IEntityTypeConfiguration<Distribution>
{
    public void Configure(EntityTypeBuilder<Distribution> builder)
    {
        builder.ToView(ReportsViewNames.Distributions);
        
        builder.HasNoKey();
        builder.Ignore(p => p.Id);
        
        builder
            .Property(p => p.Year)
            .HasColumnName(DistributionsViewColumns.Year);
        
        builder
            .Property(p => p.Month)
            .HasColumnName(DistributionsViewColumns.Month);
        
        builder
            .Property(p => p.NUAP)
            .HasColumnName(DistributionsViewColumns.NUAP);
        
        builder
            .Property(p => p.RecyclingArea)
            .HasColumnName(DistributionsViewColumns.RecyclingArea);
        
        builder
            .Property(p => p.Trips)
            .HasColumnName(DistributionsViewColumns.Trips);
        
        builder
            .Property(p => p.DeviationTons)
            .HasColumnName(DistributionsViewColumns.DeviationTons);
        
        builder
            .Property(p => p.DistributedTons)
            .HasColumnName(DistributionsViewColumns.DistributedTons);
        
        builder
            .Property(p => p.ReportedTons)
            .HasColumnName(DistributionsViewColumns.ReportedTons);
        
        builder
            .Property(p => p.TollSharedRouteTons)
            .HasColumnName(DistributionsViewColumns.TollSharedRouteTons);
        
        builder
            .Property(p => p.DistributionTollPercentage)
            .HasColumnName(DistributionsViewColumns.DistributionTollPercentage);
    }
}