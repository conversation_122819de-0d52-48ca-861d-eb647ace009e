﻿using System.ComponentModel;

namespace Reports.Application.DTOs.Reports;

public record F14ExportResponseDto
{
    public string C1_NUAP { get; init; }
    public int C2_TIPO_SITIO { get; init; }
    public string C3_NUSD { get; init; }
    public string C4_<PERSON>LACA { get; init; }
    public string C5_FECHA { get; init; }
    public string C6_HORA { get; init; }
    public long C7_NUMICRO { get; init; }
    public decimal C8_TON_LIMP_URB { get; init; }
    public decimal C9_TON_BARRIDO { get; init; }
    public decimal C10_TONRESNA { get; init; }
    public decimal C11_TONRECHAPR { get; init; }
    public decimal C12_TONRESAPR { get; init; }
    public int C13_SISTEMA_MEDICION { get; init; }
    public int C14_VLRPEAJ { get; init; }
    public long? CE_ID_TICKET { get; set; }
    public string CE_NOMBRE_AREA { get; set; }
    public string CE_RUTA_LARGA { get; set; }
    public double CE_TON_TOTAL { get; set; }
    public bool? CE_REL_COMPENSACION { get; set; }
    public long? CE_REL_COMPENSACION_ID_TICKET { get; set; }
}