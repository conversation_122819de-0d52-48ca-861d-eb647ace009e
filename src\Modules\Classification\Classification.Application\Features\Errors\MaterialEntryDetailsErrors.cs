﻿using Orion.SharedKernel.Domain.Entities.Error;
using Orion.SharedKernel.Domain.Entities.Loggers;
using System.Net;

namespace Classification.Application.Features.Errors;

public class MaterialEntryDetailNotFound : Error
{
	public MaterialEntryDetailNotFound(int id)
	{
		Title = "No se encontró el detalle del material de entrada.";
		Messages = new List<string> { $"No se encontró el detalle de material de entrada con id: '{id}'." };
		Status = HttpStatusCode.NotFound;
		Platform = PlatformApp.Api;
		ErrorCode = LevelErrorCode.Error;
	}

	public MaterialEntryDetailNotFound(IEnumerable<int> ids)
	{
		Title = "No se encontraron algunos detalles de material de entrada.";
		Messages = new List<string> { $"No se encontraron los siguientes detalles de material de entrada con ids: '{string.Join(", ", ids)}'." };
		Status = HttpStatusCode.NotFound;
		Platform = PlatformApp.Api;
		ErrorCode = LevelErrorCode.Error;
	}
}