﻿using Orion.SharedKernel.Domain.ValueObjects;
using Reports.Domain.Entities;

namespace Reports.Domain.ValueObjects;

public class MassBalance : ValueObject
{
    private const string EmvariasTownCode = "5001";
    private const string EmvariasServiceArea = "Medellín";
    private const int EmvariasNUAP = 440405001;
    private const string ItaguiServiceArea = "Itaguí";
    private const string SharedRoute = "0614001";
    private const string OthersArea = "Otros";
    private readonly string[] RecollectionAndTransportAdditionals = { "CLUS - CARGADO", "APROVECHAMIENTO - CARGADO" };

    public List<Distribution> Distributions { get; }
    public WeighinTonsResume Weighins { get; }
    public AreaGroup<WeighinTonsResume> FinalDisposition { get; }
    public AreaGroup<HygieneTonsResume> RecollectionAndTransport { get; }
    public bool IsValid { get; private set; }
    public decimal SharedRouteTons { get; private set; }
    
    protected MassBalance()
    {
        Distributions = new List<Distribution>();
        Weighins = new WeighinTonsResume();
        RecollectionAndTransport = new AreaGroup<HygieneTonsResume>();
        FinalDisposition = new AreaGroup<WeighinTonsResume>();
    }
    
    public static MassBalance Create() => new();
    
    public MassBalance ProcessDistributions(IEnumerable<Distribution> distributions)
    {
        Distributions.AddRange(distributions.OrderBy(x => x.RecyclingArea));
        return this;
    }
    
    public MassBalance ProcessWeighins(IReadOnlyList<WeighingScale> weighins)
    {
        Weighins.Emvarias = (decimal)weighins
            .Where(w => w.Town.Code.Contains(EmvariasTownCode))
            .Sum(x => x.ArrivingWeight - (x.ArrivingWeight - x.LeavingWeight)) / 1000;

        Weighins.Total = (decimal)weighins
            .Sum(x => x.ArrivingWeight - (x.ArrivingWeight - x.LeavingWeight)) / 1000;
        
        return this;
    }

    public MassBalance ProcessRecollectionAndTransport(IReadOnlyList<ReportFormat14> reportData)
    {
        var groupedData = reportData
            .GroupBy(x => x.RecyclingArea)
            .Select(x => new { x.Key, Value = x.ToList() })
            .AsQueryable();

        var externalAdditionalEntries = groupedData
            .Where(x => RecollectionAndTransportAdditionals.Contains(x.Key))
            .SelectMany(x => x.Value);

        SharedRouteTons = reportData
            .Where(x => x.ExtendedRouteCode == SharedRoute && x.RecyclingArea == ItaguiServiceArea)
            .Sum(x => x.SweepingTons + x.NonRecyclableTons);
            
        groupedData
            .Where(x => !RecollectionAndTransportAdditionals.Contains(x.Key))
            .ToList()
            .ForEach(x =>
            {
                RecollectionAndTransport.PerArea.Add(new ResumePerArea<HygieneTonsResume>
                {
                    RecyclingArea = x.Key,
                    Resume = new HygieneTonsResume
                    {
                        UrbanCleaning = x.Key == EmvariasServiceArea 
                            ? externalAdditionalEntries.Sum(y => y.UrbanCleaningTons) 
                            : x.Value.Sum(y => y.UrbanCleaningTons),
                        Sweeping = x.Value.Sum(y => y.SweepingTons),
                        NonRecyclable = x.Value.Sum(y => y.NonRecyclableTons),
                        Rejection = x.Value.Sum(y => y.RejectedTons),
                        Recyclable = x.Key == EmvariasServiceArea 
                            ? externalAdditionalEntries.Sum(y => y.RecyclableTons) 
                            : x.Value.Sum(y => y.RecyclableTons),
                        Total = x.Value.Sum(y => y.SweepingTons + y.NonRecyclableTons)
                    }
                });
            });
        
        RecollectionAndTransport.PerArea = RecollectionAndTransport.PerArea
            .OrderByDescending(x => x.RecyclingArea == EmvariasServiceArea)
            .ThenBy(x => x.RecyclingArea)
            .ToList();
        
        RecollectionAndTransport.Totals = new HygieneTonsResume
        {
            UrbanCleaning = RecollectionAndTransport.PerArea.Sum(x => x.Resume.UrbanCleaning),
            Sweeping = RecollectionAndTransport.PerArea.Sum(x => x.Resume.Sweeping),
            NonRecyclable = RecollectionAndTransport.PerArea.Sum(x => x.Resume.NonRecyclable),
            Rejection = RecollectionAndTransport.PerArea.Sum(x => x.Resume.Rejection),
            Recyclable = RecollectionAndTransport.PerArea.Sum(x => x.Resume.Recyclable),
            Total = RecollectionAndTransport.PerArea.Sum(x => x.Resume.Total)
        };
        
        return this;
    }

    public MassBalance ProcessFinalDisposition(IReadOnlyList<ReportFormat34> reportData, IReadOnlyList<RecyclingArea> recyclingAreas)
    {
        var discountEntries = reportData
            .Where(x => x is { ExistsInReport14: false, PlaceOriginNumber: not null }
                        && x.PlaceOriginNumber.ToString()!.Contains(EmvariasTownCode))
            .AsQueryable();
        
        var emvariasEntries = reportData
            .Where(x => x.PlaceOriginNumber.HasValue 
                        && x.PlaceOriginNumber.ToString()!.Contains(EmvariasTownCode))
            .AsQueryable();

        FinalDisposition.PerArea.Add(new ResumePerArea<WeighinTonsResume>
        {
            RecyclingArea = EmvariasServiceArea,
            Resume = new WeighinTonsResume
            {
                Discount = discountEntries
                    .Sum(y => y.Tons),
                Emvarias = 
                    emvariasEntries
                    .Sum(y => y.Tons),
                Total = emvariasEntries.Sum(y => y.Tons)
                        - discountEntries.Sum(y => y.Tons) 
            }
        });
        
        var entriesWithNuap = reportData
            .Where(entry => entry.NUAP is not null && recyclingAreas.Any(a => a.Code == entry.NUAP))
            .ToList();
        
        entriesWithNuap
            .Where(x => x.NUAP != EmvariasNUAP)
            .GroupBy(x => x.NUAP)
            .ToList()
            .ForEach(group =>
            {
                var recyclingArea = recyclingAreas.FirstOrDefault(a => a.Code == group.Key);
                
                if (recyclingArea is null)
                    return;
                
                FinalDisposition.PerArea.Add(new ResumePerArea<WeighinTonsResume>
                {
                    RecyclingArea = recyclingArea.Name,
                    Resume = new WeighinTonsResume
                    {
                        Discount = decimal.Zero,
                        Emvarias = decimal.Zero,
                        Total = group
                            .Sum(y => y.Tons)
                    }
                });
            });
        
        FinalDisposition.PerArea = FinalDisposition.PerArea
            .OrderByDescending(x => x.RecyclingArea == EmvariasServiceArea)
            .ThenBy(x => x.RecyclingArea)
            .ToList();
        
        FinalDisposition.PerArea.Add(new ResumePerArea<WeighinTonsResume>
        {
            RecyclingArea = OthersArea,
            Resume = new WeighinTonsResume()
            {
                Discount = decimal.Zero,
                Emvarias = decimal.Zero,
                Total = reportData.Sum(x => x.Tons)
                        - FinalDisposition.PerArea.Find(x => x.RecyclingArea == EmvariasServiceArea)!.Resume.Emvarias
                        - FinalDisposition.PerArea
                            .Where(x => x.RecyclingArea != EmvariasServiceArea)
                            .Sum(x => x.Resume.Total)
            }
        });

        FinalDisposition.Totals = new WeighinTonsResume
        {
            Discount = FinalDisposition.PerArea.Sum(x => x.Resume.Discount),
            Emvarias = FinalDisposition.PerArea.Sum(x => x.Resume.Emvarias),
            Total = FinalDisposition.PerArea.Sum(x => x.Resume.Total)
                + FinalDisposition.PerArea.Sum(x => x.Resume.Discount)
        };
        
        return this;
    }
    
    public MassBalance Validate()
    {
        const decimal TonsDisparityTolerance = 0.001m;
        
        var finalDispositionTonsWithoutDiscounts = FinalDisposition
            .PerArea
            .Find(x => x.RecyclingArea == EmvariasServiceArea)!
            .Resume.Total;

        var emvariasRecollections = RecollectionAndTransport
            .PerArea
            .Where(x => x.RecyclingArea == EmvariasServiceArea)
            .Sum(x => x.Resume.Sweeping + x.Resume.NonRecyclable);
        
        var recollectionsDistributionValidation = RecollectionAndTransport
            .PerArea
            .Join(Distributions,
                x => x.RecyclingArea,
                y => y.RecyclingArea,
                (x, y) => new { x, y })
            .Select(join => new
            {
                RecyclingArea = join.y.RecyclingArea,
                RecollectionTotal = join.x.Resume.Sweeping + join.x.Resume.NonRecyclable,
                DeviationTons = join.y.DeviationTons,
                ItaguiSharedTons = join.y.RecyclingArea == ItaguiServiceArea
                    ? SharedRouteTons
                    : decimal.Zero,
                DistributionTons = join.y.ReportedTons,
            })
            .Select(x => new
            {
                x,
                MatchedWithoutTolerance = x.DistributionTons == x.RecollectionTotal - x.ItaguiSharedTons,
                Matched = Math.Abs(Math.Round(x.DistributionTons, 3, MidpointRounding.AwayFromZero) 
                          - Math.Round(x.RecollectionTotal - x.ItaguiSharedTons, 3, MidpointRounding.AwayFromZero)) 
                          <= TonsDisparityTolerance
            });
        
        var allRecollectionTonsMatchReportedTons = recollectionsDistributionValidation
            .All(x => x.Matched);
        
        if (allRecollectionTonsMatchReportedTons && finalDispositionTonsWithoutDiscounts == emvariasRecollections)
            IsValid = true;
        
        return this;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Distributions;
        yield return Weighins;
        yield return FinalDisposition;
        yield return RecollectionAndTransport;
        yield return IsValid;
        yield return SharedRouteTons;
    }
}