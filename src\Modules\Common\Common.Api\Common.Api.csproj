<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <DocumentationFile>obj\Debug\net6.0\Common.Api.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DocumentationFile>obj\Release\net6.0\Common.Api.xml</DocumentationFile>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\Shared\Shared.Api\Shared.Api\Shared.Api.csproj" />
      <ProjectReference Include="..\Common.Application\Common.Application.csproj" />
      <ProjectReference Include="..\Common.Infrastructure\Common.Infrastructure.csproj" />
    </ItemGroup>

</Project>
