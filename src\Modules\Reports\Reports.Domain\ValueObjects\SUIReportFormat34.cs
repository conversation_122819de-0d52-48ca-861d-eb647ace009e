﻿using Reports.Domain.Common;
using Reports.Domain.Constants;

namespace Reports.Domain.ValueObjects;

public class SUIReportFormat34
{
    public long C1_NUSD { get; init; }
    public int C2_TIPO_ORIGEN { get; init; }
    public string? C3_NUSITIO_ORI { get; init; }
    public string? C4_NOMBRE_EMPRESA { get; init; }
    public long? C5_NIT_EMPRESA { get; init; }
    public string? C6_COD_DANE_ORI { get; init; }
    public string C7_PLACA { get; init; }
    public string C8_FECHA_INGRESO { get; init; }
    public string C9_FECHA_SALIDA { get; init; }
    public string C10_HORA_INGRESO { get; init; }
    public string C11_HORA_SALIDA { get; init; }
    public string C12_TONELADAS { get; init; }

    protected SUIReportFormat34(long NUSD, OriginType OriginType, long? PlaceOrigin,
        string? CompanyName, long? NIT, string? DaneCode,
        string LicensePlate, DateOnly ArrivalDate, DateOnly DepartureDate,
        TimeOnly ArrivalTime, TimeOnly DepartureTime, decimal Tons)
    {
        C1_NUSD = NUSD;
        C2_TIPO_ORIGEN = (int)OriginType;
        C3_NUSITIO_ORI = FormattedPlaceOrigin(PlaceOrigin);
        C4_NOMBRE_EMPRESA = FormattedCompanyName(CompanyName);
        C5_NIT_EMPRESA = NIT;
        C6_COD_DANE_ORI = FormattedDaneCode(DaneCode);
        C7_PLACA = LicensePlate;
        C8_FECHA_INGRESO = SUIFormats.FormattedDate(ArrivalDate);
        C9_FECHA_SALIDA = SUIFormats.FormattedDate(DepartureDate);
        C10_HORA_INGRESO = SUIFormats.FormattedTime(ArrivalTime);
        C11_HORA_SALIDA = SUIFormats.FormattedTime(DepartureTime);
        C12_TONELADAS = SUIFormats.FormattedTons(Tons);
    }

    private static string? FormattedPlaceOrigin(long? placeOrigin) => 
        placeOrigin?
            .ToString()
            .PadLeft(12, '0');
    
    private static string? FormattedDaneCode(string? daneCode) => 
        daneCode?
            .PadLeft(5, '0');

    private static string? FormattedCompanyName(string? companyName) =>
        companyName?.Length > 40 ?
            companyName[..40] :
            companyName;
    
    public static SUIReportFormat34 Create(long NUSD, OriginType OriginType, long? PlaceOrigin,
        string? CompanyName, long? NIT, string? DaneCode,
        string LicensePlate, DateOnly ArrivalDate, DateOnly DepartureDate,
        TimeOnly ArrivalTime, TimeOnly DepartureTime, decimal Tons) =>
        new(NUSD, OriginType, PlaceOrigin, CompanyName, NIT, DaneCode,
            LicensePlate, ArrivalDate, DepartureDate, ArrivalTime, DepartureTime, Tons);
}