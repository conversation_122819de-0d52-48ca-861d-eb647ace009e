using AutoMapper;
using Reports.Application.DTOs.Audit;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using System.Text.Json;

namespace Reports.Application.Common.MappingProfiles;

/// <summary>
/// AutoMapper profile for audit-related mappings
/// </summary>
public class AuditMappingProfile : Profile
{
    public AuditMappingProfile()
    {
        CreateMap<HistoricalAuditColumns, AuditRecordDto>()
            .ForMember(dest => dest.ActionTypeDescription, 
                opt => opt.MapFrom(src => GetActionTypeDescription(src.ActionType)))
            .ForMember(dest => dest.AuditDetails,
                opt => opt.MapFrom(src => ParseAuditDetails(src.HistoricalData)));

        CreateMap<AuditRecord<object>, AuditRecordDetailDto>()
            .ForMember(dest => dest.ActionTypeDescription,
                opt => opt.MapFrom(src => GetActionTypeDescription(src.ActionType)));
    }

    private static string GetActionTypeDescription(HistoricalMovementType actionType)
    {
        return actionType switch
        {
            HistoricalMovementType.Created => "Creado",
            HistoricalMovementType.Modified => "Modificado",
            HistoricalMovementType.Annulled => "Anulado",
            _ => "Desconocido"
        };
    }

    private static IEnumerable<AuditRecordDetailDto> ParseAuditDetails(string historicalData)
    {
        if (string.IsNullOrWhiteSpace(historicalData) || historicalData == "[]")
            return Enumerable.Empty<AuditRecordDetailDto>();

        try
        {
            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            // Parse as generic objects since we don't know the entity type at compile time
            var auditRecords = JsonSerializer.Deserialize<IEnumerable<GenericAuditRecord>>(historicalData, jsonOptions);
            
            if (auditRecords == null)
                return Enumerable.Empty<AuditRecordDetailDto>();

            return auditRecords.Select(record => new AuditRecordDetailDto
            {
                ActionType = record.ActionType,
                ActionTypeDescription = GetActionTypeDescription(record.ActionType),
                ActionDate = record.ActionDate,
                User = record.User,
                CurrentState = record.CurrentState,
                PreviousState = record.PreviousState
            });
        }
        catch (JsonException)
        {
            // If parsing fails, return empty collection
            return Enumerable.Empty<AuditRecordDetailDto>();
        }
    }

    /// <summary>
    /// Generic audit record for JSON deserialization
    /// </summary>
    private class GenericAuditRecord
    {
        public HistoricalMovementType ActionType { get; set; }
        public DateTime ActionDate { get; set; }
        public string User { get; set; } = string.Empty;
        public object? CurrentState { get; set; }
        public object? PreviousState { get; set; }
    }
}
