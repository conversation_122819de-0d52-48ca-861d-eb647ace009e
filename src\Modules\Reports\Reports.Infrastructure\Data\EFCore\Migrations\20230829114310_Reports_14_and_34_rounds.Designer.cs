﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Reports.Infrastructure.Data.EFCore;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    [DbContext(typeof(ReportsDbContext))]
    [Migration("20230829114310_Reports_14_and_34_rounds")]
    partial class Reports_14_and_34_rounds
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.20")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Reports.Domain.Entities.CollectionByMicroroute", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("REPEM04_Id");

                    b.Property<DateTime>("CollectionDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM04_FechaHora_Recoleccion");

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("REPEM04_Patente");

                    b.Property<string>("Microroute")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("REPEM04_Microruta");

                    b.Property<long>("NUAP")
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM04_NUAP");

                    b.Property<double>("RecyclableTons")
                        .HasPrecision(18, 2)
                        .HasColumnType("double precision")
                        .HasColumnName("REPEM04_TonResAprob");

                    b.Property<double>("RejectedTons")
                        .HasPrecision(18, 2)
                        .HasColumnType("double precision")
                        .HasColumnName("REPEM04_TonRechazo");

                    b.Property<double>("SweepingTons")
                        .HasPrecision(18, 2)
                        .HasColumnType("double precision")
                        .HasColumnName("REPEM04_TonBarrido");

                    b.Property<double>("Toll")
                        .HasPrecision(18, 2)
                        .HasColumnType("double precision")
                        .HasColumnName("REPEM04_Valor_peaje");

                    b.Property<double>("UrbanCleaningTons")
                        .HasPrecision(18, 2)
                        .HasColumnType("double precision")
                        .HasColumnName("REPEM04_TonLimUrb");

                    b.HasKey("Id")
                        .HasName("Reporting-Emvarias_04-Recoleccion_por_Microruta_key");

                    b.ToTable("Reporting-Emvarias_04-Recoleccion_por_Microruta", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.RecyclingArea", b =>
                {
                    b.Property<string>("Code")
                        .HasColumnType("text")
                        .HasColumnName("REPEM02_Codigo");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("REPEM02_Nombre");

                    b.HasKey("Code")
                        .HasName("Reporting-Emvarias_02-Areas_de_Aprovechamiento_key");

                    b.ToTable("Reporting-Emvarias_02-Areas_de_Aprovechamiento", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.ReportFormat14", b =>
                {
                    b.Property<DateOnly>("EntryDate")
                        .HasColumnType("date");

                    b.Property<TimeOnly>("EntryTime")
                        .HasColumnType("time without time zone");

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("MeasuringUnit")
                        .HasColumnType("integer");

                    b.Property<string>("Microroute")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("NUAP")
                        .HasColumnType("bigint");

                    b.Property<double>("NonRecyclableTons")
                        .HasColumnType("double precision");

                    b.Property<string>("OriginType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<double>("RecyclableTons")
                        .HasColumnType("double precision");

                    b.Property<double>("RejectedTons")
                        .HasColumnType("double precision");

                    b.Property<double>("SweepingTons")
                        .HasColumnType("double precision");

                    b.Property<double>("Toll")
                        .HasColumnType("double precision");

                    b.Property<double>("UrbanCleaningTons")
                        .HasColumnType("double precision");

                    b.ToView("View_Reporte_Formato_14");
                });

            modelBuilder.Entity("Reports.Domain.Entities.ReportFormat34", b =>
                {
                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DaneCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateOnly>("EgressDate")
                        .HasColumnType("date");

                    b.Property<TimeOnly>("EgressTime")
                        .HasColumnType("time without time zone");

                    b.Property<DateOnly>("EntryDate")
                        .HasColumnType("date");

                    b.Property<TimeOnly>("EntryTime")
                        .HasColumnType("time without time zone");

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("NIT")
                        .HasColumnType("bigint");

                    b.Property<long>("NUSD")
                        .HasColumnType("bigint");

                    b.Property<string>("OriginType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("PlaceOriginNumber")
                        .HasColumnType("bigint");

                    b.Property<double>("Tons")
                        .HasColumnType("double precision");

                    b.ToView("View_Reporte_Formato_34");
                });

            modelBuilder.Entity("Reports.Domain.Entities.Town", b =>
                {
                    b.Property<string>("Code")
                        .HasColumnType("text")
                        .HasColumnName("REPEM01_Codigo");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("REPEM01_Departamento");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("REPEM01_Nombre");

                    b.Property<string>("Province")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("REPEM01_Provincia");

                    b.HasKey("Code")
                        .HasName("Reporting-Emvarias_01-Municipios_key");

                    b.ToTable("Reporting-Emvarias_01-Municipios", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.VehicleRetrieval", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("REPEM05_Id");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("REPEM05_Empresa");

                    b.Property<string>("DaneCode")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("REPEM05_Codigo_DANE");

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("REPEM05_Patente");

                    b.Property<long>("NIT")
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM05_NIT");

                    b.Property<long>("NUAP")
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM05_NUAP");

                    b.Property<DateTime>("RetrievalDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM05_FechaHora_Recoleccion");

                    b.Property<decimal>("Tons")
                        .HasColumnType("numeric")
                        .HasColumnName("REPEM05_Toneladas");

                    b.HasKey("Id")
                        .HasName("Reporting-Emvarias_05-Recoleccion_Vehicular_key");

                    b.ToTable("Reporting-Emvarias_05-Recoleccion_Vehicular", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.WeighingScale", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("REPEM03_Id");

                    b.Property<double>("ArrivingWeight")
                        .HasPrecision(18, 2)
                        .HasColumnType("double precision")
                        .HasColumnName("REPEM03_Peso_de_entrada");

                    b.Property<long>("DepositPlace")
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM03_Lugar_de_deposito");

                    b.Property<DateTime>("EgressDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM03_Fecha_de_egreso");

                    b.Property<DateTime>("EntryDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM03_Fecha_de_entrada");

                    b.Property<double>("LeavingWeight")
                        .HasPrecision(18, 2)
                        .HasColumnType("double precision")
                        .HasColumnName("REPEM03_Peso_de_salida");

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("REPEM03_Patente");

                    b.Property<DateTime>("LoadingDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("REPEM03_Fecha_de_carga");

                    b.Property<string>("LoadingType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("REPEM03_Tipo_de_carga");

                    b.Property<long>("NIT")
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM03_Nro_Identificacion_Tributaria");

                    b.Property<long>("NUAP")
                        .HasColumnType("bigint")
                        .HasColumnName("REPEM03_Nro_Unico_Area_Prestacion");

                    b.Property<string>("OriginType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("REPEM03_Tipo_de_origen");

                    b.Property<string>("TownCode")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("REPEM03_Municipio");

                    b.HasKey("Id")
                        .HasName("Reporting-Emvarias_03-Pesaje_de_Balanza_key");

                    b.HasIndex("TownCode");

                    b.ToTable("Reporting-Emvarias_03-Pesaje_de_Balanza", (string)null);
                });

            modelBuilder.Entity("Reports.Domain.Entities.WeighingScale", b =>
                {
                    b.HasOne("Reports.Domain.Entities.Town", "Town")
                        .WithMany()
                        .HasForeignKey("TownCode")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("Reporting-Emvarias_REPEM03-REPEM01_fkey");

                    b.Navigation("Town");
                });
#pragma warning restore 612, 618
        }
    }
}
