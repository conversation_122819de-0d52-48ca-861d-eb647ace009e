﻿using Classification.Domain.Constants;
using Orion.SharedKernel.Domain.Entities.Audit;
using Orion.SharedKernel.Domain.Entities.Audit.DeletionAudit;

namespace Classification.Domain.Entities;

public class MaterialClassification : AuditableEntity<int>, ISoftDeletedEntity
{
    public ClassificationStep CompleteState { get; set; }
	public bool IsDeleted { get; set; }

    public IEnumerable<MaterialEntryDetail> MaterialEntryDetails { get; set; }
    public IEnumerable<MaterialClassificationDetail> Details { get; set; }
}