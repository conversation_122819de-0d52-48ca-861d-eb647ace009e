﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Classification.Infrastructure.Data.EFCore.Migrations
{
    public partial class Adds_MaterialClassification_And_MaterialClassificationDetail_Tables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Recycling-ECA_07-Clasificacion_de_Material",
                columns: table => new
                {
                    RECYECA07_Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    RECYECA07_Paso_de_la_Clasificacion = table.Column<string>(type: "text", nullable: false),
                    RECYECA07_Eliminado = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    RECYECA07_FechaDeCreacion = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    RECYECA07_CreadoPor = table.Column<string>(type: "character varying(60)", maxLength: 60, nullable: false),
                    RECYECA07_FechaDeModificacion = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    RECYECA07_ModificadoPor = table.Column<string>(type: "character varying(60)", maxLength: 60, nullable: true),
                    RECYECA07_FechaDeEliminacion = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    RECYECA07_EliminadoPor = table.Column<string>(type: "character varying(60)", maxLength: 60, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Recycling-ECA_07-Clasificacion_de_Material_key", x => x.RECYECA07_Id);
                });

            migrationBuilder.CreateTable(
                name: "Recycling-ECA_08-Detalle_Clasificacion_de_Material",
                columns: table => new
                {
                    RECYECA08_Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    RECYECA08_Valor = table.Column<double>(type: "double precision", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    RECYECA08_Id_de_Unidad = table.Column<int>(type: "integer", nullable: false),
                    RECYECA08_Id_de_Tipo_de_Material = table.Column<int>(type: "integer", nullable: false),
                    RECYECA08_Id_de_Clasificacion_de_Material = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Recycling-ECA_08-Detalle_Clasificacion_de_Material_key", x => x.RECYECA08_Id);
                    table.ForeignKey(
                        name: "Recycling-ECA_RECYECA08-ECA_RECYECA01_fkey",
                        column: x => x.RECYECA08_Id_de_Tipo_de_Material,
                        principalTable: "Recycling-ECA-01_Materiales",
                        principalColumn: "RECYECA01_Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "Recycling-ECA_RECYECA08-ECA_RECYECA07_fkey",
                        column: x => x.RECYECA08_Id_de_Clasificacion_de_Material,
                        principalTable: "Recycling-ECA_07-Clasificacion_de_Material",
                        principalColumn: "RECYECA07_Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "Recycling-ECA_RECYECA08-OrionDomainValue_fkey",
                        column: x => x.RECYECA08_Id_de_Unidad,
                        principalTable: "Orion-DomainValue",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Recycling-ECA_08-Detalle_Clasificacion_de_Material_RECYECA~1",
                table: "Recycling-ECA_08-Detalle_Clasificacion_de_Material",
                column: "RECYECA08_Id_de_Tipo_de_Material",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Recycling-ECA_08-Detalle_Clasificacion_de_Material_RECYECA~2",
                table: "Recycling-ECA_08-Detalle_Clasificacion_de_Material",
                column: "RECYECA08_Id_de_Unidad");

            migrationBuilder.CreateIndex(
                name: "IX_Recycling-ECA_08-Detalle_Clasificacion_de_Material_RECYECA0~",
                table: "Recycling-ECA_08-Detalle_Clasificacion_de_Material",
                column: "RECYECA08_Id_de_Clasificacion_de_Material");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Recycling-ECA_08-Detalle_Clasificacion_de_Material");

            migrationBuilder.DropTable(
                name: "Recycling-ECA_07-Clasificacion_de_Material");
        }
    }
}
