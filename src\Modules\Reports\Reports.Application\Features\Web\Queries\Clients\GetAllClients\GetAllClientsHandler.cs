﻿using AutoMapper;
using MediatR;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Domain.Entities.Headers;
using Reports.Application.DTOs.Clients;
using Reports.Domain;

namespace Reports.Application.Features.Web.Queries.Clients.GetAllClients;

internal class GetAllClientsHandler : MappingService, IRequestHandler<GetAllClientsRequest, GetAllClientsResponse>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly HeadersValuesProvider _headersValuesProvider;

    public GetAllClientsHandler(IMapper mapper, IReportsUnitOfWork unitOfWork, HeadersValuesProvider headersValuesProvider) : base(mapper)
    {
        _unitOfWork = unitOfWork;
        _headersValuesProvider = headersValuesProvider;
    }
    
    public async Task<GetAllClientsResponse> Handle(GetAllClientsRequest request, CancellationToken cancellationToken)
    {
        var clients = await _unitOfWork
            .Clients
            .GetAllAsync(
                isPaginated: false,
                cancellationToken: cancellationToken
            );
        
        _headersValuesProvider.Response.TotalRecords = clients.TotalRecords.ToString();
        
        return new GetAllClientsResponse(Mapper.Map<IEnumerable<ClientsResponseDto>>(clients.Results));
    }
}