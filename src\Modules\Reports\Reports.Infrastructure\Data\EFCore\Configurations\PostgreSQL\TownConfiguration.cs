﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class TownConfiguration : IEntityTypeConfiguration<Town>
{
    public void Configure(EntityTypeBuilder<Town> builder)
    {
        builder.ToTable(ReportsTableNames.Town);

        builder.Ignore(p => p.Id);

        builder
            .Property(p => p.Code)
            .HasMaxLength(5)
            .HasColumnName(TownColumns.Code);

        builder
            .Property(p => p.Name)
            .HasMaxLength(50)
            .HasColumnName(TownColumns.Name)
            .IsRequired();

        builder
            .Property(p => p.Department)
            .HasMaxLength(50)
            .HasColumnName(TownColumns.Department)
            .IsRequired();

        builder
            .Property(p => p.Province)
            .HasMaxLength(50)
            .HasColumnName(TownColumns.Province)
            .IsRequired();
            
        builder
            .HasKey(p => p.Code)
            .HasName(TownColumns.PrimaryKeyConstraintName);
    }
}