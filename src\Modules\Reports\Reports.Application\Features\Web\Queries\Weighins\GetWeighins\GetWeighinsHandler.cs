﻿using System.Linq.Expressions;
using AutoMapper;
using MediatR;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Domain.Entities;
using Orion.SharedKernel.Domain.Entities.Headers;
using Reports.Application.DTOs.Weighings;
using Reports.Domain;
using Reports.Domain.Entities;

namespace Reports.Application.Features.Web.Queries.Weighins.GetWeighins;

internal sealed class GetWeighinsHandler : MappingService, IRequestHandler<GetWeighinsRequest, GetWeighinsResponse>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly HeadersValuesProvider _headersValuesProvider;

    public GetWeighinsHandler(IMapper mapper, IReportsUnitOfWork classificationUnitOfWork, HeadersValuesProvider headersValuesProvider) : base(mapper)
    {
        _unitOfWork = classificationUnitOfWork;
        _headersValuesProvider = headersValuesProvider;
    }
    
    public async Task<GetWeighinsResponse> Handle(GetWeighinsRequest request, CancellationToken cancellationToken)
    {
        var predicate = GetPredicate(request);

        var weighins = await _unitOfWork
            .WeighingScales
            .GetAllAsync(
                includes: w => w.Town,
                predicate: predicate,
                useCache: false,
                pageNumber: _headersValuesProvider.Request.PageNumber,
                pageSize: _headersValuesProvider.Request.PageSize,
                maxQueryCount: int.MaxValue,
                cancellationToken: cancellationToken
            );
        
        var weighinsDtos = Mapper.Map<PaginatedResult<GetWeighingItemResponseDto>>(weighins);
        
        return new GetWeighinsResponse(weighinsDtos);
    }
    
    private static Expression<Func<WeighingScale, bool>> GetPredicate(GetWeighinsRequest request)
    {
        return w =>
            w.EntryDate >= request.FromDate
            && w.EntryDate <= request.ToDate
            && (string.IsNullOrEmpty(request.LicensePlate)
                || w.LicensePlate == request.LicensePlate)
            && (request.NIT == null
                || w.NIT == request.NIT);
    }
}