﻿using Common.Application.Services.Http.Orion;
using Common.Domain.Configuration;
using Common.Domain.Entities.Http.Orion.Request;
using Common.Domain.Entities.Http.Orion.Response;
using Microsoft.Extensions.Options;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.Http;

namespace Common.Infrastructure.Data.Http.Orion
{
    public class OrionApiService : ApiService, IOrionApiService
    {
        private readonly OrionApiConfiguration _configuration;
        private readonly ICurrentUserService _currentUserService;

        public OrionApiService(IOptions<OrionApiConfiguration> options, ICacheService cacheService, HttpClient httpClient, ICurrentUserService currentUserService) : base(cacheService, httpClient)
        {
            _configuration = options.Value;
            _currentUserService = currentUserService;
        }
        
        private async Task<IEnumerable<DomainValue>> GetDomainValuesByCode(GetDomainValuesByCodeRequest request, CancellationToken cancellationToken)
        {
            var endpoint = request.ToRoute(_configuration.GetDomainValuesByCode);
            
            var query = request.ToQuery(false);

            var headers = request.ToHeader();

            var response = await GetAll<DomainValue>(endpoint: endpoint, queryParams: query, headersParams: headers, useCache: false); 
            
            return response;
        }

        public async Task<IEnumerable<DomainValue>> GetPresentationTypes(CancellationToken cancellationToken)
        {
            var authorization = _currentUserService.GetCurrentUserToken();

            var request = new GetDomainValuesByCodeRequest(_configuration.MaterialPresentationDDVCode, authorization);

            return await GetDomainValuesByCode(request, cancellationToken);
        }

        public async Task<IEnumerable<DomainValue>> GetMaterialGroupTypes(CancellationToken cancellationToken)
        {
            var authorization = _currentUserService.GetCurrentUserToken();

            var request = new GetDomainValuesByCodeRequest(_configuration.MaterialGroupTypeDDVCode, authorization);

            return await GetDomainValuesByCode(request, cancellationToken);
        }
    }
}
