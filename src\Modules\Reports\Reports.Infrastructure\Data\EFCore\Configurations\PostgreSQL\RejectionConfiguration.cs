﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class RejectionConfiguration : IEntityTypeConfiguration<Rejection>
{
    public void Configure(EntityTypeBuilder<Rejection> builder)
    {
        builder.ToTable(ReportsTableNames.Rejections);

        builder
            .Property(p => p.Id)
            .HasColumnName(RejectionColumns.Id);

        builder
            .Property(p => p.LicensePlate)
            .HasColumnName(RejectionColumns.LicensePlate)
            .HasMaxLength(6)
            .IsRequired();

        builder
            .Property(p => p.RejectionDate)
            .HasColumnType("date")
            .HasColumnName(RejectionColumns.RejectionDate)
            .IsRequired();

        builder
            .Property(p => p.ExtendedRouteCode)
            .HasColumnName(RejectionColumns.ExtendedRouteCode)
            .HasColumnType("char(7)")
            .IsRequired();

        builder
            .Property(p => p.ECA)
            .HasColumnName(RejectionColumns.ECA)
            .IsRequired();

        builder
            .Property(p => p.Tonnage)
            .HasPrecision(18, 10)
            .HasColumnName(RejectionColumns.Tonnage)
            .IsRequired();
        
        builder.HasKey(p => p.Id)
               .HasName(RejectionColumns.PrimaryKeyConstraintName);
        
        builder
            .HasIndex(p => new
            {
                p.RejectionDate,
                p.LicensePlate
            })
            .HasDatabaseName(RejectionColumns.RejectionCompoundIndexName)
            .IsUnique(false)
            .HasAnnotation("Postgres:IndexMethod", "btree");
    }
}