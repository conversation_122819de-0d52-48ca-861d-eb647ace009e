using Orion.SharedKernel.Domain.Entities;
using Reports.Domain.Constants;
using Reports.Domain.ValueObjects;

namespace Reports.Domain.Entities;

public class ReportFormat14 : Entity<string>
{
    public long NUAP { get; private set; }
    public DestinationType DestinationType { get; private set; }
    public string DestinationCode { get; private set; }
    public string LicensePlate { get; private set; }
    public DateOnly VehicleArrival { get; private set; }
    public TimeOnly VehicleArrivalTime { get; private set; }
    public long MicrorouteId { get; private set; }
    public decimal UrbanCleaningTons { get; set; }
    public decimal SweepingTons { get; set; }
    public decimal NonRecyclableTons { get; set; }
    public decimal RejectedTons { get; set; }
    public decimal RecyclableTons { get; set; }
    public int MeasuringUnit { get; set; }
    public decimal Toll { get; set; }
    
    //Validations Fields
    public long? ServiceTicketId { get; set; }
    public string RecyclingArea { get; set; }
    public string ExtendedRouteCode { get; set; }
    public decimal TotalTons { get; set; }
    public bool? CompensationRelation { get; set; }
    public long? CompensationRelationTicketId { get; set; }
    
    public SUIReportFormat14 ToSUIReportFormat14() =>
        SUIReportFormat14.Create(NUAP, DestinationType, DestinationCode,
            LicensePlate, VehicleArrival, VehicleArrivalTime,
            MicrorouteId, UrbanCleaningTons, SweepingTons,
            NonRecyclableTons, RejectedTons, RecyclableTons,
            MeasuringUnit, Toll);
    
    public SUIReportFormat14Extended ToSUIReportFormat14Extended() =>
        SUIReportFormat14Extended.Create(NUAP, DestinationType, DestinationCode,
            LicensePlate, VehicleArrival, VehicleArrivalTime,
            MicrorouteId, UrbanCleaningTons, SweepingTons,
            NonRecyclableTons, RejectedTons, RecyclableTons,
            MeasuringUnit, Toll, ServiceTicketId, RecyclingArea, ExtendedRouteCode, TotalTons,
            CompensationRelation, CompensationRelationTicketId);
}