﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Reports.Domain.Constants;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class New_RSUI_Add_Fixed_RecyclingAreas_Table : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_02-Areas_de_Aprovechamiento",
                columns: table => new
                {
                    REPEM02_Codigo = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    REPEM02_Nombre = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Reporting-Emvarias_02-Areas_de_Aprovechamiento_key", x => x.REPEM02_Codigo);
                });
            
            migrationBuilder.Sql(
                $"INSERT INTO \"{ReportsTableNames.RecyclingArea}\" (\"{RecyclingAreaColumns.Name}\", \"{RecyclingAreaColumns.Code}\") " +
                @"VALUES ('Caldas',      440805129),
                             ('La Estrella', 441005380),
                             ('Sabaneta',    441205631),
                             ('Barbosa',     440605079),
                             ('Girardota',   441305308),
                             ('Medellín',    440405001),
                             ('Envigado',    440905266),
                             ('La Estrella', 440505380),
                             ('Copacabana',  441105212),
                             ('Bello',       440505088),
                             ('Itaguí',      440705360)"
            );
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_02-Areas_de_Aprovechamiento");
        }
    }
}
