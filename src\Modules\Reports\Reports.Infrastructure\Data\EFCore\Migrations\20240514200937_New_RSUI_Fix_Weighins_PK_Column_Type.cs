﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class New_RSUI_Fix_Weighins_PK_Column_Type : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "Temp_REPEM03_Id",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                type: "bigint",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.Sql(
                @"UPDATE ""Reporting-Emvarias_03-Pesaje_de_Balanza""
                SET ""Temp_REPEM03_Id"" = CAST(""REPEM03_Id"" AS bigint)");
            
            migrationBuilder.AlterColumn<long>(
                name: "Temp_REPEM03_Id",
                table: "Reporting-Emvarias_03-P<PERSON>aje_de_Balanza",
                type: "bigint",
                nullable: false,
                oldDefaultValue: 0);

            migrationBuilder.DropPrimaryKey(
                name: "Reporting-Emvarias_03-Pesaje_de_Balanza_key",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza");

            migrationBuilder.DropColumn(
                name: "REPEM03_Id",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza");

            migrationBuilder.RenameColumn(
                name: "Temp_REPEM03_Id",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                newName: "REPEM03_Id");

            migrationBuilder.AddPrimaryKey(
                name: "Reporting-Emvarias_03-Pesaje_de_Balanza_key",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                column: "REPEM03_Id");

            migrationBuilder.AlterColumn<long>(
                name: "REPEM03_Id",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                type: "bigint",
                nullable: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "Temp_REPEM03_Id",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                type: "character varying(10)",
                nullable: false,
                defaultValue: "0");
            
            migrationBuilder.Sql(
                @"UPDATE ""Reporting-Emvarias_03-Pesaje_de_Balanza""
                SET ""Temp_REPEM03_Id"" = CAST(""REPEM03_Id"" AS varchar(10))");
            
            migrationBuilder.AlterColumn<long>(
                name: "Temp_REPEM03_Id",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                type: "character varying(10)",
                nullable: false,
                oldDefaultValue: "0");
            
            migrationBuilder.DropPrimaryKey(
                name: "Reporting-Emvarias_03-Pesaje_de_Balanza_key",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza");

            migrationBuilder.DropColumn(
                name: "REPEM03_Id",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza");

            migrationBuilder.RenameColumn(
                name: "Temp_REPEM03_Id",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                newName: "REPEM03_Id");

            migrationBuilder.AddPrimaryKey(
                name: "Reporting-Emvarias_03-Pesaje_de_Balanza_key",
                table: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                column: "REPEM03_Id");
            
            migrationBuilder.AlterColumn<long>(
                    name: "REPEM03_Id",
                    table: "Reporting-Emvarias_03-Pesaje_de_Balanza",
                    type: "character varying(10)",
                    maxLength: 10,
                    nullable: false);
        }
    }
}
