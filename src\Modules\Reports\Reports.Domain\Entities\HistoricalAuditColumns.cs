using Orion.SharedKernel.Domain.Entities;
using Reports.Domain.Constants;

namespace Reports.Domain.Entities;

/// <summary>
/// Generic audit entity that stores historical modifications for any entity using BSON/JSON format.
/// This entity follows DDD principles and provides a reusable audit logging mechanism.
/// </summary>
public class HistoricalAuditColumns : Entity<long>
{
    /// <summary>
    /// The ID of the entity being audited (can be any type converted to string)
    /// </summary>
    public string EntityId { get; set; } = string.Empty;
    
    /// <summary>
    /// The name of the table/entity being audited
    /// </summary>
    public string TableName { get; set; } = string.Empty;
    
    /// <summary>
    /// The type of operation performed (Created, Modified, Annulled)
    /// </summary>
    public HistoricalMovementType ActionType { get; set; }
    
    /// <summary>
    /// The user who performed the action
    /// </summary>
    public string User { get; set; } = string.Empty;
    
    /// <summary>
    /// When the action was performed
    /// </summary>
    public DateTime ActionDate { get; set; }
    
    /// <summary>
    /// JSON array containing historical audit records for this entity.
    /// Each record contains the complete state of the entity at the time of the change.
    /// </summary>
    public string HistoricalData { get; set; } = "[]";
    
    /// <summary>
    /// Optional metadata about the operation (e.g., operation context, batch information)
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Creates a new audit record for entity creation
    /// </summary>
    public static HistoricalAuditColumns CreateForInsert<TEntity>(
        string entityId, 
        string tableName, 
        string user, 
        TEntity currentState,
        string? metadata = null) where TEntity : class
    {
        return new HistoricalAuditColumns
        {
            EntityId = entityId,
            TableName = tableName,
            ActionType = HistoricalMovementType.Created,
            User = user,
            ActionDate = DateTime.UtcNow,
            HistoricalData = SerializeToJson(new[] { new AuditRecord<TEntity>
            {
                ActionType = HistoricalMovementType.Created,
                ActionDate = DateTime.UtcNow,
                User = user,
                CurrentState = currentState,
                PreviousState = null
            }}),
            Metadata = metadata
        };
    }

    /// <summary>
    /// Creates a new audit record for entity modification
    /// </summary>
    public static HistoricalAuditColumns CreateForUpdate<TEntity>(
        string entityId, 
        string tableName, 
        string user, 
        TEntity previousState,
        TEntity currentState,
        string? metadata = null) where TEntity : class
    {
        return new HistoricalAuditColumns
        {
            EntityId = entityId,
            TableName = tableName,
            ActionType = HistoricalMovementType.Modified,
            User = user,
            ActionDate = DateTime.UtcNow,
            HistoricalData = SerializeToJson(new[] { new AuditRecord<TEntity>
            {
                ActionType = HistoricalMovementType.Modified,
                ActionDate = DateTime.UtcNow,
                User = user,
                CurrentState = currentState,
                PreviousState = previousState
            }}),
            Metadata = metadata
        };
    }

    /// <summary>
    /// Creates a new audit record for entity cancellation/deletion
    /// </summary>
    public static HistoricalAuditColumns CreateForCancellation<TEntity>(
        string entityId, 
        string tableName, 
        string user, 
        TEntity previousState,
        string? metadata = null) where TEntity : class
    {
        return new HistoricalAuditColumns
        {
            EntityId = entityId,
            TableName = tableName,
            ActionType = HistoricalMovementType.Annulled,
            User = user,
            ActionDate = DateTime.UtcNow,
            HistoricalData = SerializeToJson(new[] { new AuditRecord<TEntity>
            {
                ActionType = HistoricalMovementType.Annulled,
                ActionDate = DateTime.UtcNow,
                User = user,
                CurrentState = null,
                PreviousState = previousState
            }}),
            Metadata = metadata
        };
    }

    /// <summary>
    /// Adds a new audit record to the existing historical data
    /// </summary>
    public void AddAuditRecord<TEntity>(
        HistoricalMovementType actionType,
        string user,
        TEntity? currentState = null,
        TEntity? previousState = null) where TEntity : class
    {
        var existingRecords = DeserializeFromJson<AuditRecord<TEntity>>(HistoricalData);
        var newRecord = new AuditRecord<TEntity>
        {
            ActionType = actionType,
            ActionDate = DateTime.UtcNow,
            User = user,
            CurrentState = currentState,
            PreviousState = previousState
        };

        var updatedRecords = existingRecords.Append(newRecord);
        HistoricalData = SerializeToJson(updatedRecords);
        ActionType = actionType;
        ActionDate = DateTime.UtcNow;
        User = user;
    }

    /// <summary>
    /// Gets all audit records for this entity
    /// </summary>
    public IEnumerable<AuditRecord<TEntity>> GetAuditRecords<TEntity>() where TEntity : class
    {
        return DeserializeFromJson<AuditRecord<TEntity>>(HistoricalData);
    }

    private static string SerializeToJson<T>(IEnumerable<T> data)
    {
        return System.Text.Json.JsonSerializer.Serialize(data, new System.Text.Json.JsonSerializerOptions
        {
            PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase,
            WriteIndented = false
        });
    }

    private static IEnumerable<T> DeserializeFromJson<T>(string json)
    {
        if (string.IsNullOrWhiteSpace(json) || json == "[]")
            return Enumerable.Empty<T>();

        return System.Text.Json.JsonSerializer.Deserialize<IEnumerable<T>>(json, new System.Text.Json.JsonSerializerOptions
        {
            PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
        }) ?? Enumerable.Empty<T>();
    }
}

/// <summary>
/// Represents a single audit record within the historical data
/// </summary>
public class AuditRecord<TEntity> where TEntity : class
{
    public HistoricalMovementType ActionType { get; set; }
    public DateTime ActionDate { get; set; }
    public string User { get; set; } = string.Empty;
    public TEntity? CurrentState { get; set; }
    public TEntity? PreviousState { get; set; }
}
