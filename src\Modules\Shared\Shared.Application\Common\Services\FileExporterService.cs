using NPOI.XSSF.UserModel;
using Shared.Application.Common.Helpers;

namespace Shared.Application.Common.Services;

public class FileExporterService :  IFileExporterService
{
    private const string DefaultDateFormat = "yyyy-MM-ddTHH:mm:ss";
    public byte[] GetExcelBytes<TEntity>(IEnumerable<TEntity> data, string? headerRowTitle, string? dateFormat) where TEntity : class
    {
        XSSFWorkbook workbook = new();

        var sheet = workbook.CreateSheet("hoja");

        var allProperties = typeof(TEntity).GetProperties();
        
        var hasTitleRow = !string.IsNullOrEmpty(headerRowTitle);
        
        if (hasTitleRow)
            sheet.CreateTitleRow(headerRowTitle!, allProperties);

        var processedProperties = sheet.CreateColumnNamesRow(allProperties, hasTitleRow);
        
        dateFormat ??= DefaultDateFormat;
        
        sheet.CreateDataRows(entities: data.ToList(), processedProperties, hasTitleRow, dateFormat);

        using MemoryStream stream = new();
        workbook.Write(stream);

        return stream.ToArray();
    }

    public byte[] GetCsvBytes<TEntity>(IEnumerable<TEntity> entities, string? dateFormat) where TEntity : class
    {
        using MemoryStream stream = new();

        using StreamWriter writer = new(stream);

        var allProperties = typeof(TEntity).GetProperties();

        var processedProperties = writer.CreateHeadersForCsv<TEntity>(allProperties);
        
        dateFormat ??= DefaultDateFormat;

        writer.CreateRowsForCsv(entities, processedProperties, dateFormat);

        writer.Flush();
        stream.Position = 0;

        return stream.ToArray();
    }
}