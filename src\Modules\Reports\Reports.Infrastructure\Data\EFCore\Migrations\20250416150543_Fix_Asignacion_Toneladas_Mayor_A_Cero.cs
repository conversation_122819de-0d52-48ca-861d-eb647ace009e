﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Fix_Asignacion_Toneladas_Mayor_A_Cero : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                create or replace view public.""REPEM_Detalle_Compensacion_Tickets""
                (""Id_Ticket"", ""NUAP"", ""Fecha_Hora_Pesaje"", ""Toneladas_Resultantes"", ""Tipo_Compensacion"") as
                WITH cte AS (SELECT dtc.""Id_Ticket"",
                                    dtc.""NUAP"",
                                    dtc.""Numero_de_Microruta"",
                                    dtc.""Numero_Ruta_Intendencia"",
                                    dtc.""Patente"",
                                    dtc.""Toneladas_Distribuidas"",
                                    dtc.""PesoTotal_Toneladas"",
                                    dtc.""Porcentaje_No_Aforado"",
                                    dtc.""Porcentaje_Limpieza_Urbana"",
                                    dtc.""Porcentaje_Barrido"",
                                    dtc.""Porcentaje_Residuos_Aprovechables"",
                                    dtc.""Fecha_Hora_Pesaje"",
                                    dtc.""Sumatoria_Acumulada"",
                                    CASE
                                        WHEN dtc.""Sumatoria_Acumulada"" < dtc.""PesoTotal_Toneladas"" THEN 0::numeric
                                        ELSE dtc.""Sumatoria_Acumulada"" - dtc.""PesoTotal_Toneladas""::numeric
                                        END AS ""Exceso_Acumulado""
                             FROM ""REPEM_Dependencia_Toneladas_A_Compensar"" dtc),
                     cnt AS (SELECT cte.""Id_Ticket"",
                                    count(cte.""Id_Ticket"") AS ""Cantidad""
                             FROM cte
                             GROUP BY cte.""Id_Ticket""),
                     rst AS (SELECT DISTINCT ON (cte.""Id_Ticket"") cte.""Id_Ticket"",
                                                                  cte.""PesoTotal_Toneladas"" -
                                                                  max(cte.""Sumatoria_Acumulada"") OVER (PARTITION BY cte.""Id_Ticket"") AS ""Resto_Disponible""
                             FROM cte
                                      JOIN cnt ON cnt.""Id_Ticket"" = cte.""Id_Ticket""
                             WHERE cte.""Exceso_Acumulado"" = 0::numeric
                               AND cnt.""Cantidad"" > 1
                             ORDER BY cte.""Id_Ticket"", cte.""Toneladas_Distribuidas"" DESC),
                     sprst AS (SELECT DISTINCT ON (cte.""Id_Ticket"") cte.""Id_Ticket"",
                                                                    cte.""Toneladas_Distribuidas"" - cte.""PesoTotal_Toneladas""::numeric AS ""Resto_Disponible""
                               FROM cte
                                        JOIN cnt ON cnt.""Id_Ticket"" = cte.""Id_Ticket""
                               WHERE cte.""Exceso_Acumulado"" > 0::numeric
                                 AND cnt.""Cantidad"" = 1
                               ORDER BY cte.""Id_Ticket"", cte.""Toneladas_Distribuidas"" DESC),
                     pcomp AS (SELECT cte.""Id_Ticket"",
                                      cte.""NUAP"",
                                      cte.""Numero_de_Microruta"",
                                      cte.""Numero_Ruta_Intendencia"",
                                      cte.""Patente"",
                                      cte.""Toneladas_Distribuidas"",
                                      cte.""PesoTotal_Toneladas"",
                                      cte.""Porcentaje_No_Aforado"",
                                      cte.""Porcentaje_Limpieza_Urbana"",
                                      cte.""Porcentaje_Barrido"",
                                      cte.""Porcentaje_Residuos_Aprovechables"",
                                      cte.""Fecha_Hora_Pesaje"",
                                      cte.""Sumatoria_Acumulada"",
                                      cte.""Exceso_Acumulado"",
                                      cte.""Toneladas_Distribuidas"" - rst.""Resto_Disponible"" AS ""Toneladas_Resultantes""
                               FROM cte
                                        JOIN (SELECT icte.""Id_Ticket"",
                                                     max(icte.""Toneladas_Distribuidas"") AS max_toneladas
                                              FROM cte icte
                                              GROUP BY icte.""Id_Ticket"") sub
                                             ON cte.""Id_Ticket"" = sub.""Id_Ticket"" AND cte.""Toneladas_Distribuidas"" = sub.max_toneladas
                                        JOIN rst ON rst.""Id_Ticket"" = cte.""Id_Ticket""
                               WHERE cte.""Exceso_Acumulado"" > 0::numeric
                               UNION ALL
                               SELECT cte.""Id_Ticket"",
                                      cte.""NUAP"",
                                      cte.""Numero_de_Microruta"",
                                      cte.""Numero_Ruta_Intendencia"",
                                      cte.""Patente"",
                                      cte.""Toneladas_Distribuidas"",
                                      cte.""PesoTotal_Toneladas"",
                                      cte.""Porcentaje_No_Aforado"",
                                      cte.""Porcentaje_Limpieza_Urbana"",
                                      cte.""Porcentaje_Barrido"",
                                      cte.""Porcentaje_Residuos_Aprovechables"",
                                      cte.""Fecha_Hora_Pesaje"",
                                      cte.""Sumatoria_Acumulada"",
                                      cte.""Exceso_Acumulado"",
                                      sprst.""Resto_Disponible"" AS ""Toneladas_Resultantes""
                               FROM cte
                                        JOIN sprst ON sprst.""Id_Ticket"" = cte.""Id_Ticket"")
                SELECT cte.""Id_Ticket"",
                       cte.""NUAP"",
                       cte.""Fecha_Hora_Pesaje"",
                       cte.""Toneladas_Distribuidas"" + COALESCE(pcomp.""Toneladas_Resultantes"", 0::numeric) AS ""Toneladas_Resultantes"",
                       CASE
                           WHEN cte.""Exceso_Acumulado"" = 0::numeric OR pcomp.* IS NOT NULL THEN 'ORIGINAL'::text
                           ELSE 'TOTAL'::text
                       END                                                                     AS ""Tipo_Compensacion""
                FROM cte
                         LEFT JOIN pcomp ON pcomp.""Id_Ticket"" = cte.""Id_Ticket"" AND cte.""NUAP"" = pcomp.""NUAP""
                WHERE pcomp is NULL
                UNION ALL
                SELECT pcomp.""Id_Ticket"",
                       pcomp.""NUAP"",
                       pcomp.""Fecha_Hora_Pesaje"",
                       pcomp.""Toneladas_Distribuidas"",
                       'TOTAL'::text AS ""Tipo_Compensacion""
                FROM pcomp;
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Detalle_Compensacion_Tickets""
                            (""Id_Ticket"", ""NUAP"", ""Fecha_Hora_Pesaje"", ""Toneladas_Resultantes"", ""Tipo_Compensacion"") as
                WITH cte AS (SELECT dtc.""Id_Ticket"",
                                    dtc.""NUAP"",
                                    dtc.""Numero_de_Microruta"",
                                    dtc.""Numero_Ruta_Intendencia"",
                                    dtc.""Patente"",
                                    dtc.""Toneladas_Distribuidas"",
                                    dtc.""PesoTotal_Toneladas"",
                                    dtc.""Porcentaje_No_Aforado"",
                                    dtc.""Porcentaje_Limpieza_Urbana"",
                                    dtc.""Porcentaje_Barrido"",
                                    dtc.""Porcentaje_Residuos_Aprovechables"",
                                    dtc.""Fecha_Hora_Pesaje"",
                                    dtc.""Sumatoria_Acumulada"",
                                    CASE
                                        WHEN dtc.""Sumatoria_Acumulada"" < dtc.""PesoTotal_Toneladas"" THEN 0::numeric
                                        ELSE dtc.""Sumatoria_Acumulada"" - dtc.""PesoTotal_Toneladas""::numeric
                                        END AS ""Exceso_Acumulado""
                             FROM ""REPEM_Dependencia_Toneladas_A_Compensar"" dtc),
                     cnt AS (SELECT cte.""Id_Ticket"",
                                    count(cte.""Id_Ticket"") AS ""Cantidad""
                             FROM cte
                             GROUP BY cte.""Id_Ticket""),
                     rst AS (SELECT DISTINCT ON (cte.""Id_Ticket"") cte.""Id_Ticket"",
                                                                  cte.""PesoTotal_Toneladas"" -
                                                                  max(cte.""Sumatoria_Acumulada"") OVER (PARTITION BY cte.""Id_Ticket"") AS ""Resto_Disponible""
                             FROM cte
                                      JOIN cnt ON cnt.""Id_Ticket"" = cte.""Id_Ticket""
                             WHERE cte.""Exceso_Acumulado"" = 0::numeric
                               AND cnt.""Cantidad"" > 1
                             ORDER BY cte.""Id_Ticket"", cte.""Toneladas_Distribuidas"" DESC),
                     sprst AS (SELECT DISTINCT ON (cte.""Id_Ticket"") cte.""Id_Ticket"",
                                                                    cte.""Toneladas_Distribuidas"" - cte.""PesoTotal_Toneladas""::numeric AS ""Resto_Disponible""
                               FROM cte
                                        JOIN cnt ON cnt.""Id_Ticket"" = cte.""Id_Ticket""
                               WHERE cte.""Exceso_Acumulado"" > 0::numeric
                                 AND cnt.""Cantidad"" = 1
                               ORDER BY cte.""Id_Ticket"", cte.""Toneladas_Distribuidas"" DESC),
                     pcomp AS (SELECT cte.""Id_Ticket"",
                                      cte.""NUAP"",
                                      cte.""Numero_de_Microruta"",
                                      cte.""Numero_Ruta_Intendencia"",
                                      cte.""Patente"",
                                      cte.""Toneladas_Distribuidas"",
                                      cte.""PesoTotal_Toneladas"",
                                      cte.""Porcentaje_No_Aforado"",
                                      cte.""Porcentaje_Limpieza_Urbana"",
                                      cte.""Porcentaje_Barrido"",
                                      cte.""Porcentaje_Residuos_Aprovechables"",
                                      cte.""Fecha_Hora_Pesaje"",
                                      cte.""Sumatoria_Acumulada"",
                                      cte.""Exceso_Acumulado"",
                                      cte.""Toneladas_Distribuidas"" - rst.""Resto_Disponible"" AS ""Toneladas_Resultantes""
                               FROM cte
                                        JOIN (SELECT icte.""Id_Ticket"",
                                                     max(icte.""Toneladas_Distribuidas"") AS max_toneladas
                                              FROM cte icte
                                              GROUP BY icte.""Id_Ticket"") sub
                                             ON cte.""Id_Ticket"" = sub.""Id_Ticket"" AND cte.""Toneladas_Distribuidas"" = sub.max_toneladas
                                        JOIN rst ON rst.""Id_Ticket"" = cte.""Id_Ticket""
                               WHERE cte.""Exceso_Acumulado"" > 0::numeric
                               UNION ALL
                               SELECT cte.""Id_Ticket"",
                                      cte.""NUAP"",
                                      cte.""Numero_de_Microruta"",
                                      cte.""Numero_Ruta_Intendencia"",
                                      cte.""Patente"",
                                      cte.""Toneladas_Distribuidas"",
                                      cte.""PesoTotal_Toneladas"",
                                      cte.""Porcentaje_No_Aforado"",
                                      cte.""Porcentaje_Limpieza_Urbana"",
                                      cte.""Porcentaje_Barrido"",
                                      cte.""Porcentaje_Residuos_Aprovechables"",
                                      cte.""Fecha_Hora_Pesaje"",
                                      cte.""Sumatoria_Acumulada"",
                                      cte.""Exceso_Acumulado"",
                                      sprst.""Resto_Disponible"" AS ""Toneladas_Resultantes""
                               FROM cte
                                        JOIN sprst ON sprst.""Id_Ticket"" = cte.""Id_Ticket"")
                SELECT cte.""Id_Ticket"",
                       cte.""NUAP"",
                       cte.""Fecha_Hora_Pesaje"",
                       cte.""Toneladas_Distribuidas"" - COALESCE(pcomp.""Toneladas_Resultantes"", 0::numeric) AS ""Toneladas_Resultantes"",
                       CASE
                           WHEN cte.""Exceso_Acumulado"" = 0::numeric OR pcomp.* IS NOT NULL THEN 'ORIGINAL'::text
                           ELSE 'TOTAL'::text
                           END                                                                            AS ""Tipo_Compensacion""
                FROM cte
                         LEFT JOIN pcomp ON pcomp.""Id_Ticket"" = cte.""Id_Ticket"" AND cte.""NUAP"" = pcomp.""NUAP""
                UNION ALL
                SELECT pcomp.""Id_Ticket"",
                       pcomp.""NUAP"",
                       pcomp.""Fecha_Hora_Pesaje"",
                       pcomp.""Toneladas_Resultantes"",
                       'PARCIAL'::text AS ""Tipo_Compensacion""
                FROM pcomp;
            ");
        }
    }
}
