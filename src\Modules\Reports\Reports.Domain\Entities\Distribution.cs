﻿using Orion.SharedKernel.Domain.Entities;

namespace Reports.Domain.Entities;

public class Distribution : Entity<int>
{
    public int Year { get; set; }
    public int Month { get; set; }
    public int NUAP { get; set; }
    public string RecyclingArea { get; set; } = string.Empty;
    public int Trips { get; set; }
    public decimal DeviationTons { get; set; }
    public decimal DistributedTons { get; set; }
    public decimal TollSharedRouteTons { get; set; }
    public decimal DistributionTollPercentage { get; set; }
    public decimal ReportedTons { get; set; }
    public decimal RoundedDistributionTollPercentage => Math.Round(DistributionTollPercentage, 2, MidpointRounding.AwayFromZero);
    public decimal TotalTons => DeviationTons + DistributedTons * Trips;
}