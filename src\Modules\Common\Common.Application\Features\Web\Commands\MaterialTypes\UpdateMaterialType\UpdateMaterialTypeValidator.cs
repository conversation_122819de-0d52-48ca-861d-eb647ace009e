﻿namespace Common.Application.Features.Web.Commands.MaterialTypes.UpdateMaterialType;

public class UpdateMaterialTypeValidator : AbstractValidator<UpdateMaterialTypeRequest>
{
    public UpdateMaterialTypeValidator()
    {
        RuleFor(r => r.Id)
            .NotEmpty()
                .WithMessage("El id es requerido.");

		RuleFor(r => r)
			.Must(r => !string.IsNullOrEmpty(r.Name) || !string.IsNullOrEmpty(r.Description) || r.GroupId > 0)
			.WithMessage("Debe especificar al menos uno de los campos: Nombre, Descripción o Id de Grupo.");

		
		RuleFor(r => r.GroupId)
			.GreaterThan(0)
				.WithMessage("El campo Id de Grupo debe ser mayor a 0.");
		
	}
}