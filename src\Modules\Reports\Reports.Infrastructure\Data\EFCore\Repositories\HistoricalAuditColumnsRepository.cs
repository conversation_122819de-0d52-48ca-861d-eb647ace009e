using EFCore.BulkExtensions;
using Microsoft.EntityFrameworkCore;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Domain.Entities;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;
using Reports.Domain.Constants;
using Reports.Domain.Entities;
using Reports.Domain.Repositories;

namespace Reports.Infrastructure.Data.EFCore.Repositories;

public class HistoricalAuditColumnsRepository : Repository<HistoricalAuditColumns, long, ReportsDbContext>, IHistoricalAuditColumnsRepository
{
    public HistoricalAuditColumnsRepository(IDbContextProvider<ReportsDbContext> dbContextProvider, ICacheService cacheService)
        : base(dbContextProvider, cacheService) { }

    public async Task<IEnumerable<HistoricalAuditColumns>> GetAuditRecordsForEntityAsync(
        string entityId, 
        string tableName, 
        CancellationToken cancellationToken)
    {
        return await _context.HistoricalAuditColumns
            .AsNoTracking()
            .Where(x => x.EntityId == entityId && x.TableName == tableName)
            .OrderByDescending(x => x.ActionDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<HistoricalAuditColumns>> GetAuditRecordsForEntitiesAsync(
        IEnumerable<string> entityIds, 
        string tableName, 
        CancellationToken cancellationToken)
    {
        var entityIdsList = entityIds.ToList();
        
        return await _context.HistoricalAuditColumns
            .AsNoTracking()
            .Where(x => entityIdsList.Contains(x.EntityId) && x.TableName == tableName)
            .OrderByDescending(x => x.ActionDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<PaginatedResult<HistoricalAuditColumns>> GetAuditRecordsByActionTypeAsync(
        HistoricalMovementType actionType,
        string? tableName = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int pageNumber = 1,
        int pageSize = 25,
        CancellationToken cancellationToken = default)
    {
        var query = _context.HistoricalAuditColumns
            .AsNoTracking()
            .Where(x => x.ActionType == actionType);

        if (!string.IsNullOrEmpty(tableName))
            query = query.Where(x => x.TableName == tableName);

        if (fromDate.HasValue)
            query = query.Where(x => x.ActionDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(x => x.ActionDate <= toDate.Value);

        var totalCount = await query.CountAsync(cancellationToken);

        var results = await query
            .OrderByDescending(x => x.ActionDate)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PaginatedResult<HistoricalAuditColumns>
        {
            Results = results,
            TotalRecords = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize
        };
    }

    public async Task<PaginatedResult<HistoricalAuditColumns>> GetAuditRecordsByUserAsync(
        string user,
        string? tableName = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int pageNumber = 1,
        int pageSize = 25,
        CancellationToken cancellationToken = default)
    {
        var query = _context.HistoricalAuditColumns
            .AsNoTracking()
            .Where(x => x.User == user);

        if (!string.IsNullOrEmpty(tableName))
            query = query.Where(x => x.TableName == tableName);

        if (fromDate.HasValue)
            query = query.Where(x => x.ActionDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(x => x.ActionDate <= toDate.Value);

        var totalCount = await query.CountAsync(cancellationToken);

        var results = await query
            .OrderByDescending(x => x.ActionDate)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PaginatedResult<HistoricalAuditColumns>
        {
            Results = results,
            TotalRecords = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize
        };
    }

    public async Task BulkInsertAsync(IEnumerable<HistoricalAuditColumns> auditRecords, CancellationToken cancellationToken)
    {
        await _context.BulkInsertAsync(auditRecords.ToList(),
            new BulkConfig
            {
                PreserveInsertOrder = false,
                SetOutputIdentity = false
            },
            cancellationToken: cancellationToken);
    }

    public async Task<bool> HasAuditRecordsAsync(string entityId, string tableName, CancellationToken cancellationToken)
    {
        return await _context.HistoricalAuditColumns
            .AsNoTracking()
            .AnyAsync(x => x.EntityId == entityId && x.TableName == tableName, cancellationToken);
    }

    public async Task<HistoricalAuditColumns?> GetLatestAuditRecordAsync(
        string entityId, 
        string tableName, 
        CancellationToken cancellationToken)
    {
        return await _context.HistoricalAuditColumns
            .AsNoTracking()
            .Where(x => x.EntityId == entityId && x.TableName == tableName)
            .OrderByDescending(x => x.ActionDate)
            .FirstOrDefaultAsync(cancellationToken);
    }
}
