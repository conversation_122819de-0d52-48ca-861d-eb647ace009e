using System.Net;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Orion.SharedKernel.Api.Controllers;
using Orion.SharedKernel.Domain.Entities.Error;
using Reports.Application.Features.Web.Queries.Microroutes.GetAllRegulatoryRouteCodes;

namespace Reports.Api.Controllers.v1.Web;

[ApiVersion("1.0")]
[Authorize]
public class MicrorouteController : OrionController
{
    /// <summary>
    /// Obtiene la lista completa de codigos de ruta regulatorios de la superintendencia
    /// </summary>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(List<GetAllRegulatoryRouteCodesResponse>))]
    [ProducesResponseType((int)HttpStatusCode.NoContent, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpGet("regulatory-routes")]
    public async Task<IActionResult> GetAll()
    {
        var request = new GetAllRegulatoryRouteCodesRequest();

        var response = await Mediator.Send(request);

        return Ok(response.RegulatoryRouteCodes);
    }
}