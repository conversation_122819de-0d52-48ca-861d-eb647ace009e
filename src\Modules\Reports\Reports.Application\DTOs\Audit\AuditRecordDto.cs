using Reports.Domain.Constants;

namespace Reports.Application.DTOs.Audit;

/// <summary>
/// DTO representing an audit record
/// </summary>
public class AuditRecordDto
{
    public long Id { get; set; }
    public string EntityId { get; set; } = string.Empty;
    public string TableName { get; set; } = string.Empty;
    public HistoricalMovementType ActionType { get; set; }
    public string ActionTypeDescription { get; set; } = string.Empty;
    public string User { get; set; } = string.Empty;
    public DateTime ActionDate { get; set; }
    public string HistoricalData { get; set; } = string.Empty;
    public string? Metadata { get; set; }
    public IEnumerable<AuditRecordDetailDto> AuditDetails { get; set; } = Enumerable.Empty<AuditRecordDetailDto>();
}

/// <summary>
/// DTO representing individual audit record details within the historical data
/// </summary>
public class AuditRecordDetailDto
{
    public HistoricalMovementType ActionType { get; set; }
    public string ActionTypeDescription { get; set; } = string.Empty;
    public DateTime ActionDate { get; set; }
    public string User { get; set; } = string.Empty;
    public object? CurrentState { get; set; }
    public object? PreviousState { get; set; }
}

/// <summary>
/// DTO for paginated audit records
/// </summary>
public class PaginatedAuditRecordsDto
{
    public IEnumerable<AuditRecordDto> Results { get; set; } = Enumerable.Empty<AuditRecordDto>();
    public int TotalRecords { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalRecords / PageSize);
    public bool HasNextPage => PageNumber < TotalPages;
    public bool HasPreviousPage => PageNumber > 1;
}

/// <summary>
/// DTO for audit statistics
/// </summary>
public class AuditStatisticsDto
{
    public string TableName { get; set; } = string.Empty;
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public int TotalRecords { get; set; }
    public int CreatedRecords { get; set; }
    public int ModifiedRecords { get; set; }
    public int AnnulledRecords { get; set; }
    public IEnumerable<UserAuditStatisticDto> UserStatistics { get; set; } = Enumerable.Empty<UserAuditStatisticDto>();
    public IEnumerable<DailyAuditStatisticDto> DailyStatistics { get; set; } = Enumerable.Empty<DailyAuditStatisticDto>();
}

/// <summary>
/// DTO for user-specific audit statistics
/// </summary>
public class UserAuditStatisticDto
{
    public string User { get; set; } = string.Empty;
    public int TotalOperations { get; set; }
    public int CreatedRecords { get; set; }
    public int ModifiedRecords { get; set; }
    public int AnnulledRecords { get; set; }
}

/// <summary>
/// DTO for daily audit statistics
/// </summary>
public class DailyAuditStatisticDto
{
    public DateTime Date { get; set; }
    public int TotalOperations { get; set; }
    public int CreatedRecords { get; set; }
    public int ModifiedRecords { get; set; }
    public int AnnulledRecords { get; set; }
}
