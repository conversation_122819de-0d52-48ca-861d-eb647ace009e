using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Domain.Entities;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;
using Reports.Domain.Entities;
using Reports.Domain.Repositories;

namespace Reports.Infrastructure.Data.EFCore.Repositories;

public class ReportFormat14Repository : Repository<ReportFormat14, string, ReportsDbContext>, IReportFormat14Repository
{
    public ReportFormat14Repository(IDbContextProvider<ReportsDbContext> dbContextProvider, ICacheService cacheService) : base(dbContextProvider, cacheService) { }
    
    public async Task<PaginatedResult<ReportFormat14>> GetFilteredReportAsync(Expression<Func<ReportFormat14, bool>> predicate,
        CancellationToken cancellationToken,
        bool isPaginated = true,
        int pageNumber = 1,
        int pageSize = 25)
    {
        var query = _context.ReportsFormat14
            .AsQueryable()
            .AsNoTracking()
            .Where(predicate);
        
        if (!isPaginated)
            return new PaginatedResult<ReportFormat14> { Results = await query.ToListAsync(cancellationToken) };

        var totalCount = await query.CountAsync(cancellationToken);
        
        var result = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken: cancellationToken);

        return new PaginatedResult<ReportFormat14>
        {
            Results = result,
            TotalRecords = totalCount,
            PageSize = pageSize,
            PageNumber = pageNumber
        };
    }

    public async Task<List<ReportFormat14>> GetFilteredReportAsync(Expression<Func<ReportFormat14, bool>> predicate, CancellationToken cancellationToken)
    {
        return await _context.ReportsFormat14
            .AsQueryable()
            .AsNoTracking()
            .Where(predicate)
            .ToListAsync(cancellationToken);
    }
}