﻿using System.Text.Json.Serialization;
using Classification.Application.DTOs.MaterialEntry;
using MediatR;

namespace Classification.Application.Features.Web.Commands.MaterialEntries.CreateMaterialEntry;

public record CreateMaterialEntryRequest : IRequest<CreateMaterialEntryResponse>
{
    public MaterialEntryRequestDto MaterialEntryRequest { get; set; }
    
    public CreateMaterialEntryRequest(MaterialEntryRequestDto requestRequestDto)
    {
        MaterialEntryRequest = requestRequestDto;
    }
}