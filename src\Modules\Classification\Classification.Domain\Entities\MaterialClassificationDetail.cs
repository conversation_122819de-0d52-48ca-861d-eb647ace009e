﻿using Common.Domain.Entities;
using Common.Domain.Entities.Http.Orion.Response;
using Orion.SharedKernel.Domain.Entities;
using Orion.SharedKernel.Domain.Entities.Audit.DeletionAudit;

namespace Classification.Domain.Entities;

public class MaterialClassificationDetail : Entity<int>, ISoftDeletedEntity
{
    public double Value { get; set; }
	public bool IsDeleted { get; set; }

    public int UnitId { get; set; }
    public DomainValue Unit { get; set; }

    public int MaterialTypeId { get; set; }
    public MaterialType MaterialType { get; set; }
    
    public int MaterialClassificationId { get; set; }
    public MaterialClassification MaterialClassification { get; set; }
}