﻿using FluentValidation;

namespace Classification.Application.Features.Web.Queries.MaterialEntries.GetWeeklyMaterialEntries;

public class GetWeeklyMaterialEntriesValidator : AbstractValidator<GetWeeklyMaterialEntriesRequest>
{
    public GetWeeklyMaterialEntriesValidator()
    {
        RuleFor(x => x.LicensePlate)
            .MinimumLength(5)
            .When(x => x.LicensePlate != null)
            .WithMessage("La placa del vehículo no puede tener menos de 5 caracteres.");
        
        When(x => x.Route != null, () =>
        {
            RuleFor(x => x.Route)
                .MaximumLength(10)
                .WithMessage("La ruta no puede tener más de 10 caracteres.")
                .MinimumLength(1)
                .WithMessage("La ruta no puede tener menos de 1 caracter.");
        });
        
        When(x => x.ClientName != null, () =>
        {
            RuleFor(x => x.ClientName)
                .MaximumLength(100)
                .WithMessage("El nombre del cliente no puede tener más de 50 caracteres.")
                .MinimumLength(1)
                .WithMessage("El nombre del cliente no puede tener menos de 1 caracter.");
        });
    }
}