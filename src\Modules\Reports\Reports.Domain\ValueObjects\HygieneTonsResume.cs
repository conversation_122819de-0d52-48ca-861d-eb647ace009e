﻿using Orion.SharedKernel.Domain.ValueObjects;
using Reports.Domain.Common;

namespace Reports.Domain.ValueObjects;

public class HygieneTonsResume : ValueObject, IResumeBase
{
    public decimal UrbanCleaning { get; set; }
    public decimal Sweeping { get; set; }
    public decimal NonRecyclable { get; set; }
    public decimal Rejection { get; set; }
    public decimal Recyclable { get; set; }
    public decimal Total { get; set; }
    
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return UrbanCleaning;
        yield return Sweeping;
        yield return NonRecyclable;
        yield return Rejection;
        yield return Recyclable;
        yield return Total;
    }
}