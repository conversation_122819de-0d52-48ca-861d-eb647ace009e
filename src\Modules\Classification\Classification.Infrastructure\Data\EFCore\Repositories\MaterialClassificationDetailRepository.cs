﻿using Classification.Domain.Entities;
using Classification.Domain.Repositories;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;

namespace Classification.Infrastructure.Data.EFCore.Repositories;

public class MaterialClassificationDetailRepository
	: Repository<MaterialClassificationDetail, int, ClassificationDbContext>,
	IMaterialClassificationDetailRepository
{
	public MaterialClassificationDetailRepository(IDbContextProvider<ClassificationDbContext> dbContextProvider, 
		ICacheService cacheService) 
		: base(dbContextProvider, cacheService)
	{
	}
}
