﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Classification.Infrastructure.Data.EFCore.Migrations
{
    public partial class Adds_Audit_For_MaterialEntryDetail : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "RECYECA05_CreadoPor",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material",
                type: "character varying(60)",
                maxLength: 60,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "RECYECA05_Eliminado",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "RECYECA05_EliminadoPor",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material",
                type: "character varying(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "RECYECA05_FechaDeCreacion",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "RECYECA05_FechaDeEliminacion",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "RECYECA05_FechaDeModificacion",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RECYECA05_ModificadoPor",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material",
                type: "character varying(60)",
                maxLength: 60,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RECYECA05_CreadoPor",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material");

            migrationBuilder.DropColumn(
                name: "RECYECA05_Eliminado",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material");

            migrationBuilder.DropColumn(
                name: "RECYECA05_EliminadoPor",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material");

            migrationBuilder.DropColumn(
                name: "RECYECA05_FechaDeCreacion",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material");

            migrationBuilder.DropColumn(
                name: "RECYECA05_FechaDeEliminacion",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material");

            migrationBuilder.DropColumn(
                name: "RECYECA05_FechaDeModificacion",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material");

            migrationBuilder.DropColumn(
                name: "RECYECA05_ModificadoPor",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material");
        }
    }
}
