<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>f8956a75-e1d0-49c5-9d4f-30ef346e2626</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.18.1" />
    <PackageReference Include="Scalar.AspNetCore" Version="2.4.9" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Modules\Classification\Classification.Api\Classification.Api.csproj" />
    <ProjectReference Include="..\Modules\Common\Common.Api\Common.Api.csproj" />
    <ProjectReference Include="..\Modules\Reports\Reports.Api\Reports.Api\Reports.Api.csproj" />
  </ItemGroup>

</Project>
