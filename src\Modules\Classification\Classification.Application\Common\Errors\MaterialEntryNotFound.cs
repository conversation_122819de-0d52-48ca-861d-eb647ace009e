﻿using Orion.SharedKernel.Domain.Entities.Error;
using Orion.SharedKernel.Domain.Entities.Loggers;
using System.Net;


namespace Classification.Application.Common.Errors
{
    public class MaterialEntryNotFound : Error
    {
        public MaterialEntryNotFound(int id) {
            Title = "No existe el ingreso de material.";
            Messages = new List<string>()
            {
            $"No existe el ingreso de material con identificador: {id}."
        };
            Status = HttpStatusCode.NotFound;
            Platform = PlatformApp.Api;
            ErrorCode = LevelErrorCode.Error;
        }
    }
}
