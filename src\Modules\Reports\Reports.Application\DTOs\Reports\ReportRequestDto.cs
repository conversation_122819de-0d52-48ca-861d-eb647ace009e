﻿namespace Reports.Application.DTOs.Reports;

public record ReportRequestDto
{
    /// <summary>
    /// <PERSON>cha de entrada a filtrar desde.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD</remarks>
    /// <example>2023-07-14</example>
    public DateTime FromDate { get; init; }

    /// <summary>
    /// Fecha de entrada a filtrar hasta.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD</remarks>
    /// <example>2023-07-14</example>
    public DateTime ToDate { get; init; }

    /// <summary>
    /// Tipo de origen.
    /// </summary>
    /// <remarks>(1) NUAP, (2) NUET, (3) NUECA, (4) Otros</remarks>
    /// <example>NUAP</example>
    /// <example>1</example>
    public string? OriginType { get; init; }
    
    /// <summary>
    /// Numero de identificación tributaria.
    /// </summary>
    /// <example>120400182636</example>
    public long? NIT { get; init; }
    
    /// <summary>
    /// Placa del vehículo.
    /// </summary>
    /// <remarks>Mínimo 5 carácteres alfanuméricos</remarks>
    /// <example>GKV295</example>
    public string? LicensePlate { get; init; }
    
    /// <summary>
    /// Id del pesaje.
    /// </summary>
    /// <example>604918273</example>
    public long? WeighinId { get; init; }

    /// <summary>
    /// Area de aprovechamiento.
    /// </summary>
    /// <example>440805129</example>
    public long? NUAP { get; init; }
    
    /// <summary>
    /// Código de ruta larga.
    /// </summary>
    /// <example>0609415</example>
    public string? RouteCode { get; init; }
    
    /// <summary>
    /// Bandera para verificar si existe en el formato 14.
    /// </summary>
    /// <example>true</example>
    public bool? ExistsInFormat14 { get; init; }

    /// <summary>
    /// Tipo de reporte.
    /// </summary>
    /// <remarks> Format14, Format34 </remarks>
    /// <example>Format14</example>
    public string? ReportType { get; init; }
}