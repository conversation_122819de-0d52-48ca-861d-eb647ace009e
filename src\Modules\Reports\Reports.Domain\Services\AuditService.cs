using System.Reflection;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Domain.Services;

/// <summary>
/// Domain service implementation for audit logging operations.
/// Encapsulates the business logic for creating audit records following DDD principles.
/// </summary>
public class AuditService : IAuditService
{
    public IEnumerable<HistoricalAuditColumns> CreateInsertAuditRecords<TEntity>(
        IEnumerable<TEntity> entities,
        string tableName,
        string user,
        string? metadata = null) where TEntity : class
    {
        return entities.Select(entity =>
        {
            var entityId = ExtractEntityId(entity);
            return HistoricalAuditColumns.CreateForInsert(
                entityId,
                tableName,
                user,
                entity,
                metadata);
        });
    }

    public IEnumerable<HistoricalAuditColumns> CreateUpdateAuditRecords<TEntity>(
        IEnumerable<EntityChange<TEntity>> entityChanges,
        string tableName,
        string user,
        string? metadata = null) where TEntity : class
    {
        return entityChanges.Select(change =>
            HistoricalAuditColumns.CreateForUpdate(
                change.EntityId,
                tableName,
                user,
                change.PreviousState,
                change.CurrentState,
                metadata));
    }

    public IEnumerable<HistoricalAuditColumns> CreateCancellationAuditRecords<TEntity>(
        IEnumerable<TEntity> entities,
        string tableName,
        string user,
        string? metadata = null) where TEntity : class
    {
        return entities.Select(entity =>
        {
            var entityId = ExtractEntityId(entity);
            return HistoricalAuditColumns.CreateForCancellation(
                entityId,
                tableName,
                user,
                entity,
                metadata);
        });
    }

    public IEnumerable<HistoricalAuditColumns> CreateBulkOperationAuditRecords<TEntity>(
        BulkAuditOperation<TEntity> bulkOperation) where TEntity : class
    {
        var auditRecords = new List<HistoricalAuditColumns>();

        // Create audit records for insertions
        if (bulkOperation.InsertedEntities.Any())
        {
            auditRecords.AddRange(CreateInsertAuditRecords(
                bulkOperation.InsertedEntities,
                bulkOperation.TableName,
                bulkOperation.User,
                bulkOperation.Metadata));
        }

        // Create audit records for updates
        if (bulkOperation.UpdatedEntities.Any())
        {
            auditRecords.AddRange(CreateUpdateAuditRecords(
                bulkOperation.UpdatedEntities,
                bulkOperation.TableName,
                bulkOperation.User,
                bulkOperation.Metadata));
        }

        // Create audit records for cancellations
        if (bulkOperation.CancelledEntities.Any())
        {
            auditRecords.AddRange(CreateCancellationAuditRecords(
                bulkOperation.CancelledEntities,
                bulkOperation.TableName,
                bulkOperation.User,
                bulkOperation.Metadata));
        }

        return auditRecords;
    }

    public string ExtractEntityId<TEntity>(TEntity entity) where TEntity : class
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        // Try to find Id property using common conventions
        var entityType = typeof(TEntity);
        
        // Look for properties named "Id" first
        var idProperty = entityType.GetProperty("Id", BindingFlags.Public | BindingFlags.Instance);
        
        if (idProperty != null)
        {
            var idValue = idProperty.GetValue(entity);
            return idValue?.ToString() ?? string.Empty;
        }

        // Look for properties ending with "Id"
        var idProperties = entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance)
            .Where(p => p.Name.EndsWith("Id", StringComparison.OrdinalIgnoreCase))
            .ToList();

        if (idProperties.Count == 1)
        {
            var idValue = idProperties[0].GetValue(entity);
            return idValue?.ToString() ?? string.Empty;
        }

        // Look for properties with specific naming patterns
        var conventionProperties = entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance)
            .Where(p => p.Name.Equals($"{entityType.Name}Id", StringComparison.OrdinalIgnoreCase))
            .ToList();

        if (conventionProperties.Count == 1)
        {
            var idValue = conventionProperties[0].GetValue(entity);
            return idValue?.ToString() ?? string.Empty;
        }

        // If no conventional Id property found, use the hash code as fallback
        // This is not ideal but ensures we always have an identifier
        return entity.GetHashCode().ToString();
    }

    public string GetTableName<TEntity>() where TEntity : class
    {
        var entityType = typeof(TEntity);
        
        // Use a simple convention: pluralize the entity name
        // In a real implementation, you might want to use EF Core metadata
        // or custom attributes to determine the actual table name
        var tableName = entityType.Name;
        
        // Simple pluralization logic
        if (tableName.EndsWith("y", StringComparison.OrdinalIgnoreCase))
        {
            tableName = tableName.Substring(0, tableName.Length - 1) + "ies";
        }
        else if (tableName.EndsWith("s", StringComparison.OrdinalIgnoreCase) ||
                 tableName.EndsWith("sh", StringComparison.OrdinalIgnoreCase) ||
                 tableName.EndsWith("ch", StringComparison.OrdinalIgnoreCase) ||
                 tableName.EndsWith("x", StringComparison.OrdinalIgnoreCase) ||
                 tableName.EndsWith("z", StringComparison.OrdinalIgnoreCase))
        {
            tableName += "es";
        }
        else
        {
            tableName += "s";
        }

        return tableName;
    }
}
