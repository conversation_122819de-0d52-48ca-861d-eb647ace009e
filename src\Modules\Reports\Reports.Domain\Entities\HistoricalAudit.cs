using Orion.SharedKernel.Domain.Entities;
using Reports.Domain.Constants;

namespace Reports.Domain.Entities;

public class HistoricalAudit : Entity<int>
{
    public string EntityId { get; set; } = string.Empty;
    public string TableName { get; set; } = string.Empty;
    public HistoricalMovementType ActionType { get; set; }
    public string User { get; set; } = string.Empty;
    public DateTime ActionDate { get; set; }
    public string? PreviousData { get; set; }
}
