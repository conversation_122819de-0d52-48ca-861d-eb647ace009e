﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Classification.Infrastructure.Data.EFCore.Migrations
{
    public partial class Remove_FK_DDV : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "Recycling-ECA_RECYECA06-OrionDomainValue_fkey",
                table: "Recycling-ECA_06-<PERSON>lson");

            migrationBuilder.DropForeignKey(
                name: "Recycling-ECA_RECYECA08-OrionDomainValue_fkey",
                table: "Recycling-ECA_08-Detalle_Clasificacion_de_Material");

            migrationBuilder.DropTable(
                name: "Orion-DomainValue");

            migrationBuilder.DropTable(
                name: "Orion-Domain");

            migrationBuilder.DropIndex(
                name: "IX_Recycling-ECA_08-Detalle_Clasificacion_de_Material_RECYECA~2",
                table: "Recycling-ECA_08-Detalle_Clasificacion_de_Material");

            migrationBuilder.DropIndex(
                name: "IX_Recycling-ECA_06-<PERSON><PERSON>_RECYECA06_Presentacion",
                table: "Recycling-ECA_06-Bolson");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Orion-Domain",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Code = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "text", nullable: false),
                    ParentDomainId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Domain", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Orion-DomainValue",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    DomainId = table.Column<int>(type: "integer", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Order = table.Column<int>(type: "integer", nullable: true),
                    ParentDomainValueId = table.Column<int>(type: "integer", nullable: true),
                    Value = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DomainValue", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DomainValue_Domain_DomainId",
                        column: x => x.DomainId,
                        principalTable: "Domain",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Recycling-ECA_08-Detalle_Clasificacion_de_Material_RECYECA~2",
                table: "Recycling-ECA_08-Detalle_Clasificacion_de_Material",
                column: "RECYECA08_Id_de_Unidad");

            migrationBuilder.CreateIndex(
                name: "IX_Recycling-ECA_06-Bolson_RECYECA06_Presentacion",
                table: "Recycling-ECA_06-Bolson",
                column: "RECYECA06_Presentacion");

            migrationBuilder.CreateIndex(
                name: "IX_DomainValue_DomainId",
                table: "DomainValue",
                column: "DomainId");

            migrationBuilder.AddForeignKey(
                name: "Recycling-ECA_RECYECA06-OrionDomainValue_fkey",
                table: "Recycling-ECA_06-Bolson",
                column: "RECYECA06_Presentacion",
                principalTable: "DomainValue",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "Recycling-ECA_RECYECA08-OrionDomainValue_fkey",
                table: "Recycling-ECA_08-Detalle_Clasificacion_de_Material",
                column: "RECYECA08_Id_de_Unidad",
                principalTable: "DomainValue",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
