﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Fix_Weighins_LoadingTypeColumn : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "REPEM03_Tipo_de_carga",
                table: "Reporting-Emvarias_03-<PERSON><PERSON><PERSON><PERSON>_de_Balanza",
                type: "character(1)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "char(1)");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "REPEM03_Tipo_de_carga",
                table: "Reporting-Emvarias_03-<PERSON><PERSON><PERSON><PERSON>_de_Balanza",
                type: "char(1)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character(1)");
        }
    }
}
