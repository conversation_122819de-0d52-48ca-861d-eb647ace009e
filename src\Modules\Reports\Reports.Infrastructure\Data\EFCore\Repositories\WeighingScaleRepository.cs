﻿using System.Collections.Immutable;
using System.Linq.Expressions;
using EFCore.BulkExtensions;
using Microsoft.EntityFrameworkCore;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;
using Reports.Domain.Entities;
using Reports.Domain.Repositories;

namespace Reports.Infrastructure.Data.EFCore.Repositories;

public class WeighingScaleRepository : Repository<WeighingScale, long, ReportsDbContext>, IWeighingScaleRepository
{
    public WeighingScaleRepository(IDbContextProvider<ReportsDbContext> dbContextProvider, ICacheService cacheService)
        : base(dbContextProvider, cacheService) { }

    public async Task BulkInsertAsync(List<WeighingScale> weighins, CancellationToken cancellationToken)
    {
        await _context.BulkInsertAsync(weighins,
            new BulkConfig
            {
                PreserveInsertOrder = false,
                SetOutputIdentity = false
            },
            cancellationToken: cancellationToken);
    }

    public async Task BulkUpdateAsync(List<WeighingScale> weighins, CancellationToken cancellationToken)
    {
        await _context.BulkUpdateAsync(weighins,
            new BulkConfig
            {
                PreserveInsertOrder = false,
                SetOutputIdentity = false,
                PropertiesToIncludeOnUpdate = new List<string>
                {
                    nameof(WeighingScale.LicensePlate),
                    nameof(WeighingScale.NIT),
                    nameof(WeighingScale.TownCode),
                    nameof(WeighingScale.NUAP),
                    nameof(WeighingScale.OriginType),
                    nameof(WeighingScale.CancelDate),
                }
            },
            cancellationToken: cancellationToken);
    }
}