﻿using Classification.Domain;
using Common.Domain.Events;

//using Classification.Domain.Entities;
using Orion.SharedKernel.Application.Exceptions;

namespace Common.Application.Features.Web.Commands.MaterialTypes.DeleteMaterialType;

internal sealed class DeleteMaterialTypeHandler : IRequestHandler<DeleteMaterialTypeRequest>
{
	private readonly ICommonUnitOfWork _commonUnitOfWork;
	private readonly IMediator _mediator;

    public DeleteMaterialTypeHandler(ICommonUnitOfWork commonUnitOfWork, IMediator mediator)
    {
        _commonUnitOfWork = commonUnitOfWork;
        _mediator = mediator;
    }

    public async Task<Unit> Handle(DeleteMaterialTypeRequest request, CancellationToken cancellationToken)
	{
		MaterialType materialType = await GetMaterialTypeAsync(request, cancellationToken);

		await CheckIfTheMaterialTypeIsInAnyClassificationDetail(request, cancellationToken);
		
		await ProcessTransactionAsync(materialType, cancellationToken);

		return Unit.Value;
	}

	private async Task<MaterialType> GetMaterialTypeAsync(DeleteMaterialTypeRequest request, CancellationToken cancellationToken)
	{
		MaterialType materialType = await _commonUnitOfWork
			.MaterialTypeRepository
			.GetByIdAsync(request.Id, cancellationToken)
		?? throw new OrionException(new MaterialTypeNotFound(request.Id));

		if (materialType.IsDeleted)
		{
			throw new OrionException(_commonUnitOfWork.ErrorService.GenerateError(new MaterialTypeNotFound(request.Id)));
		}

		return materialType;
	}

	private async Task CheckIfTheMaterialTypeIsInAnyClassificationDetail(DeleteMaterialTypeRequest request, CancellationToken cancellationToken)
	{
		await _mediator.Publish(CheckMaterialExistsDomainEvent.Create(request.Id), cancellationToken);
	}

	private async Task ProcessTransactionAsync(MaterialType materialType, CancellationToken cancellationToken)
	{
		materialType.Delete();

		_commonUnitOfWork
			.MaterialTypeRepository
			.Update(materialType);

		await _commonUnitOfWork.SaveChangesAsync(cancellationToken);
	}
}