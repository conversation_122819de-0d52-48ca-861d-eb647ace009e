using System.Reflection;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Reports.Application.Services;
using Reports.Domain.Services;

namespace Reports.Application;

public static class ServicesExtensions
{
    public static IServiceCollection AddReportsApplication(this IServiceCollection services)
    {
        services.AddMediatR(Assembly.GetExecutingAssembly());

        services.AddAutoMapper(Assembly.GetExecutingAssembly());

        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

        // Register audit services
        services.AddScoped<IAuditService, AuditService>();
        services.AddScoped<IWeighingScaleAuditService, WeighingScaleAuditService>();
        services.AddScoped<IAuditApplicationService, AuditApplicationService>();

        return services;
    }
}