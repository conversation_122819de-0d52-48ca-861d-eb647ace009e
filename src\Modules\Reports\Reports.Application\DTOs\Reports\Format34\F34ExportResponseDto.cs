namespace Reports.Application.DTOs.Reports;

public record F34ExportResponseDto
{
    public long C1_NUSD { get; init; }
    public int C2_TIPO_ORIGEN { get; init; }
    public long? C3_NUSITIO_ORI { get; init; }
    public string? C4_NOMBRE_EMPRESA { get; init; }
    public long? C5_NIT_EMPRESA { get; init; }
    public string? C6_COD_DANE_ORI { get; init; }
    public string C7_PLACA { get; init; }
    public DateOnly C8_FECHA_INGRESO { get; init; }
    public DateOnly C9_FECHA_SALIDA { get; init; }
    public TimeOnly C10_HORA_INGRESO { get; init; }
    public TimeOnly C11_HORA_SALIDA { get; init; }
    public decimal C12_TONELADAS { get; init; }
    public long CE_ID_TICKET { get; init; }
    public decimal CE_VALOR_TICKET_LEGACY { get; init; }
    public decimal CE_VALOR_TON_TICKET_LEGACY { get; init; }
    public decimal CE_TONELADAS_RECHAZADAS { get; init; }
    public bool CE_EXISTE_EN_F14 { get; init; }
    public long CE_NIT_EMPRESA { get; init; }
    public long? CE_NUAP { get; init; }
}