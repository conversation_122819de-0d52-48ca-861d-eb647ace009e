﻿using AutoMapper;
using Common.Application.DTOs.MaterialType;
using Common.Domain.Entities;

namespace Common.Application.Common.MappingProfiles;

public class DomainToDtoMappingProfile : Profile
{
    public DomainToDtoMappingProfile()
    {
        #region MaterialType

        CreateMap<MaterialType, MaterialTypeDto>()
            .ForMember(dest => dest.Id,
                opt => opt.MapFrom(
                    src => src.Id))
            .ForMember(dest => dest.Name,
                opt => opt.MapFrom(
                    src => src.Name))
            .ForMember(dest => dest.Description,
                opt => opt.MapFrom(
                    src => src.Description))
            .ForMember(dest => dest.GroupId,
                opt => opt.MapFrom(
                    src => src.GroupId))
            .ForMember(dest => dest.Group,
                opt => opt.MapFrom(
                    src => src.Group.Value));

        #endregion
    }
}