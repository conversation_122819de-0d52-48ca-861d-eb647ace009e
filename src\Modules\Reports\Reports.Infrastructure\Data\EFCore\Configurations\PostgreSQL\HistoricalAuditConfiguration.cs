using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class HistoricalAuditConfiguration : IEntityTypeConfiguration<HistoricalAudit>
{
    public void Configure(EntityTypeBuilder<HistoricalAudit> builder)
    {
        builder.ToTable(ReportsTableNames.HistoricalAudit);

        builder.HasKey(x => x.Id)
            .HasName(HistoricalAuditColumns.PrimaryKeyConstraintName);

        builder.Property(x => x.Id)
            .HasColumnName(HistoricalAuditColumns.Id)
            .IsRequired();

        builder.Property(x => x.EntityId)
            .HasColumnName(HistoricalAuditColumns.EntityId)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(x => x.TableName)
            .HasColumnName(HistoricalAuditColumns.TableName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(x => x.ActionType)
            .HasColumnName(HistoricalAuditColumns.ActionType)
            .HasConversion<int>()
            .IsRequired();

        builder.Property(x => x.User)
            .HasColumnName(HistoricalAuditColumns.User)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(x => x.ActionDate)
            .HasColumnName(HistoricalAuditColumns.ActionDate)
            .HasColumnType("timestamp without time zone")
            .IsRequired();

        builder.Property(x => x.PreviousData)
            .HasColumnName(HistoricalAuditColumns.PreviousData)
            .HasColumnType("text")
            .IsRequired(false);

        builder.HasIndex(x => x.EntityId)
            .HasDatabaseName("IX_HistoricalAudit_EntityId");

        builder.HasIndex(x => x.TableName)
            .HasDatabaseName("IX_HistoricalAudit_TableName");

        builder.HasIndex(x => x.ActionDate)
            .HasDatabaseName("IX_HistoricalAudit_ActionDate");

        builder.HasIndex(x => x.User)
            .HasDatabaseName("IX_HistoricalAudit_User");
    }
}
