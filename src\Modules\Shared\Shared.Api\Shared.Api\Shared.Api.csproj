﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
    <PackageReference Include="Orion.Core.Api" Version="1.2.0" />
    <PackageReference Include="Orion.Core.PermissionAuthorization" Version="1.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Shared.Application\Shared.Application.csproj" />
    <ProjectReference Include="..\..\Shared.Infrastructure\Shared.Infrastructure.csproj" />
  </ItemGroup>

</Project>