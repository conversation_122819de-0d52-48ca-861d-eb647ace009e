﻿using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;
using Reports.Domain.Entities;
using Reports.Domain.Repositories;

namespace Reports.Infrastructure.Data.EFCore.Repositories;

public class RecyclingAreaRepository : Repository<RecyclingArea, int, ReportsDbContext>, IRecyclingAreaRepository
{
    public RecyclingAreaRepository(IDbContextProvider<ReportsDbContext> dbContextProvider, ICacheService cacheService) : base(dbContextProvider, cacheService)
    {
    }
}