using System.Collections.Immutable;
using Microsoft.EntityFrameworkCore;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;
using Reports.Domain.Entities;
using Reports.Domain.Repositories;

namespace Reports.Infrastructure.Data.EFCore.Repositories;

public class TownRepository : Repository<Town, string, ReportsDbContext>, ITownRepository
{
    private readonly ReportsDbContext _dbContext;

    public TownRepository(IDbContextProvider<ReportsDbContext> dbContextProvider, ICacheService cacheService) : base(
        dbContextProvider, cacheService)
    {
        _dbContext = dbContextProvider.GetDbContext();
    }

    public async Task<bool> AllMatchesByIdAsync(ImmutableHashSet<string> ids, CancellationToken cancellationToken)
    {
        var matchingCount = await _dbContext
            .Towns
            .AsQueryable()
            .CountAsync(town =>
                ids.Contains(town.Code), cancellationToken);
        
        return matchingCount == ids.Count;
    }
}