﻿using Common.Application;
using Common.Domain.Configuration;
using Common.Infrastructure;

namespace Common.Api;

public static class ServicesExtensions
{
    public static IServiceCollection AddCommonModule(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddCommonOptions(configuration);
        
        services.AddCommonInfrastructure(configuration);
        
        services.AddCommonApplication();

        return services;
    }

    public static IServiceCollection AddCommonOptions(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<OrionApiConfiguration>(configuration.GetSection(nameof(OrionApiConfiguration)));
        
        return services;
    }
}