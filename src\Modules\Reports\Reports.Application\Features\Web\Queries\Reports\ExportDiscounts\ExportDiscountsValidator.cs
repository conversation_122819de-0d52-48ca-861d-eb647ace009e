﻿using FluentValidation;

namespace Reports.Application.Features.Web.Queries.Reports.ExportDiscounts;

public class ExportDiscountsValidator : AbstractValidator<ExportDiscountsRequest>
{
    public ExportDiscountsValidator()
    {
        RuleFor(p => p.FromDate)
            .Must(p => p != default)
            .WithMessage("Debe ingresar una fecha de inicio válida.");
        
        RuleFor(p => p.ToDate)
            .Must(p => p != default)
            .WithMessage("Debe ingresar una fecha de fin válida.");
        
        RuleFor(p => p.FromDate)
            .LessThanOrEqualTo(p => p.ToDate)
            .WithMessage("La fecha de inicio no puede ser mayor a la fecha de fin.");
    }
}