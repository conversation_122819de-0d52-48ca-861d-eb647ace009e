﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Classification.Infrastructure.Data.EFCore.Migrations
{
    public partial class Adds_Audit_For_MaterialEntry : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "RECYECA04_CreadoPor",
                table: "Recycling-ECA_04-Ingreso_de_Material",
                type: "character varying(60)",
                maxLength: 60,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "RECYECA04_Eliminado",
                table: "Recycling-ECA_04-Ingreso_de_Material",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "RECYECA04_EliminadoPor",
                table: "Recycling-ECA_04-Ingreso_de_Material",
                type: "character varying(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "RECYECA04_FechaDeCreacion",
                table: "Recycling-ECA_04-Ingreso_de_Material",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "RECYECA04_FechaDeEliminacion",
                table: "Recycling-ECA_04-Ingreso_de_Material",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "RECYECA04_FechaDeModificacion",
                table: "Recycling-ECA_04-Ingreso_de_Material",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RECYECA04_ModificadoPor",
                table: "Recycling-ECA_04-Ingreso_de_Material",
                type: "character varying(60)",
                maxLength: 60,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RECYECA04_CreadoPor",
                table: "Recycling-ECA_04-Ingreso_de_Material");

            migrationBuilder.DropColumn(
                name: "RECYECA04_Eliminado",
                table: "Recycling-ECA_04-Ingreso_de_Material");

            migrationBuilder.DropColumn(
                name: "RECYECA04_EliminadoPor",
                table: "Recycling-ECA_04-Ingreso_de_Material");

            migrationBuilder.DropColumn(
                name: "RECYECA04_FechaDeCreacion",
                table: "Recycling-ECA_04-Ingreso_de_Material");

            migrationBuilder.DropColumn(
                name: "RECYECA04_FechaDeEliminacion",
                table: "Recycling-ECA_04-Ingreso_de_Material");

            migrationBuilder.DropColumn(
                name: "RECYECA04_FechaDeModificacion",
                table: "Recycling-ECA_04-Ingreso_de_Material");

            migrationBuilder.DropColumn(
                name: "RECYECA04_ModificadoPor",
                table: "Recycling-ECA_04-Ingreso_de_Material");
        }
    }
}
