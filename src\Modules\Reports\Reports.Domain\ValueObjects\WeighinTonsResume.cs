﻿using Orion.SharedKernel.Domain.ValueObjects;
using Reports.Domain.Common;

namespace Reports.Domain.ValueObjects;

public class WeighinTonsResume : ValueObject, IResumeBase
{
    public decimal Emvarias { get; set; }
    public decimal Total { get; set; }
    public decimal Discount { get; set; }
    
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Emvarias;
        yield return Total;
        yield return Discount;
    }
}