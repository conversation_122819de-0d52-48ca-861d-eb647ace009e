﻿using System.Net;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Orion.SharedKernel.Api.Controllers;
using Orion.SharedKernel.Domain.Entities.Error;
using Reports.Application.DTOs.Clients;
using Reports.Application.Features.Web.Queries.Clients.GetAllClients;

namespace Reports.Api.Controllers.v1.Web;

[ApiVersion("1.0")]
[Authorize]
public class ClientsController : OrionController
{
    /// <summary>
    /// Obtiene la lista completa de clientes
    /// </summary>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(List<ClientsResponseDto>))]
    [ProducesResponseType((int)HttpStatusCode.Conflict, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var request = new GetAllClientsRequest();

        var response = await Mediator.Send(request);

        return Ok(response.Clients);
    }
}