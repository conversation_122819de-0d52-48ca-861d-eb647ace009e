﻿using System.Globalization;
using AutoMapper;
using Classification.Application.Common.Extensions;
using Classification.Application.DTOs.MaterialEntry;
using Classification.Application.Features.Web.Commands.MaterialEntries.CreateMaterialEntry;
using Classification.Domain.Constants;
using Classification.Domain.Entities;

namespace Classification.Application.Common.MappingProfiles;

public class RequestToDomainMappingProfile : Profile
{
    public RequestToDomainMappingProfile()
    {
        #region MaterialEntry

        CreateMap<SackRequestDto, Sack>();
        
        CreateMap<MaterialEntryDetailRequestDto, MaterialEntryDetail>();

        CreateMap<MaterialEntryRequestDto, MaterialEntry>()
            .ForMember(dest => dest.Date,
                opt =>
                    opt.MapFrom(src => src.EntryDate!.ToStandardDateTime()))
            .ForMember(p => p.Origin,
                opt => opt.MapFrom(
                    src =>
                        Enum.Parse<OriginType>(src.Origin!)));

        #endregion
    }
}