﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Fix_Calculo_Barrido_F14 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14""
                            (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                             ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                             ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                             ""CE_REL_COMPENSACION_ID_TICKET"", ""CE_AJUSTE_DECIMAL"")
                as
                WITH cte AS (SELECT rpm.""REPEM04_Id""                                     AS ""Id_Ticket"",
                                    mic.""REPEM07_NUAP""                                   AS ""NUAP"",
                                    CASE
                                        WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                                 COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                        WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                            THEN tr.""Toneladas_Descuento_Medellin""
                                        ELSE tdha.""Toneladas_Resultantes""
                                        END                                              AS ""Calculo_Toneladas"",
                                    CASE
                                        WHEN mic.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                        ELSE (
                                            CASE
                                                WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas"" -
                                                                                         COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                    THEN tr.""Toneladas_Descuento_Medellin""
                                                ELSE tdha.""Toneladas_Resultantes""
                                                END * (mic.""REPEM07_Porcentaje_Barrido""::numeric / 100::numeric))::numeric(18, 3)
                                        END::numeric(18, 3)                              AS ""Calculo_Barrido"",
                                    CASE
                                        WHEN pe.* IS NULL THEN 0::numeric
                                        WHEN mic.""REPEM07_NUAP"" = 440405001 THEN 0::numeric
                                        WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                            THEN 0::numeric
                                        ELSE (pe.""REPEM06_Valor"" * 2::numeric::double precision *
                                              (COALESCE(dpm.""Porcentaje_Distribucion_Peaje"", 100::numeric) /
                                               100::numeric)::double precision)::numeric
                                        END                                              AS ""Calculo_Peaje"",
                                    COALESCE(tdha.""Tipo_Compensacion"", 'ORIGINAL'::text) AS ""Tipo_Compensacion""
                             FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                      JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                           ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                      LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                            dpm.""Año""::numeric =
                                                                                            EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                            dpm.""Mes""::numeric =
                                                                                            EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                      LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe
                                                ON pe.""REPEM06_Placa""::text = rpm.""REPEM04_Patente""::text AND
                                                   EXTRACT(year FROM pe.""REPEM06_Fecha_Validez"") =
                                                   EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                      LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                                      LEFT JOIN ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                                ON tdha.""Id_Ticket"" = rpm.""REPEM04_Id"" AND tdha.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                   tdha.""Tipo_Compensacion"" = 'ORIGINAL'::text),
                     aggregated_compensations AS (SELECT 0                                                                           AS ""Id_Ticket"",
                                                         ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"",
                                                         sum(""REPEM_Tickets_Compensables"".""Suma_Agrupacion_Toneladas_Por_Compensar"") AS ""Suma_Agrupacion_Toneladas_Por_Compensar""
                                                  FROM ""REPEM_Tickets_Compensables""
                                                  GROUP BY ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"")
                SELECT mic.""REPEM07_NUAP""                                         AS ""C1_NUAP"",
                       1                                                          AS ""C2_TIPO_SITIO"",
                       720105237                                                  AS ""C3_NUSD"",
                       rpm.""REPEM04_Patente""                                      AS ""C4_PLACA"",
                       rpm.""REPEM04_FechaHora_Pesaje""::date                       AS ""C5_FECHA"",
                       rpm.""REPEM04_FechaHora_Pesaje""::time without time zone     AS ""C6_HORA"",
                       mic.""REPEM07_Numero_Ruta_Intendencia""                      AS ""C7_NUMICRO"",
                       0                                                          AS ""C8_TON_LIMP_URB"",
                       round(bs.""Calculo_Barrido"" + COALESCE(tde.""Toneladas_Descuento"", 0::numeric), 3) -
                       COALESCE(dbi.""Valor_A_Descontar"", 0::numeric)              AS ""C9_TON_BARRIDO"",
                       CASE
                           WHEN mic.""REPEM07_Numero_de_Microruta"" = 614001 THEN round(
                                   bs.""Calculo_Toneladas"" - COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                           ELSE round(bs.""Calculo_Toneladas"" - bs.""Calculo_Barrido"" - COALESCE(tde.""Toneladas_Descuento"", 0::numeric) -
                                      COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                           END + COALESCE(compred.""Toneladas_Ajuste"", 0::numeric) AS ""C10_TONRESNA"",
                       COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)           AS ""C11_TONRECHAPR"",
                       CASE
                           WHEN mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN bs.""Calculo_Toneladas"" *
                                                                                                            (mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric /
                                                                                                             100::numeric)
                           ELSE 0::numeric
                           END                                                    AS ""C12_TONRESAPR"",
                       '1'::text                                                  AS ""C13_SISTEMA_MEDICION"",
                       bs.""Calculo_Peaje""                                         AS ""C14_VLRPEAJ"",
                       rpm.""REPEM04_Id""                                           AS ""CE_ID_TICKET"",
                       ap.""REPEM02_Nombre""                                        AS ""CE_NOMBRE_AREA"",
                       mic.""REPEM07_Ruta_Larga""                                   AS ""CE_RUTA_LARGA"",
                       rpm.""REPEM04_PesoTotal_Toneladas""                          AS ""CE_TON_TOTAL"",
                       rtc.* IS NOT NULL                                          AS ""CE_REL_COMPENSACION"",
                       rtc.""Id_Ticket""                                            AS ""CE_REL_COMPENSACION_ID_TICKET"",
                       compred.* IS NOT NULL                                      AS ""CE_AJUSTE_DECIMAL""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                         JOIN cte bs ON bs.""Calculo_Toneladas"" IS NOT NULL AND rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                              ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                 mic.""REPEM07_NUAP"" = bs.""NUAP""
                         JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                         LEFT JOIN ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" tde ON rpm.""REPEM04_Id"" = tde.""NroTicket"" AND
                                                                                          EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                          tde.""Año"" AND
                                                                                          EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                          tde.""Mes"" AND
                                                                                          mic.""REPEM07_Numero_de_Microruta"" =
                                                                                          tde.""RutaCodigo"" AND
                                                                                          mic.""REPEM07_NUAP"" = tde.""NUAP""
                         LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                   ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mic.""REPEM07_NUAP"" = 440405001
                         LEFT JOIN aggregated_compensations rtc ON rpm.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                         LEFT JOIN ""REPEM_Descuento_de_Barrido_Itagui_Por_Ticket"" dbi ON dbi.""Id_Ticket_A_Descontar"" = rpm.""REPEM04_Id""
                         LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" compred
                                   ON compred.""Nro_Ticket_Ajustable"" = rpm.""REPEM04_Id"" AND compred.""NUAP"" = mic.""REPEM07_NUAP""
                UNION ALL
                SELECT ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C1_NUAP"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C2_TIPO_SITIO"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C3_NUSD"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C4_PLACA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C5_FECHA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C6_HORA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C7_NUMICRO"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C8_TON_LIMP_URB"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C9_TON_BARRIDO"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C10_TONRESNA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C11_TONRECHAPR"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C12_TONRESAPR"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C13_SISTEMA_MEDICION"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C14_VLRPEAJ"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_ID_TICKET"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_NOMBRE_AREA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_RUTA_LARGA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_TON_TOTAL"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_REL_COMPENSACION"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_REL_COMPENSACION_ID_TICKET"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_AJUSTE_DECIMAL""
                FROM ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"";
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14""
                            (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                             ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                             ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                             ""CE_REL_COMPENSACION_ID_TICKET"", ""CE_AJUSTE_DECIMAL"")
                as
                WITH cte AS (SELECT rpm.""REPEM04_Id""                                     AS ""Id_Ticket"",
                                    mic.""REPEM07_NUAP""                                   AS ""NUAP"",
                                    CASE
                                        WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                                 COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                        WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                            THEN tr.""Toneladas_Descuento_Medellin""
                                        ELSE tdha.""Toneladas_Resultantes""
                                        END                                              AS ""Calculo_Toneladas"",
                                    CASE
                                        WHEN mic.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                        ELSE (
                                            CASE
                                                WHEN mic.""REPEM07_NUAP"" = 440405001 THEN 0::numeric
                                                WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0 THEN 0::numeric
                                                ELSE tdha.""Toneladas_Resultantes""
                                                END * (mic.""REPEM07_Porcentaje_Barrido""::numeric / 100::numeric))::numeric(18, 3)
                                        END::numeric(18, 3)                              AS ""Calculo_Barrido"",
                                    CASE
                                        WHEN pe.* IS NULL THEN 0::numeric
                                        WHEN mic.""REPEM07_NUAP"" = 440405001 THEN 0::numeric
                                        WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                            THEN 0::numeric
                                        ELSE (pe.""REPEM06_Valor"" * 2::numeric::double precision *
                                              (COALESCE(dpm.""Porcentaje_Distribucion_Peaje"", 100::numeric) /
                                               100::numeric)::double precision)::numeric
                                        END                                              AS ""Calculo_Peaje"",
                                    COALESCE(tdha.""Tipo_Compensacion"", 'ORIGINAL'::text) AS ""Tipo_Compensacion""
                             FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                      JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                           ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                      LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                            dpm.""Año""::numeric =
                                                                                            EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                            dpm.""Mes""::numeric =
                                                                                            EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                      LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe
                                                ON pe.""REPEM06_Placa""::text = rpm.""REPEM04_Patente""::text AND
                                                   EXTRACT(year FROM pe.""REPEM06_Fecha_Validez"") =
                                                   EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                      LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                                      LEFT JOIN ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                                ON tdha.""Id_Ticket"" = rpm.""REPEM04_Id"" AND tdha.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                   tdha.""Tipo_Compensacion"" = 'ORIGINAL'::text),
                     aggregated_compensations AS (SELECT 0                                                                           AS ""Id_Ticket"",
                                                         ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"",
                                                         sum(""REPEM_Tickets_Compensables"".""Suma_Agrupacion_Toneladas_Por_Compensar"") AS ""Suma_Agrupacion_Toneladas_Por_Compensar""
                                                  FROM ""REPEM_Tickets_Compensables""
                                                  GROUP BY ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"")
                SELECT mic.""REPEM07_NUAP""                                         AS ""C1_NUAP"",
                       1                                                          AS ""C2_TIPO_SITIO"",
                       720105237                                                  AS ""C3_NUSD"",
                       rpm.""REPEM04_Patente""                                      AS ""C4_PLACA"",
                       rpm.""REPEM04_FechaHora_Pesaje""::date                       AS ""C5_FECHA"",
                       rpm.""REPEM04_FechaHora_Pesaje""::time without time zone     AS ""C6_HORA"",
                       mic.""REPEM07_Numero_Ruta_Intendencia""                      AS ""C7_NUMICRO"",
                       0                                                          AS ""C8_TON_LIMP_URB"",
                       round(bs.""Calculo_Barrido"" + COALESCE(tde.""Toneladas_Descuento"", 0::numeric), 3) -
                       COALESCE(dbi.""Valor_A_Descontar"", 0::numeric)              AS ""C9_TON_BARRIDO"",
                       CASE
                           WHEN mic.""REPEM07_Numero_de_Microruta"" = 614001 THEN round(
                                   bs.""Calculo_Toneladas"" - COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                           ELSE round(bs.""Calculo_Toneladas"" - bs.""Calculo_Barrido"" - COALESCE(tde.""Toneladas_Descuento"", 0::numeric) -
                                      COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                           END + COALESCE(compred.""Toneladas_Ajuste"", 0::numeric) AS ""C10_TONRESNA"",
                       COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)           AS ""C11_TONRECHAPR"",
                       CASE
                           WHEN mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN bs.""Calculo_Toneladas"" *
                                                                                                            (mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric /
                                                                                                             100::numeric)
                           ELSE 0::numeric
                           END                                                    AS ""C12_TONRESAPR"",
                       '1'::text                                                  AS ""C13_SISTEMA_MEDICION"",
                       bs.""Calculo_Peaje""                                         AS ""C14_VLRPEAJ"",
                       rpm.""REPEM04_Id""                                           AS ""CE_ID_TICKET"",
                       ap.""REPEM02_Nombre""                                        AS ""CE_NOMBRE_AREA"",
                       mic.""REPEM07_Ruta_Larga""                                   AS ""CE_RUTA_LARGA"",
                       rpm.""REPEM04_PesoTotal_Toneladas""                          AS ""CE_TON_TOTAL"",
                       rtc.* IS NOT NULL                                          AS ""CE_REL_COMPENSACION"",
                       rtc.""Id_Ticket""                                            AS ""CE_REL_COMPENSACION_ID_TICKET"",
                       compred.* IS NOT NULL                                      AS ""CE_AJUSTE_DECIMAL""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                         JOIN cte bs ON bs.""Calculo_Toneladas"" IS NOT NULL AND rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                              ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                 mic.""REPEM07_NUAP"" = bs.""NUAP""
                         JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                         LEFT JOIN ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" tde ON rpm.""REPEM04_Id"" = tde.""NroTicket"" AND
                                                                                          EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                          tde.""Año"" AND
                                                                                          EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                          tde.""Mes"" AND
                                                                                          mic.""REPEM07_Numero_de_Microruta"" =
                                                                                          tde.""RutaCodigo"" AND
                                                                                          mic.""REPEM07_NUAP"" = tde.""NUAP""
                         LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                   ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mic.""REPEM07_NUAP"" = 440405001
                         LEFT JOIN aggregated_compensations rtc ON rpm.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                         LEFT JOIN ""REPEM_Descuento_de_Barrido_Itagui_Por_Ticket"" dbi ON dbi.""Id_Ticket_A_Descontar"" = rpm.""REPEM04_Id""
                         LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" compred
                                   ON compred.""Nro_Ticket_Ajustable"" = rpm.""REPEM04_Id"" AND compred.""NUAP"" = mic.""REPEM07_NUAP""
                UNION ALL
                SELECT ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C1_NUAP"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C2_TIPO_SITIO"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C3_NUSD"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C4_PLACA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C5_FECHA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C6_HORA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C7_NUMICRO"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C8_TON_LIMP_URB"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C9_TON_BARRIDO"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C10_TONRESNA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C11_TONRECHAPR"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C12_TONRESAPR"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C13_SISTEMA_MEDICION"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C14_VLRPEAJ"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_ID_TICKET"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_NOMBRE_AREA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_RUTA_LARGA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_TON_TOTAL"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_REL_COMPENSACION"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_REL_COMPENSACION_ID_TICKET"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_AJUSTE_DECIMAL""
                FROM ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"";


            ");
        }
    }
}
