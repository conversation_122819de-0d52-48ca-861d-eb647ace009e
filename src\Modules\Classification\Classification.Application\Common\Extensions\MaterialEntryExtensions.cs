﻿using Classification.Domain.Entities;

namespace Classification.Application.Common.Extensions;

public static class MaterialEntryExtensions
{
    public static IEnumerable<MaterialEntry> FilterByClientName(this List<MaterialEntry> materialEntries, string? clientName)
    {
        if (string.IsNullOrEmpty(clientName)) return materialEntries;

        return materialEntries.Where(me => me.Details.Any(d => d.Client.Contains(clientName)));

    }
}