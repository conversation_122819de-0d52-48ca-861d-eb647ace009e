﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class New_RSUI_Recollection_Table : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_04-Recoleccion_por_Microruta",
                columns: table => new
                {
                    REPEM04_Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    REPEM04_NUAP = table.Column<long>(type: "bigint", nullable: false),
                    REPEM04_Patente = table.Column<string>(type: "character varying(6)", maxLength: 6, nullable: false),
                    REPEM04_EstadoServicio = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    REPEM04_GrupoTurno = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    REPEM04_TipoServicio = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    REPEM04_RutaCodigo = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    REPEM04_EsRefuerzo = table.Column<bool>(type: "boolean", nullable: false),
                    REPEM04_Interno = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    REPEM04_PesoTotal = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    REPEM04_IdServicio = table.Column<long>(type: "bigint", nullable: false),
                    REPEM04_FechaHora_EntradaRuta = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    REPEM04_FechaHora_SalidaRuta = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    REPEM04_Observaciones = table.Column<string>(type: "text", nullable: true),
                    REPEM04_PesoTotal_Toneladas = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    REPEM04_FechaHora_Pesaje = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    REPEM04_Fecha_de_Servicio = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    REPEM04_FechaHora_InicioServicio = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    REPEM04_FechaHora_LlegadaBase = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    REPEM04_FechaHora_SalidaBase = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Reporting-Emvarias_04-Recoleccion_por_Microruta_key", x => x.REPEM04_Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_04-Recoleccion_por_Microruta");
        }
    }
}
