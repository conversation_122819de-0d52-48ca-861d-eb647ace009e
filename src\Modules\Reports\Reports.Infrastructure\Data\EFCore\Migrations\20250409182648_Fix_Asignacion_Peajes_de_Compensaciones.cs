﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Fix_Asignacion_Peajes_de_Compensaciones : Migration
    {
        private void DropDependantViews(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"drop view if exists ""REPEM_Reporte_SUI_Recolecciones_F34""");
            migrationBuilder.Sql(@"drop view if exists ""REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos""");
            migrationBuilder.Sql(@"drop view if exists ""REPEM_Reporte_SUI_Recolecciones_F14""");
            migrationBuilder.Sql(@"drop view if exists ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones""");
        }

        private void CreateDependantViewsForUpdate(MigrationBuilder migrationBuilder)
        {
            //F14 
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14""
                (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                 ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                 ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                 ""CE_REL_COMPENSACION_ID_TICKET"", ""CE_AJUSTE_DECIMAL"")
                as
                WITH cte AS (SELECT rpm.""REPEM04_Id""                                     AS ""Id_Ticket"",
                                    mic.""REPEM07_NUAP""                                   AS ""NUAP"",
                                    CASE
                                        WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                                 COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                        WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                            THEN tr.""Toneladas_Descuento_Medellin""
                                        ELSE tdha.""Toneladas_Resultantes""
                                        END                                              AS ""Calculo_Toneladas"",
                                    CASE
                                        WHEN mic.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                        ELSE (
                                            CASE
                                                WHEN mic.""REPEM07_NUAP"" = 440405001 THEN 0::numeric
                                                WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0 THEN 0::numeric
                                                ELSE tdha.""Toneladas_Resultantes""
                                                END * (mic.""REPEM07_Porcentaje_Barrido""::numeric / 100::numeric))::numeric(18, 3)
                                        END::numeric(18, 3)                              AS ""Calculo_Barrido"",
                                    CASE
                                        WHEN pe.* IS NULL THEN 0::numeric
                                        WHEN mic.""REPEM07_NUAP"" = 440405001 THEN 0::numeric
                                        WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                            THEN 0::numeric
                                        ELSE (pe.""REPEM06_Valor"" * 2::numeric::double precision *
                                              (COALESCE(dpm.""Porcentaje_Distribucion_Peaje"", 100::numeric) /
                                               100::numeric)::double precision)::numeric
                                        END                                              AS ""Calculo_Peaje"",
                                    COALESCE(tdha.""Tipo_Compensacion"", 'ORIGINAL'::text) AS ""Tipo_Compensacion""
                             FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                      JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                           ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                      LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                            dpm.""Año""::numeric =
                                                                                            EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                            dpm.""Mes""::numeric =
                                                                                            EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                      LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe
                                                ON pe.""REPEM06_Placa""::text = rpm.""REPEM04_Patente""::text AND
                                                   EXTRACT(year FROM pe.""REPEM06_Fecha_Validez"") =
                                                   EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                      LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                                      LEFT JOIN ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                                ON tdha.""Id_Ticket"" = rpm.""REPEM04_Id"" AND tdha.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                   tdha.""Tipo_Compensacion"" = 'ORIGINAL'::text),
                     aggregated_compensations AS (SELECT 0                                                                           AS ""Id_Ticket"",
                                                         ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"",
                                                         sum(""REPEM_Tickets_Compensables"".""Suma_Agrupacion_Toneladas_Por_Compensar"") AS ""Suma_Agrupacion_Toneladas_Por_Compensar""
                                                  FROM ""REPEM_Tickets_Compensables""
                                                  GROUP BY ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"")
                SELECT mic.""REPEM07_NUAP""                                         AS ""C1_NUAP"",
                       1                                                          AS ""C2_TIPO_SITIO"",
                       720105237                                                  AS ""C3_NUSD"",
                       rpm.""REPEM04_Patente""                                      AS ""C4_PLACA"",
                       rpm.""REPEM04_FechaHora_Pesaje""::date                       AS ""C5_FECHA"",
                       rpm.""REPEM04_FechaHora_Pesaje""::time without time zone     AS ""C6_HORA"",
                       mic.""REPEM07_Numero_Ruta_Intendencia""                      AS ""C7_NUMICRO"",
                       0                                                          AS ""C8_TON_LIMP_URB"",
                       round(bs.""Calculo_Barrido"" + COALESCE(tde.""Toneladas_Descuento"", 0::numeric), 3) -
                       COALESCE(dbi.""Valor_A_Descontar"", 0::numeric)              AS ""C9_TON_BARRIDO"",
                       CASE
                           WHEN mic.""REPEM07_Numero_de_Microruta"" = 614001 THEN round(
                                   bs.""Calculo_Toneladas"" - COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                           ELSE round(bs.""Calculo_Toneladas"" - bs.""Calculo_Barrido"" - COALESCE(tde.""Toneladas_Descuento"", 0::numeric) -
                                      COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                           END + COALESCE(compred.""Toneladas_Ajuste"", 0::numeric) AS ""C10_TONRESNA"",
                       COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)           AS ""C11_TONRECHAPR"",
                       CASE
                           WHEN mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN bs.""Calculo_Toneladas"" *
                                                                                                            (mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric /
                                                                                                             100::numeric)
                           ELSE 0::numeric
                           END                                                    AS ""C12_TONRESAPR"",
                       '1'::text                                                  AS ""C13_SISTEMA_MEDICION"",
                       bs.""Calculo_Peaje""                                         AS ""C14_VLRPEAJ"",
                       rpm.""REPEM04_Id""                                           AS ""CE_ID_TICKET"",
                       ap.""REPEM02_Nombre""                                        AS ""CE_NOMBRE_AREA"",
                       mic.""REPEM07_Ruta_Larga""                                   AS ""CE_RUTA_LARGA"",
                       rpm.""REPEM04_PesoTotal_Toneladas""                          AS ""CE_TON_TOTAL"",
                       rtc.* IS NOT NULL                                          AS ""CE_REL_COMPENSACION"",
                       rtc.""Id_Ticket""                                            AS ""CE_REL_COMPENSACION_ID_TICKET"",
                       compred.* IS NOT NULL                                      AS ""CE_AJUSTE_DECIMAL""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                         JOIN cte bs ON bs.""Calculo_Toneladas"" IS NOT NULL AND rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                              ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                 mic.""REPEM07_NUAP"" = bs.""NUAP""
                         JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                         LEFT JOIN ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" tde ON rpm.""REPEM04_Id"" = tde.""NroTicket"" AND
                                                                                          EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                          tde.""Año"" AND
                                                                                          EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                          tde.""Mes"" AND
                                                                                          mic.""REPEM07_Numero_de_Microruta"" =
                                                                                          tde.""RutaCodigo"" AND
                                                                                          mic.""REPEM07_NUAP"" = tde.""NUAP""
                         LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                   ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mic.""REPEM07_NUAP"" = 440405001
                         LEFT JOIN aggregated_compensations rtc ON rpm.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                         LEFT JOIN ""REPEM_Descuento_de_Barrido_Itagui_Por_Ticket"" dbi ON dbi.""Id_Ticket_A_Descontar"" = rpm.""REPEM04_Id""
                         LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" compred
                                   ON compred.""Nro_Ticket_Ajustable"" = rpm.""REPEM04_Id"" AND compred.""NUAP"" = mic.""REPEM07_NUAP""
                UNION ALL
                SELECT ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C1_NUAP"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C2_TIPO_SITIO"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C3_NUSD"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C4_PLACA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C5_FECHA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C6_HORA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C7_NUMICRO"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C8_TON_LIMP_URB"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C9_TON_BARRIDO"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C10_TONRESNA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C11_TONRECHAPR"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C12_TONRESAPR"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C13_SISTEMA_MEDICION"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C14_VLRPEAJ"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_ID_TICKET"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_NOMBRE_AREA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_RUTA_LARGA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_TON_TOTAL"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_REL_COMPENSACION"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_REL_COMPENSACION_ID_TICKET"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_AJUSTE_DECIMAL""
                FROM ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"";                        
            ");
            
            //F14 Aditivos
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos""
                            (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                             ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                             ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                             ""CE_REL_COMPENSACION_ID_TICKET"")
                as
                SELECT f14.""C1_NUAP"",
                       f14.""C2_TIPO_SITIO"",
                       f14.""C3_NUSD""::character varying(10) AS ""C3_NUSD"",
                       f14.""C4_PLACA"",
                       f14.""C5_FECHA"",
                       f14.""C6_HORA"",
                       f14.""C7_NUMICRO"",
                       f14.""C8_TON_LIMP_URB""::numeric       AS ""C8_TON_LIMP_URB"",
                       f14.""C9_TON_BARRIDO"",
                       f14.""C10_TONRESNA"",
                       f14.""C11_TONRECHAPR"",
                       f14.""C12_TONRESAPR"",
                       f14.""C13_SISTEMA_MEDICION""::integer  AS ""C13_SISTEMA_MEDICION"",
                       f14.""C14_VLRPEAJ"",
                       f14.""CE_ID_TICKET"",
                       f14.""CE_NOMBRE_AREA"",
                       f14.""CE_RUTA_LARGA"",
                       f14.""CE_TON_TOTAL""::numeric          AS ""CE_TON_TOTAL"",
                       f14.""CE_REL_COMPENSACION"",
                       f14.""CE_REL_COMPENSACION_ID_TICKET""
                FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" f14
                UNION ALL
                SELECT clu.""C1_NUAP"",
                       clu.""C2_TIPO_SITIO"",
                       clu.""C3_NUSD""::character varying(10) AS ""C3_NUSD"",
                       clu.""C4_PLACA"",
                       clu.""C5_FECHA"",
                       clu.""C6_HORA"",
                       clu.""C7_NUMICRO"",
                       clu.""C8_TON_LIMP_URB""::numeric       AS ""C8_TON_LIMP_URB"",
                       clu.""C9_TON_BARRIDO""::numeric        AS ""C9_TON_BARRIDO"",
                       clu.""C10_TONRESNA""::numeric          AS ""C10_TONRESNA"",
                       clu.""C11_TONRECHAPR""::numeric        AS ""C11_TONRECHAPR"",
                       clu.""C12_TONRESAPR""::numeric         AS ""C12_TONRESAPR"",
                       clu.""C13_SISTEMA_MEDICION"",
                       clu.""C14_VLRPEAJ""::numeric           AS ""C14_VLRPEAJ"",
                       NULL::bigint                         AS ""CE_ID_TICKET"",
                       'CLUS - CARGADO'::character varying  AS ""CE_NOMBRE_AREA"",
                       clu.""CE_RUTA_LARGA"",
                       clu.""CE_TON_TOTAL""::numeric          AS ""CE_TON_TOTAL"",
                       NULL::boolean                        AS ""CE_REL_COMPENSACION"",
                       NULL::bigint                         AS ""CE_REL_COMPENSACION_ID_TICKET""
                FROM ""Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU"" clu
                UNION ALL
                SELECT apr.""C1_NUAP"",
                       apr.""C2_TIPO_SITIO"",
                       apr.""C3_NUSD""::character varying(10)           AS ""C3_NUSD"",
                       apr.""C4_PLACA"",
                       apr.""C5_FECHA"",
                       apr.""C6_HORA"",
                       apr.""C7_NUMICRO"",
                       apr.""C8_TON_LIMP_URB""::numeric                 AS ""C8_TON_LIMP_URB"",
                       apr.""C9_TON_BARRIDO""::numeric                  AS ""C9_TON_BARRIDO"",
                       apr.""C10_TONRESNA""::numeric                    AS ""C10_TONRESNA"",
                       apr.""C11_TONRECHAPR""::numeric                  AS ""C11_TONRECHAPR"",
                       apr.""C12_TONRESAPR""::numeric                   AS ""C12_TONRESAPR"",
                       apr.""C13_SISTEMA_MEDICION"",
                       apr.""C14_VLRPEAJ""::numeric                     AS ""C14_VLRPEAJ"",
                       NULL::bigint                                   AS ""CE_ID_TICKET"",
                       'APROVECHAMIENTO - CARGADO'::character varying AS ""CE_NOMBRE_AREA"",
                       apr.""CE_RUTA_LARGA"",
                       apr.""CE_TON_TOTAL""::numeric                    AS ""CE_TON_TOTAL"",
                       NULL::boolean                                  AS ""CE_REL_COMPENSACION"",
                       NULL::bigint                                   AS ""CE_REL_COMPENSACION_ID_TICKET""
                FROM ""Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento"" apr;
            ");
            
            //F34 
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F34""
                (""C1_NUSD"", ""C2_TIPO_ORIGEN"", ""C3_NUSITIO_ORI"", ""C4_NOMBRE_EMPRESA"", ""C5_NIT_EMPRESA"", ""C6_COD_DANE_ORI"",
                 ""C7_PLACA"", ""C8_FECHA_INGRESO"", ""C9_FECHA_SALIDA"", ""C10_HORA_INGRESO"", ""C11_HORA_SALIDA"", ""C12_TONELADAS"",
                 ""CE_ID_TICKET"", ""CE_VALOR_TICKET_LEGACY"", ""CE_VALOR_TON_TICKET_LEGACY"", ""CE_TONELADAS_RECHAZADAS"",
                 ""CE_EXISTE_EN_F14"", ""CE_NIT_EMPRESA"", ""CE_NUAP"", ""CE_FECHA_FILTRO"")
                as
                WITH combined_data AS (SELECT 720105237                                                                              AS ""C1_NUSD"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                                                  ELSE NULL::bigint
                                                  END                                                                                AS ""C2_TIPO_ORIGEN"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                                                  ELSE r14.""C1_NUAP""
                                                  END                                                                                AS ""C3_NUSITIO_ORI"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE cli.""REPEM08_Nombre_Completo""
                                                  END                                                                                AS ""C4_NOMBRE_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                                  ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                                  END                                                                                AS ""C5_NIT_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE mun.""REPEM01_Codigo""
                                                  END                                                                                AS ""C6_COD_DANE_ORI"",
                                              pb.""REPEM03_Patente""                                                                   AS ""C7_PLACA"",
                                              pb.""REPEM03_Fecha_de_entrada""::date                                                    AS ""C8_FECHA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::date                                                     AS ""C9_FECHA_SALIDA"",
                                              pb.""REPEM03_Fecha_de_entrada""::time without time zone                                  AS ""C10_HORA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::time without time zone                                   AS ""C11_HORA_SALIDA"",
                                              r14.""C9_TON_BARRIDO"" + r14.""C10_TONRESNA"" -
                                              COALESCE(r14.""C11_TONRECHAPR"", 0::numeric)                                             AS ""C12_TONELADAS"",
                                              pb.""REPEM03_Id""                                                                        AS ""CE_ID_TICKET"",
                                              COALESCE(tct.""REPEM10_Valor"", 0::numeric)                                              AS ""CE_VALOR_TICKET_LEGACY"",
                                              COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric)                     AS ""CE_VALOR_TON_TICKET_LEGACY"",
                                              COALESCE(r14.""C11_TONRECHAPR"", 0::numeric)                                             AS ""CE_TONELADAS_RECHAZADAS"",
                                              true                                                                                   AS ""CE_EXISTE_EN_F14"",
                                              pb.""REPEM03_Nro_Identificacion_Tributaria""                                             AS ""CE_NIT_EMPRESA"",
                                              r14.""C1_NUAP""                                                                          AS ""CE_NUAP"",
                                              r14.""C5_FECHA""                                                                         AS ""CE_FECHA_FILTRO""
                                       FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                                                JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                     ON pb.""REPEM03_Id"" = r14.""CE_ID_TICKET"" AND
                                                        pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                                LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                                          ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                                LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                                          ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                                                LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                                          ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND
                                                             pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                       UNION ALL
                                       SELECT 720105237                                                          AS ""C1_NUSD"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                                                  ELSE NULL::bigint
                                                  END                                                            AS ""C2_TIPO_ORIGEN"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                                                  ELSE pb.""REPEM03_Nro_Unico_Area_Prestacion""
                                                  END                                                            AS ""C3_NUSITIO_ORI"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE cli.""REPEM08_Nombre_Completo""
                                                  END                                                            AS ""C4_NOMBRE_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                                  ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                                  END                                                            AS ""C5_NIT_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE mun.""REPEM01_Codigo""
                                                  END                                                            AS ""C6_COD_DANE_ORI"",
                                              pb.""REPEM03_Patente""                                               AS ""C7_PLACA"",
                                              pb.""REPEM03_Fecha_de_entrada""::date                                AS ""C8_FECHA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::date                                 AS ""C9_FECHA_SALIDA"",
                                              pb.""REPEM03_Fecha_de_entrada""::time without time zone              AS ""C10_HORA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::time without time zone               AS ""C11_HORA_SALIDA"",
                                              tct.""REPEM10_Valor""::numeric / 1000::numeric -
                                              COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                   AS ""C12_TONELADAS"",
                                              pb.""REPEM03_Id""                                                    AS ""CE_ID_TICKET"",
                                              COALESCE(tct.""REPEM10_Valor"", 0::numeric)                          AS ""CE_VALOR_TICKET_LEGACY"",
                                              COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                                              COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                   AS ""CE_TONELADAS_RECHAZADAS"",
                                              false                                                              AS ""CE_EXISTE_EN_F14"",
                                              pb.""REPEM03_Nro_Identificacion_Tributaria""                         AS ""CE_NIT_EMPRESA"",
                                              pb.""REPEM03_Nro_Unico_Area_Prestacion""                             AS ""CE_NUAP"",
                                              pb.""REPEM03_Fecha_de_entrada""::date                                AS ""CE_FECHA_FILTRO""
                                       FROM ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                                          ON pb.""REPEM03_Id"" = tct.""REPEM10_Id""
                                                LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                                          ON pb.""REPEM03_Id"" = tre.""Ticket_Asignable"" AND
                                                             pb.""REPEM03_Nro_Unico_Area_Prestacion"" = 440405001
                                                LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                                          ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                                LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                                          ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                                       WHERE pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                         AND NOT (pb.""REPEM03_Id"" IN (SELECT DISTINCT rpm.""REPEM04_Id"" AS id
                                                                      FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                                                               JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                                                    ON rpm.""REPEM04_RutaCodigo""::text =
                                                                                       mic.""REPEM07_Numero_de_Microruta""::text))
                                       UNION ALL
                                       SELECT 720105237                                                          AS ""C1_NUSD"",
                                              '3'::bigint                                                        AS ""C2_TIPO_ORIGEN"",
                                              trept.""Num_ECA""                                                    AS ""C3_NUSITIO_ORI"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE cli.""REPEM08_Nombre_Completo""
                                                  END                                                            AS ""C4_NOMBRE_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                                  ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                                  END                                                            AS ""C5_NIT_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE mun.""REPEM01_Codigo""
                                                  END                                                            AS ""C6_COD_DANE_ORI"",
                                              pb.""REPEM03_Patente""                                               AS ""C7_PLACA"",
                                              pb.""REPEM03_Fecha_de_entrada""::date                                AS ""C8_FECHA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::date                                 AS ""C9_FECHA_SALIDA"",
                                              pb.""REPEM03_Fecha_de_entrada""::time without time zone              AS ""C10_HORA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::time without time zone               AS ""C11_HORA_SALIDA"",
                                              COALESCE(trept.""Toneladas_Rechazadas""::numeric, 0::numeric)        AS ""C12_TONELADAS"",
                                              pb.""REPEM03_Id""                                                    AS ""CE_ID_TICKET"",
                                              COALESCE(tct.""REPEM10_Valor"", 0::numeric)                          AS ""CE_VALOR_TICKET_LEGACY"",
                                              COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                                              0                                                                  AS ""CE_TONELADAS_RECHAZADAS"",
                                              true                                                               AS ""CE_EXISTE_EN_F14"",
                                              pb.""REPEM03_Nro_Identificacion_Tributaria""                         AS ""CE_NIT_EMPRESA"",
                                              440405001                                                          AS ""CE_NUAP"",
                                              r14.""C5_FECHA""                                                     AS ""CE_FECHA_FILTRO""
                                       FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                                                JOIN ""REPEM_Toneladas_Rechazos_Por_Ticket"" trept
                                                     ON r14.""CE_ID_TICKET"" = trept.""Id_Ticket"" AND trept.""NUAP"" = r14.""C1_NUAP""
                                                JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                     ON pb.""REPEM03_Id"" = r14.""CE_ID_TICKET"" AND
                                                        pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                                LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                                          ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                                LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                                          ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                                                LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                                          ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND
                                                             pb.""REPEM03_Fecha_de_cancelacion"" IS NULL)
                SELECT ""C1_NUSD"",
                       ""C2_TIPO_ORIGEN"",
                       ""C3_NUSITIO_ORI"",
                       ""C4_NOMBRE_EMPRESA"",
                       ""C5_NIT_EMPRESA"",
                       ""C6_COD_DANE_ORI"",
                       ""C7_PLACA"",
                       ""C8_FECHA_INGRESO"",
                       ""C9_FECHA_SALIDA"",
                       ""C10_HORA_INGRESO"",
                       ""C11_HORA_SALIDA"",
                       COALESCE(""C12_TONELADAS"", 0::numeric) AS ""C12_TONELADAS"",
                       ""CE_ID_TICKET"",
                       ""CE_VALOR_TICKET_LEGACY"",
                       ""CE_VALOR_TON_TICKET_LEGACY"",
                       ""CE_TONELADAS_RECHAZADAS"",
                       ""CE_EXISTE_EN_F14"",
                       ""CE_NIT_EMPRESA"",
                       ""CE_NUAP"",
                       ""CE_FECHA_FILTRO""
                FROM combined_data;
            ");
        }
        
        private void CreateDependantViewsForRollback(MigrationBuilder migrationBuilder)
        {
            //F14 
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14""
                            (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                             ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                             ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                             ""CE_REL_COMPENSACION_ID_TICKET"", ""CE_AJUSTE_DECIMAL"")
                as
                WITH cte AS (SELECT rpm.""REPEM04_Id""                                     AS ""Id_Ticket"",
                                    mic.""REPEM07_NUAP""                                   AS ""NUAP"",
                                    CASE
                                        WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                                 COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                        WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                            THEN tr.""Toneladas_Descuento_Medellin""
                                        ELSE tdha.""Toneladas_Resultantes""
                                        END                                              AS ""Calculo_Toneladas"",
                                    CASE
                                        WHEN mic.""REPEM07_Porcentaje_Barrido"" = 0 THEN 0::numeric
                                        ELSE (
                                            CASE
                                                WHEN mic.""REPEM07_NUAP"" = 440405001 THEN rpm.""REPEM04_PesoTotal_Toneladas"" -
                                                                                         COALESCE(tr.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado"" <> 0
                                                    THEN tr.""Toneladas_Descuento_Medellin""
                                                ELSE tdha.""Toneladas_Resultantes""
                                                END * (mic.""REPEM07_Porcentaje_Barrido""::numeric / 100::numeric))::numeric(18, 3)
                                        END::numeric(18, 3)                              AS ""Calculo_Barrido"",
                                    CASE
                                        WHEN pe.* IS NULL THEN 0::numeric
                                        WHEN mic.""REPEM07_NUAP"" = 440405001 THEN (pe.""REPEM06_Valor"" * 2::numeric::double precision -
                                                                                  COALESCE(tr.""Peaje_Descuento_Medellin"", 0::numeric)::double precision)::numeric
                                        WHEN mic.""REPEM07_NUAP"" = 440705360 AND mic.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                            THEN tr.""Peaje_Descuento_Medellin""
                                        ELSE (pe.""REPEM06_Valor"" * 2::numeric::double precision *
                                              (COALESCE(dpm.""Porcentaje_Distribucion_Peaje"", 100::numeric) /
                                               100::numeric)::double precision)::numeric
                                        END                                              AS ""Calculo_Peaje"",
                                    COALESCE(tdha.""Tipo_Compensacion"", 'ORIGINAL'::text) AS ""Tipo_Compensacion""
                             FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                      JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                           ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text
                                      LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dpm ON dpm.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                                                            dpm.""Año""::numeric =
                                                                                            EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") AND
                                                                                            dpm.""Mes""::numeric =
                                                                                            EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                      LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe
                                                ON pe.""REPEM06_Placa""::text = rpm.""REPEM04_Patente""::text AND
                                                   EXTRACT(year FROM pe.""REPEM06_Fecha_Validez"") =
                                                   EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"")
                                      LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm.""REPEM04_Id""
                                      LEFT JOIN ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                                ON tdha.""Id_Ticket"" = rpm.""REPEM04_Id"" AND tdha.""NUAP"" = mic.""REPEM07_NUAP"" AND
                                                   tdha.""Tipo_Compensacion"" = 'ORIGINAL'::text),
                     aggregated_compensations AS (SELECT 0                                                                           AS ""Id_Ticket"",
                                                         ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"",
                                                         sum(""REPEM_Tickets_Compensables"".""Suma_Agrupacion_Toneladas_Por_Compensar"") AS ""Suma_Agrupacion_Toneladas_Por_Compensar""
                                                  FROM ""REPEM_Tickets_Compensables""
                                                  GROUP BY ""REPEM_Tickets_Compensables"".""Nro_Ticket_Compensacion"")
                SELECT mic.""REPEM07_NUAP""                                         AS ""C1_NUAP"",
                       1                                                          AS ""C2_TIPO_SITIO"",
                       720105237                                                  AS ""C3_NUSD"",
                       rpm.""REPEM04_Patente""                                      AS ""C4_PLACA"",
                       rpm.""REPEM04_FechaHora_Pesaje""::date                       AS ""C5_FECHA"",
                       rpm.""REPEM04_FechaHora_Pesaje""::time without time zone     AS ""C6_HORA"",
                       mic.""REPEM07_Numero_Ruta_Intendencia""                      AS ""C7_NUMICRO"",
                       0                                                          AS ""C8_TON_LIMP_URB"",
                       round(bs.""Calculo_Barrido"" + COALESCE(tde.""Toneladas_Descuento"", 0::numeric), 3) -
                       COALESCE(dbi.""Valor_A_Descontar"", 0::numeric)              AS ""C9_TON_BARRIDO"",
                       CASE
                           WHEN mic.""REPEM07_Numero_de_Microruta"" = 614001 THEN round(
                                   bs.""Calculo_Toneladas"" - COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                           ELSE round(bs.""Calculo_Toneladas"" - bs.""Calculo_Barrido"" - COALESCE(tde.""Toneladas_Descuento"", 0::numeric) -
                                      COALESCE(rtc.""Suma_Agrupacion_Toneladas_Por_Compensar"", 0::numeric), 3)
                           END + COALESCE(compred.""Toneladas_Ajuste"", 0::numeric) AS ""C10_TONRESNA"",
                       COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)           AS ""C11_TONRECHAPR"",
                       CASE
                           WHEN mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN bs.""Calculo_Toneladas"" *
                                                                                                            (mic.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric /
                                                                                                             100::numeric)
                           ELSE 0::numeric
                           END                                                    AS ""C12_TONRESAPR"",
                       '1'::text                                                  AS ""C13_SISTEMA_MEDICION"",
                       bs.""Calculo_Peaje""                                         AS ""C14_VLRPEAJ"",
                       rpm.""REPEM04_Id""                                           AS ""CE_ID_TICKET"",
                       ap.""REPEM02_Nombre""                                        AS ""CE_NOMBRE_AREA"",
                       mic.""REPEM07_Ruta_Larga""                                   AS ""CE_RUTA_LARGA"",
                       rpm.""REPEM04_PesoTotal_Toneladas""                          AS ""CE_TON_TOTAL"",
                       rtc.* IS NOT NULL                                          AS ""CE_REL_COMPENSACION"",
                       rtc.""Id_Ticket""                                            AS ""CE_REL_COMPENSACION_ID_TICKET"",
                       compred.* IS NOT NULL                                      AS ""CE_AJUSTE_DECIMAL""
                FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                         JOIN cte bs ON bs.""Calculo_Toneladas"" IS NOT NULL AND rpm.""REPEM04_Id"" = bs.""Id_Ticket""
                         JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                              ON rpm.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                 mic.""REPEM07_NUAP"" = bs.""NUAP""
                         JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                         LEFT JOIN ""REPEM_Descuento_Pesaje_Excepcional_Por_Ticket"" tde ON rpm.""REPEM04_Id"" = tde.""NroTicket"" AND
                                                                                          EXTRACT(year FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                          tde.""Año"" AND
                                                                                          EXTRACT(month FROM rpm.""REPEM04_FechaHora_Pesaje"") =
                                                                                          tde.""Mes"" AND
                                                                                          mic.""REPEM07_Numero_de_Microruta"" =
                                                                                          tde.""RutaCodigo"" AND
                                                                                          mic.""REPEM07_NUAP"" = tde.""NUAP""
                         LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                   ON rpm.""REPEM04_Id"" = tre.""Ticket_Asignable"" AND mic.""REPEM07_NUAP"" = 440405001
                         LEFT JOIN aggregated_compensations rtc ON rpm.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                         LEFT JOIN ""REPEM_Descuento_de_Barrido_Itagui_Por_Ticket"" dbi ON dbi.""Id_Ticket_A_Descontar"" = rpm.""REPEM04_Id""
                         LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" compred
                                   ON compred.""Nro_Ticket_Ajustable"" = rpm.""REPEM04_Id"" AND compred.""NUAP"" = mic.""REPEM07_NUAP""
                UNION ALL
                SELECT ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C1_NUAP"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C2_TIPO_SITIO"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C3_NUSD"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C4_PLACA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C5_FECHA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C6_HORA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C7_NUMICRO"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C8_TON_LIMP_URB"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C9_TON_BARRIDO"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C10_TONRESNA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C11_TONRECHAPR"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C12_TONRESAPR"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C13_SISTEMA_MEDICION"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""C14_VLRPEAJ"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_ID_TICKET"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_NOMBRE_AREA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_RUTA_LARGA"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_TON_TOTAL"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_REL_COMPENSACION"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_REL_COMPENSACION_ID_TICKET"",
                       ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"".""CE_AJUSTE_DECIMAL""
                FROM ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones"";
            ");
            
            //F14 Aditivos
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14_con_Aditivos""
                            (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                             ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                             ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                             ""CE_REL_COMPENSACION_ID_TICKET"")
                as
                SELECT f14.""C1_NUAP"",
                       f14.""C2_TIPO_SITIO"",
                       f14.""C3_NUSD""::character varying(10) AS ""C3_NUSD"",
                       f14.""C4_PLACA"",
                       f14.""C5_FECHA"",
                       f14.""C6_HORA"",
                       f14.""C7_NUMICRO"",
                       f14.""C8_TON_LIMP_URB""::numeric       AS ""C8_TON_LIMP_URB"",
                       f14.""C9_TON_BARRIDO"",
                       f14.""C10_TONRESNA"",
                       f14.""C11_TONRECHAPR"",
                       f14.""C12_TONRESAPR"",
                       f14.""C13_SISTEMA_MEDICION""::integer  AS ""C13_SISTEMA_MEDICION"",
                       f14.""C14_VLRPEAJ"",
                       f14.""CE_ID_TICKET"",
                       f14.""CE_NOMBRE_AREA"",
                       f14.""CE_RUTA_LARGA"",
                       f14.""CE_TON_TOTAL""::numeric          AS ""CE_TON_TOTAL"",
                       f14.""CE_REL_COMPENSACION"",
                       f14.""CE_REL_COMPENSACION_ID_TICKET""
                FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" f14
                UNION ALL
                SELECT clu.""C1_NUAP"",
                       clu.""C2_TIPO_SITIO"",
                       clu.""C3_NUSD""::character varying(10) AS ""C3_NUSD"",
                       clu.""C4_PLACA"",
                       clu.""C5_FECHA"",
                       clu.""C6_HORA"",
                       clu.""C7_NUMICRO"",
                       clu.""C8_TON_LIMP_URB""::numeric       AS ""C8_TON_LIMP_URB"",
                       clu.""C9_TON_BARRIDO""::numeric        AS ""C9_TON_BARRIDO"",
                       clu.""C10_TONRESNA""::numeric          AS ""C10_TONRESNA"",
                       clu.""C11_TONRECHAPR""::numeric        AS ""C11_TONRECHAPR"",
                       clu.""C12_TONRESAPR""::numeric         AS ""C12_TONRESAPR"",
                       clu.""C13_SISTEMA_MEDICION"",
                       clu.""C14_VLRPEAJ""::numeric           AS ""C14_VLRPEAJ"",
                       NULL::bigint                         AS ""CE_ID_TICKET"",
                       'CLUS - CARGADO'::character varying  AS ""CE_NOMBRE_AREA"",
                       clu.""CE_RUTA_LARGA"",
                       clu.""CE_TON_TOTAL""::numeric          AS ""CE_TON_TOTAL"",
                       NULL::boolean                        AS ""CE_REL_COMPENSACION"",
                       NULL::bigint                         AS ""CE_REL_COMPENSACION_ID_TICKET""
                FROM ""Reporting-Emvarias_11-Reporte_F14_Adiciones_CLU"" clu
                UNION ALL
                SELECT apr.""C1_NUAP"",
                       apr.""C2_TIPO_SITIO"",
                       apr.""C3_NUSD""::character varying(10)           AS ""C3_NUSD"",
                       apr.""C4_PLACA"",
                       apr.""C5_FECHA"",
                       apr.""C6_HORA"",
                       apr.""C7_NUMICRO"",
                       apr.""C8_TON_LIMP_URB""::numeric                 AS ""C8_TON_LIMP_URB"",
                       apr.""C9_TON_BARRIDO""::numeric                  AS ""C9_TON_BARRIDO"",
                       apr.""C10_TONRESNA""::numeric                    AS ""C10_TONRESNA"",
                       apr.""C11_TONRECHAPR""::numeric                  AS ""C11_TONRECHAPR"",
                       apr.""C12_TONRESAPR""::numeric                   AS ""C12_TONRESAPR"",
                       apr.""C13_SISTEMA_MEDICION"",
                       apr.""C14_VLRPEAJ""::numeric                     AS ""C14_VLRPEAJ"",
                       NULL::bigint                                   AS ""CE_ID_TICKET"",
                       'APROVECHAMIENTO - CARGADO'::character varying AS ""CE_NOMBRE_AREA"",
                       apr.""CE_RUTA_LARGA"",
                       apr.""CE_TON_TOTAL""::numeric                    AS ""CE_TON_TOTAL"",
                       NULL::boolean                                  AS ""CE_REL_COMPENSACION"",
                       NULL::bigint                                   AS ""CE_REL_COMPENSACION_ID_TICKET""
                FROM ""Reporting-Emvarias_12-Reporte_F14_Adiciones_Aprovechamiento"" apr;
            ");
            
            //F34 
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Reporte_SUI_Recolecciones_F34""
                (""C1_NUSD"", ""C2_TIPO_ORIGEN"", ""C3_NUSITIO_ORI"", ""C4_NOMBRE_EMPRESA"", ""C5_NIT_EMPRESA"", ""C6_COD_DANE_ORI"",
                 ""C7_PLACA"", ""C8_FECHA_INGRESO"", ""C9_FECHA_SALIDA"", ""C10_HORA_INGRESO"", ""C11_HORA_SALIDA"", ""C12_TONELADAS"",
                 ""CE_ID_TICKET"", ""CE_VALOR_TICKET_LEGACY"", ""CE_VALOR_TON_TICKET_LEGACY"", ""CE_TONELADAS_RECHAZADAS"",
                 ""CE_EXISTE_EN_F14"", ""CE_NIT_EMPRESA"", ""CE_NUAP"", ""CE_FECHA_FILTRO"")
                as
                WITH combined_data AS (SELECT 720105237                                                                              AS ""C1_NUSD"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                                                  ELSE NULL::bigint
                                                  END                                                                                AS ""C2_TIPO_ORIGEN"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                                                  ELSE r14.""C1_NUAP""
                                                  END                                                                                AS ""C3_NUSITIO_ORI"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE cli.""REPEM08_Nombre_Completo""
                                                  END                                                                                AS ""C4_NOMBRE_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                                  ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                                  END                                                                                AS ""C5_NIT_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE mun.""REPEM01_Codigo""
                                                  END                                                                                AS ""C6_COD_DANE_ORI"",
                                              pb.""REPEM03_Patente""                                                                   AS ""C7_PLACA"",
                                              pb.""REPEM03_Fecha_de_entrada""::date                                                    AS ""C8_FECHA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::date                                                     AS ""C9_FECHA_SALIDA"",
                                              pb.""REPEM03_Fecha_de_entrada""::time without time zone                                  AS ""C10_HORA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::time without time zone                                   AS ""C11_HORA_SALIDA"",
                                              r14.""C9_TON_BARRIDO"" + r14.""C10_TONRESNA"" -
                                              COALESCE(r14.""C11_TONRECHAPR"", 0::numeric)                                             AS ""C12_TONELADAS"",
                                              pb.""REPEM03_Id""                                                                        AS ""CE_ID_TICKET"",
                                              COALESCE(tct.""REPEM10_Valor"", 0::numeric)                                              AS ""CE_VALOR_TICKET_LEGACY"",
                                              COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric)                     AS ""CE_VALOR_TON_TICKET_LEGACY"",
                                              COALESCE(r14.""C11_TONRECHAPR"", 0::numeric)                                             AS ""CE_TONELADAS_RECHAZADAS"",
                                              true                                                                                   AS ""CE_EXISTE_EN_F14"",
                                              pb.""REPEM03_Nro_Identificacion_Tributaria""                                             AS ""CE_NIT_EMPRESA"",
                                              r14.""C1_NUAP""                                                                          AS ""CE_NUAP"",
                                              r14.""C5_FECHA""                                                                         AS ""CE_FECHA_FILTRO""
                                       FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                                                JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                     ON pb.""REPEM03_Id"" = r14.""CE_ID_TICKET"" AND
                                                        pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                                LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                                          ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                                LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                                          ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                                                LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                                          ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND
                                                             pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                       UNION ALL
                                       SELECT 720105237                                                          AS ""C1_NUSD"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN '4'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUAP'::text THEN '1'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUET'::text THEN '2'::bigint
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'NUECA'::text THEN '3'::bigint
                                                  ELSE NULL::bigint
                                                  END                                                            AS ""C2_TIPO_ORIGEN"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" = 'Others'::text THEN NULL::bigint
                                                  ELSE pb.""REPEM03_Nro_Unico_Area_Prestacion""
                                                  END                                                            AS ""C3_NUSITIO_ORI"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE cli.""REPEM08_Nombre_Completo""
                                                  END                                                            AS ""C4_NOMBRE_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                                  ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                                  END                                                            AS ""C5_NIT_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE mun.""REPEM01_Codigo""
                                                  END                                                            AS ""C6_COD_DANE_ORI"",
                                              pb.""REPEM03_Patente""                                               AS ""C7_PLACA"",
                                              pb.""REPEM03_Fecha_de_entrada""::date                                AS ""C8_FECHA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::date                                 AS ""C9_FECHA_SALIDA"",
                                              pb.""REPEM03_Fecha_de_entrada""::time without time zone              AS ""C10_HORA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::time without time zone               AS ""C11_HORA_SALIDA"",
                                              tct.""REPEM10_Valor""::numeric / 1000::numeric -
                                              COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                   AS ""C12_TONELADAS"",
                                              pb.""REPEM03_Id""                                                    AS ""CE_ID_TICKET"",
                                              COALESCE(tct.""REPEM10_Valor"", 0::numeric)                          AS ""CE_VALOR_TICKET_LEGACY"",
                                              COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                                              COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                   AS ""CE_TONELADAS_RECHAZADAS"",
                                              false                                                              AS ""CE_EXISTE_EN_F14"",
                                              pb.""REPEM03_Nro_Identificacion_Tributaria""                         AS ""CE_NIT_EMPRESA"",
                                              pb.""REPEM03_Nro_Unico_Area_Prestacion""                             AS ""CE_NUAP"",
                                              pb.""REPEM03_Fecha_de_entrada""::date                                AS ""CE_FECHA_FILTRO""
                                       FROM ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                                          ON pb.""REPEM03_Id"" = tct.""REPEM10_Id""
                                                LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                                                          ON pb.""REPEM03_Id"" = tre.""Ticket_Asignable"" AND
                                                             pb.""REPEM03_Nro_Unico_Area_Prestacion"" = 440405001
                                                LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                                          ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                                LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                                          ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                                       WHERE pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                         AND NOT (pb.""REPEM03_Id"" IN (SELECT DISTINCT rpm.""REPEM04_Id"" AS id
                                                                      FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm
                                                                               JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                                                    ON rpm.""REPEM04_RutaCodigo""::text =
                                                                                       mic.""REPEM07_Numero_de_Microruta""::text))
                                       UNION ALL
                                       SELECT 720105237                                                          AS ""C1_NUSD"",
                                              '3'::bigint                                                        AS ""C2_TIPO_ORIGEN"",
                                              trept.""Num_ECA""                                                    AS ""C3_NUSITIO_ORI"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE cli.""REPEM08_Nombre_Completo""
                                                  END                                                            AS ""C4_NOMBRE_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::bigint
                                                  ELSE pb.""REPEM03_Nro_Identificacion_Tributaria""
                                                  END                                                            AS ""C5_NIT_EMPRESA"",
                                              CASE
                                                  WHEN pb.""REPEM03_Tipo_de_origen"" <> 'Others'::text THEN NULL::character varying
                                                  ELSE mun.""REPEM01_Codigo""
                                                  END                                                            AS ""C6_COD_DANE_ORI"",
                                              pb.""REPEM03_Patente""                                               AS ""C7_PLACA"",
                                              pb.""REPEM03_Fecha_de_entrada""::date                                AS ""C8_FECHA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::date                                 AS ""C9_FECHA_SALIDA"",
                                              pb.""REPEM03_Fecha_de_entrada""::time without time zone              AS ""C10_HORA_INGRESO"",
                                              pb.""REPEM03_Fecha_de_egreso""::time without time zone               AS ""C11_HORA_SALIDA"",
                                              COALESCE(trept.""Toneladas_Rechazadas""::numeric, 0::numeric)        AS ""C12_TONELADAS"",
                                              pb.""REPEM03_Id""                                                    AS ""CE_ID_TICKET"",
                                              COALESCE(tct.""REPEM10_Valor"", 0::numeric)                          AS ""CE_VALOR_TICKET_LEGACY"",
                                              COALESCE(tct.""REPEM10_Valor""::numeric / 1000::numeric, 0::numeric) AS ""CE_VALOR_TON_TICKET_LEGACY"",
                                              0                                                                  AS ""CE_TONELADAS_RECHAZADAS"",
                                              true                                                               AS ""CE_EXISTE_EN_F14"",
                                              pb.""REPEM03_Nro_Identificacion_Tributaria""                         AS ""CE_NIT_EMPRESA"",
                                              440405001                                                          AS ""CE_NUAP"",
                                              r14.""C5_FECHA""                                                     AS ""CE_FECHA_FILTRO""
                                       FROM ""REPEM_Reporte_SUI_Recolecciones_F14"" r14
                                                JOIN ""REPEM_Toneladas_Rechazos_Por_Ticket"" trept
                                                     ON r14.""CE_ID_TICKET"" = trept.""Id_Ticket"" AND trept.""NUAP"" = r14.""C1_NUAP""
                                                JOIN ""Reporting-Emvarias_03-Pesaje_de_Balanza"" pb
                                                     ON pb.""REPEM03_Id"" = r14.""CE_ID_TICKET"" AND
                                                        pb.""REPEM03_Fecha_de_cancelacion"" IS NULL
                                                LEFT JOIN ""Reporting-Emvarias_08-Clientes"" cli
                                                          ON pb.""REPEM03_Nro_Identificacion_Tributaria"" = cli.""REPEM08_NIT""
                                                LEFT JOIN ""Reporting-Emvarias_01-Municipios"" mun
                                                          ON pb.""REPEM03_Municipio""::text = mun.""REPEM01_Codigo""::text
                                                LEFT JOIN ""Reporting-Emvarias_10-Tickets_de_Servicio_Generados"" tct
                                                          ON pb.""REPEM03_Id"" = tct.""REPEM10_Id"" AND
                                                             pb.""REPEM03_Fecha_de_cancelacion"" IS NULL)
                SELECT ""C1_NUSD"",
                       ""C2_TIPO_ORIGEN"",
                       ""C3_NUSITIO_ORI"",
                       ""C4_NOMBRE_EMPRESA"",
                       ""C5_NIT_EMPRESA"",
                       ""C6_COD_DANE_ORI"",
                       ""C7_PLACA"",
                       ""C8_FECHA_INGRESO"",
                       ""C9_FECHA_SALIDA"",
                       ""C10_HORA_INGRESO"",
                       ""C11_HORA_SALIDA"",
                       COALESCE(""C12_TONELADAS"", 0::numeric) AS ""C12_TONELADAS"",
                       ""CE_ID_TICKET"",
                       ""CE_VALOR_TICKET_LEGACY"",
                       ""CE_VALOR_TON_TICKET_LEGACY"",
                       ""CE_TONELADAS_RECHAZADAS"",
                       ""CE_EXISTE_EN_F14"",
                       ""CE_NIT_EMPRESA"",
                       ""CE_NUAP"",
                       ""CE_FECHA_FILTRO""
                FROM combined_data;
            ");
        }
        
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            DropDependantViews(migrationBuilder);
            
            migrationBuilder.Sql(@"
            create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones""
            (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
             ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
             ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
             ""CE_REL_COMPENSACION_ID_TICKET"", ""CE_AJUSTE_DECIMAL"")
            as
            WITH cte_comp AS (SELECT tdha.""Id_Ticket"",
                                     tdha.""NUAP"",
                                     tdha.""Toneladas_Resultantes"",
                                     tdha.""Tipo_Compensacion"",
                                     tdha.""Tipo_Compensacion"",
                                     rpm_1.""REPEM04_PesoTotal_Toneladas"",
                                     mic.""REPEM07_Numero_Ruta_Intendencia"" AS ""Numero_Ruta_Indendencia"",
                                     mic.""REPEM07_Ruta_Larga"",
                                     mic.""REPEM07_Porcentaje_No_Aforado"",
                                     mic.""REPEM07_Porcentaje_Barrido"",
                                     mic.""REPEM07_Porcentaje_Residuos_Aprovechables"",
                                     ap.""REPEM02_Nombre""                   AS ""Area_Aprovechamiento"",
                                     dm.""Porcentaje_Distribucion_Peaje"",
                                     pe.""REPEM06_Valor""::numeric           AS ""Valor_Peaje"",
                                     tr.""Toneladas_Descuento_Medellin"",
                                     tr.""Peaje_Descuento_Medellin""
                              FROM ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                       JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm_1
                                            ON tdha.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                       JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                            ON rpm_1.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                               mic.""REPEM07_NUAP"" = tdha.""NUAP""
                                       JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                            ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                                       LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                       LEFT JOIN ""REPEM_Distribuciones_de_Microrutas"" dm
                                                 ON dm.""Año""::numeric = EXTRACT(year FROM rpm_1.""REPEM04_FechaHora_Pesaje"") AND
                                                    dm.""Mes""::numeric = EXTRACT(month FROM rpm_1.""REPEM04_FechaHora_Pesaje"") AND
                                                    dm.""NUAP"" = tdha.""NUAP""
                                       LEFT JOIN ""Reporting-Emvarias_06-Peajes"" pe
                                                 ON pe.""REPEM06_Placa""::text = rpm_1.""REPEM04_Patente""::text AND
                                                    EXTRACT(year FROM pe.""REPEM06_Fecha_Validez"") =
                                                    EXTRACT(year FROM rpm_1.""REPEM04_FechaHora_Pesaje"")
                              WHERE tdha.""Tipo_Compensacion"" = ANY (ARRAY ['TOTAL'::text, 'PARCIAL'::text])),
                 calculated_comp AS (SELECT c.""Id_Ticket"",
                                            c.""NUAP"",
                                            c.""Toneladas_Resultantes"",
                                            c.""Tipo_Compensacion"",
                                            c.""Tipo_Compensacion_1"" AS ""Tipo_Compensacion"",
                                            c.""REPEM04_PesoTotal_Toneladas"",
                                            c.""Numero_Ruta_Indendencia"",
                                            c.""REPEM07_Ruta_Larga"",
                                            c.""REPEM07_Porcentaje_No_Aforado"",
                                            c.""REPEM07_Porcentaje_Barrido"",
                                            c.""REPEM07_Porcentaje_Residuos_Aprovechables"",
                                            c.""Area_Aprovechamiento"",
                                            c.""Porcentaje_Distribucion_Peaje"",
                                            c.""Valor_Peaje"",
                                            c.""Toneladas_Descuento_Medellin"",
                                            c.""Peaje_Descuento_Medellin"",
                                            CASE
                                                WHEN c.""NUAP"" = 440405001 THEN c.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                               COALESCE(c.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                WHEN c.""NUAP"" = 440705360 AND c.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                                    THEN COALESCE(c.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                ELSE c.""Toneladas_Resultantes""
                                                END                 AS ""Calculo_Toneladas"",
                                            CASE
                                                WHEN c.""Porcentaje_Distribucion_Peaje"" IS NOT NULL AND
                                                     c.""Valor_Peaje"" IS NOT NULL AND c.""NUAP"" = '440705360'::bigint AND
                                                     c.""REPEM07_Porcentaje_No_Aforado"" <> 0 THEN c.""Peaje_Descuento_Medellin""
                                                WHEN c.""Porcentaje_Distribucion_Peaje"" IS NOT NULL AND c.""Valor_Peaje"" IS NOT NULL
                                                    THEN c.""Valor_Peaje"" * 2::numeric * c.""Porcentaje_Distribucion_Peaje""::numeric /
                                                         100::numeric
                                                ELSE 0::numeric
                                                END                 AS ""Calculo_Peaje""
                                     FROM cte_comp c(""Id_Ticket"", ""NUAP"", ""Toneladas_Resultantes"", ""Tipo_Compensacion"",
                                                     ""Tipo_Compensacion_1"", ""REPEM04_PesoTotal_Toneladas"",
                                                     ""Numero_Ruta_Indendencia"", ""REPEM07_Ruta_Larga"",
                                                     ""REPEM07_Porcentaje_No_Aforado"", ""REPEM07_Porcentaje_Barrido"",
                                                     ""REPEM07_Porcentaje_Residuos_Aprovechables"", ""Area_Aprovechamiento"",
                                                     ""Porcentaje_Distribucion_Peaje"", ""Valor_Peaje"", ""Toneladas_Descuento_Medellin"",
                                                     ""Peaje_Descuento_Medellin""))
            SELECT cc.""NUAP""                                                              AS ""C1_NUAP"",
                   1                                                                      AS ""C2_TIPO_SITIO"",
                   720105237                                                              AS ""C3_NUSD"",
                   compensation.""REPEM04_Patente""                                         AS ""C4_PLACA"",
                   compensation.""REPEM04_FechaHora_Pesaje""::date                          AS ""C5_FECHA"",
                   compensation.""REPEM04_FechaHora_Pesaje""::time without time zone        AS ""C6_HORA"",
                   cc.""Numero_Ruta_Indendencia""                                           AS ""C7_NUMICRO"",
                   0                                                                      AS ""C8_TON_LIMP_URB"",
                   0                                                                      AS ""C9_TON_BARRIDO"",
                   cc.""Calculo_Toneladas"" + COALESCE(cred.""Toneladas_Ajuste"", 0::numeric) AS ""C10_TONRESNA"",
                   COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                       AS ""C11_TONRECHAPR"",
                   CASE
                       WHEN cc.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN cc.""Calculo_Toneladas"" *
                                                                                                       (cc.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric / 100::numeric)
                       ELSE 0::numeric
                       END                                                                AS ""C12_TONRESAPR"",
                   '1'::text                                                              AS ""C13_SISTEMA_MEDICION"",
                   cc.""Calculo_Peaje""                                                     AS ""C14_VLRPEAJ"",
                   rtc.""Nro_Ticket_Compensacion""                                          AS ""CE_ID_TICKET"",
                   cc.""Area_Aprovechamiento""                                              AS ""CE_NOMBRE_AREA"",
                   cc.""REPEM07_Ruta_Larga""                                                AS ""CE_RUTA_LARGA"",
                   rtc.""Maximo_Toneladas_Compensables""                                    AS ""CE_TON_TOTAL"",
                   true                                                                   AS ""CE_REL_COMPENSACION"",
                   rtc.""Id_Ticket""                                                        AS ""CE_REL_COMPENSACION_ID_TICKET"",
                   false                                                                  AS ""CE_AJUSTE_DECIMAL""
            FROM calculated_comp cc(""Id_Ticket"", ""NUAP"", ""Toneladas_Resultantes"", ""Tipo_Compensacion"", ""Tipo_Compensacion_1"",
                                    ""REPEM04_PesoTotal_Toneladas"", ""Numero_Ruta_Indendencia"", ""REPEM07_Ruta_Larga"",
                                    ""REPEM07_Porcentaje_No_Aforado"", ""REPEM07_Porcentaje_Barrido"",
                                    ""REPEM07_Porcentaje_Residuos_Aprovechables"", ""Area_Aprovechamiento"",
                                    ""Porcentaje_Distribucion_Peaje"", ""Valor_Peaje"", ""Toneladas_Descuento_Medellin"",
                                    ""Peaje_Descuento_Medellin"", ""Calculo_Toneladas"", ""Calculo_Peaje"")
                     JOIN ""REPEM_Tickets_Compensables"" rtc ON cc.""Id_Ticket"" = rtc.""Id_Ticket""
                     JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" compensation
                          ON compensation.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                     LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                               ON cc.""Id_Ticket"" = tre.""Ticket_Asignable"" AND cc.""NUAP"" = 440405001
                     LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" cred
                               ON cc.""Id_Ticket"" = cred.""Nro_Ticket_Ajustable"" AND cc.""NUAP"" = cred.""NUAP"";
            ");
            
            CreateDependantViewsForUpdate(migrationBuilder);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            DropDependantViews(migrationBuilder);
            
            migrationBuilder.Sql(@"
            create or replace view ""REPEM_Reporte_SUI_Recolecciones_F14_Compensaciones""
                        (""C1_NUAP"", ""C2_TIPO_SITIO"", ""C3_NUSD"", ""C4_PLACA"", ""C5_FECHA"", ""C6_HORA"", ""C7_NUMICRO"", ""C8_TON_LIMP_URB"",
                         ""C9_TON_BARRIDO"", ""C10_TONRESNA"", ""C11_TONRECHAPR"", ""C12_TONRESAPR"", ""C13_SISTEMA_MEDICION"", ""C14_VLRPEAJ"",
                         ""CE_ID_TICKET"", ""CE_NOMBRE_AREA"", ""CE_RUTA_LARGA"", ""CE_TON_TOTAL"", ""CE_REL_COMPENSACION"",
                         ""CE_REL_COMPENSACION_ID_TICKET"", ""CE_AJUSTE_DECIMAL"")
            as
            WITH cte_comp AS (SELECT tdha.""Id_Ticket"",
                                     tdha.""NUAP"",
                                     tdha.""Toneladas_Resultantes"",
                                     tdha.""Tipo_Compensacion"",
                                     tdha.""Tipo_Compensacion"",
                                     rpm_1.""REPEM04_PesoTotal_Toneladas"",
                                     mic.""REPEM07_Numero_Ruta_Intendencia"" AS ""Numero_Ruta_Indendencia"",
                                     mic.""REPEM07_Ruta_Larga"",
                                     mic.""REPEM07_Porcentaje_No_Aforado"",
                                     mic.""REPEM07_Porcentaje_Barrido"",
                                     mic.""REPEM07_Porcentaje_Residuos_Aprovechables"",
                                     ap.""REPEM02_Nombre""                   AS ""Area_Aprovechamiento"",
                                     tr.""Toneladas_Descuento_Medellin""
                              FROM ""REPEM_Detalle_Compensacion_Tickets"" tdha
                                       JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm_1
                                            ON tdha.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                                       JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                            ON rpm_1.""REPEM04_RutaCodigo""::text = mic.""REPEM07_Numero_de_Microruta""::text AND
                                               mic.""REPEM07_NUAP"" = tdha.""NUAP""
                                       JOIN ""Reporting-Emvarias_02-Areas_de_Aprovechamiento"" ap
                                            ON ap.""REPEM02_Codigo"" = mic.""REPEM07_NUAP""
                                       LEFT JOIN ""REPEM_Descuento_Por_Ticket"" tr ON tr.""Id_Ticket"" = rpm_1.""REPEM04_Id""
                              WHERE tdha.""Tipo_Compensacion"" = ANY (ARRAY ['TOTAL'::text, 'PARCIAL'::text])),
                 calculated_comp AS (SELECT c.""Id_Ticket"",
                                            c.""NUAP"",
                                            c.""Toneladas_Resultantes"",
                                            c.""Tipo_Compensacion"",
                                            c.""Tipo_Compensacion_1"" AS ""Tipo_Compensacion"",
                                            c.""REPEM04_PesoTotal_Toneladas"",
                                            c.""Numero_Ruta_Indendencia"",
                                            c.""REPEM07_Ruta_Larga"",
                                            c.""REPEM07_Porcentaje_No_Aforado"",
                                            c.""REPEM07_Porcentaje_Barrido"",
                                            c.""REPEM07_Porcentaje_Residuos_Aprovechables"",
                                            c.""Area_Aprovechamiento"",
                                            c.""Toneladas_Descuento_Medellin"",
                                            CASE
                                                WHEN c.""NUAP"" = 440405001 THEN c.""REPEM04_PesoTotal_Toneladas""::numeric -
                                                                               COALESCE(c.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                WHEN c.""NUAP"" = 440705360 AND c.""REPEM07_Porcentaje_No_Aforado""::numeric <> 0::numeric
                                                    THEN COALESCE(c.""Toneladas_Descuento_Medellin"", 0::numeric)
                                                ELSE c.""Toneladas_Resultantes""
                                                END                 AS ""Calculo_Toneladas""
                                     FROM cte_comp c(""Id_Ticket"", ""NUAP"", ""Toneladas_Resultantes"", ""Tipo_Compensacion"",
                                                     ""Tipo_Compensacion_1"", ""REPEM04_PesoTotal_Toneladas"",
                                                     ""Numero_Ruta_Indendencia"", ""REPEM07_Ruta_Larga"",
                                                     ""REPEM07_Porcentaje_No_Aforado"", ""REPEM07_Porcentaje_Barrido"",
                                                     ""REPEM07_Porcentaje_Residuos_Aprovechables"", ""Area_Aprovechamiento"",
                                                     ""Toneladas_Descuento_Medellin""))
            SELECT cc.""NUAP""                                                              AS ""C1_NUAP"",
                   1                                                                      AS ""C2_TIPO_SITIO"",
                   720105237                                                              AS ""C3_NUSD"",
                   compensation.""REPEM04_Patente""                                         AS ""C4_PLACA"",
                   compensation.""REPEM04_FechaHora_Pesaje""::date                          AS ""C5_FECHA"",
                   compensation.""REPEM04_FechaHora_Pesaje""::time without time zone        AS ""C6_HORA"",
                   cc.""Numero_Ruta_Indendencia""                                           AS ""C7_NUMICRO"",
                   0                                                                      AS ""C8_TON_LIMP_URB"",
                   0                                                                      AS ""C9_TON_BARRIDO"",
                   cc.""Calculo_Toneladas"" + COALESCE(cred.""Toneladas_Ajuste"", 0::numeric) AS ""C10_TONRESNA"",
                   COALESCE(tre.""Toneladas_Rechazadas"", 0::numeric)                       AS ""C11_TONRECHAPR"",
                   CASE
                       WHEN cc.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric <> 0::numeric THEN cc.""Calculo_Toneladas"" *
                                                                                                       (cc.""REPEM07_Porcentaje_Residuos_Aprovechables""::numeric / 100::numeric)
                       ELSE 0::numeric
                       END                                                                AS ""C12_TONRESAPR"",
                   '1'::text                                                              AS ""C13_SISTEMA_MEDICION"",
                   0::numeric                                                             AS ""C14_VLRPEAJ"",
                   rtc.""Nro_Ticket_Compensacion""                                          AS ""CE_ID_TICKET"",
                   cc.""Area_Aprovechamiento""                                              AS ""CE_NOMBRE_AREA"",
                   cc.""REPEM07_Ruta_Larga""                                                AS ""CE_RUTA_LARGA"",
                   rtc.""Maximo_Toneladas_Compensables""                                    AS ""CE_TON_TOTAL"",
                   true                                                                   AS ""CE_REL_COMPENSACION"",
                   rtc.""Id_Ticket""                                                        AS ""CE_REL_COMPENSACION_ID_TICKET"",
                   false                                                                  AS ""CE_AJUSTE_DECIMAL""
            FROM calculated_comp cc(""Id_Ticket"", ""NUAP"", ""Toneladas_Resultantes"", ""Tipo_Compensacion"", ""Tipo_Compensacion_1"",
                                    ""REPEM04_PesoTotal_Toneladas"", ""Numero_Ruta_Indendencia"", ""REPEM07_Ruta_Larga"",
                                    ""REPEM07_Porcentaje_No_Aforado"", ""REPEM07_Porcentaje_Barrido"",
                                    ""REPEM07_Porcentaje_Residuos_Aprovechables"", ""Area_Aprovechamiento"",
                                    ""Toneladas_Descuento_Medellin"", ""Calculo_Toneladas"")
                     JOIN ""REPEM_Tickets_Compensables"" rtc ON cc.""Id_Ticket"" = rtc.""Id_Ticket""
                     JOIN ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" compensation
                          ON compensation.""REPEM04_Id"" = rtc.""Nro_Ticket_Compensacion""
                     LEFT JOIN ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket"" tre
                               ON cc.""Id_Ticket"" = tre.""Ticket_Asignable"" AND cc.""NUAP"" = 440405001
                     LEFT JOIN ""REPEM_Compensaciones_por_Redondeo"" cred
                               ON cc.""Id_Ticket"" = cred.""Nro_Ticket_Ajustable"" AND cc.""NUAP"" = cred.""NUAP"";
            ");
            
            CreateDependantViewsForRollback(migrationBuilder);
        }
    }
}
