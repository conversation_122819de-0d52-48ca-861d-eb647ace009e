﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Orion.SharedKernel.Infrastructure.Data.EFCore;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Settings;
using Reports.Infrastructure.Data.EFCore;
using Reports.Infrastructure.Data.EFCore.Repositories;
using Reports.Infrastructure.Data.Http;

namespace Reports.Infrastructure;

public static class ServicesExtensions
{
    public static IServiceCollection AddReportsInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddEFCorePostgre<ReportsDbContext>(configuration.GetConnectionString(Providers.PostgreSql)!);

        services.AddReportsRepositories();
        
        services.AddHttpClients(configuration);

        return services;
    }
}