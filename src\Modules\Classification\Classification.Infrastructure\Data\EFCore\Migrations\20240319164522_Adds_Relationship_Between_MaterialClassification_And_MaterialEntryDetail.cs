﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Classification.Infrastructure.Data.EFCore.Migrations
{
    public partial class Adds_Relationship_Between_MaterialClassification_And_MaterialEntryDetail : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "RECYECA05_Id_de_Clasificacion_de_Material",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Recycling-ECA_05-Detalle_Ingreso_de_Material_RECYECA05_Id_d~",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material",
                column: "RECYECA05_Id_de_Clasificacion_de_Material");

            migrationBuilder.AddForeignKey(
                name: "Recycling-ECA_RECYECA05-RECYECA07_fkey",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material",
                column: "RECYECA05_Id_de_Clasificacion_de_Material",
                principalTable: "Recycling-ECA_07-Clasificacion_de_Material",
                principalColumn: "RECYECA07_Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "Recycling-ECA_RECYECA05-RECYECA07_fkey",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material");

            migrationBuilder.DropIndex(
                name: "IX_Recycling-ECA_05-Detalle_Ingreso_de_Material_RECYECA05_Id_d~",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material");

            migrationBuilder.DropColumn(
                name: "RECYECA05_Id_de_Clasificacion_de_Material",
                table: "Recycling-ECA_05-Detalle_Ingreso_de_Material");
        }
    }
}
