﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Fix_Microrutas_Asignadas_Detectadas_SUI : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "OutboxMessages",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    EventName = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    EventType = table.Column<string>(type: "character varying(30)", maxLength: 30, nullable: false),
                    EventStatus = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    Content = table.Column<string>(type: "text", nullable: false),
                    OcurredOnUtc = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ProcessedOnUtc = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Error = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OutboxMessages", x => x.Id);
                });

            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket""
                            (""Ticket_Asignable"", ""Toneladas_Rechazadas"", ""Cantidad_Rechazos"", ""Fecha_Rechazo"") as
                SELECT rpm.""REPEM04_Id""                           AS ""Ticket_Asignable"",
                       sum(r.""REPEM09_Toneladas"")::numeric(18, 3) AS ""Toneladas_Rechazadas"",
                       count(r.""REPEM09_Id"")                      AS ""Cantidad_Rechazos"",
                       max(r.""REPEM09_Fecha_Corta"")               AS ""Fecha_Rechazo""
                FROM ""Reporting-Emvarias_09-Rechazos"" r
                         LEFT JOIN LATERAL ( SELECT rpm_1.""REPEM04_Id"",
                                                    rpm_1.""REPEM04_Patente"",
                                                    rpm_1.""REPEM04_EstadoServicio"",
                                                    rpm_1.""REPEM04_GrupoTurno"",
                                                    rpm_1.""REPEM04_TipoServicio"",
                                                    rpm_1.""REPEM04_RutaCodigo"",
                                                    rpm_1.""REPEM04_EsRefuerzo"",
                                                    rpm_1.""REPEM04_Interno"",
                                                    rpm_1.""REPEM04_PesoTotal"",
                                                    rpm_1.""REPEM04_IdServicio"",
                                                    rpm_1.""REPEM04_FechaHora_EntradaRuta"",
                                                    rpm_1.""REPEM04_FechaHora_SalidaRuta"",
                                                    rpm_1.""REPEM04_Observaciones"",
                                                    rpm_1.""REPEM04_PesoTotal_Toneladas"",
                                                    rpm_1.""REPEM04_FechaHora_Pesaje"",
                                                    rpm_1.""REPEM04_Fecha_de_Servicio"",
                                                    rpm_1.""REPEM04_FechaHora_InicioServicio"",
                                                    rpm_1.""REPEM04_FechaHora_LlegadaBase"",
                                                    rpm_1.""REPEM04_FechaHora_SalidaBase"",
                                                    mic.""REPEM07_Numero_de_Microruta"",
                                                    mic.""REPEM07_NUAP"",
                                                    mic.""REPEM07_Numero_Ruta_Intendencia"",
                                                    mic.""REPEM07_Porcentaje_Barrido"",
                                                    mic.""REPEM07_Porcentaje_Limpieza_Urbana"",
                                                    mic.""REPEM07_Porcentaje_No_Aforado"",
                                                    mic.""REPEM07_Porcentaje_Residuos_Aprovechables"",
                                                    mic.""REPEM07_Ruta_Larga""
                                             FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm_1
                                                      JOIN ""Reporting-Emvarias_07-Microrutas"" mic ON rpm_1.""REPEM04_RutaCodigo""::text =
                                                                                                     mic.""REPEM07_Numero_de_Microruta""::text AND
                                                                                                     rpm_1.""REPEM04_FechaHora_Pesaje"" >=
                                                                                                     mic.""REPEM07_Fecha_Inicio_Vigencia"" AND
                                                                                                     (mic.""REPEM07_Fecha_Fin_Vigencia"" IS NULL OR
                                                                                                      rpm_1.""REPEM04_FechaHora_Pesaje"" <=
                                                                                                      mic.""REPEM07_Fecha_Fin_Vigencia"")
                                             WHERE r.""REPEM09_Placa""::text = rpm_1.""REPEM04_Patente""::text
                                               AND r.""REPEM09_Fecha_Corta"" = rpm_1.""REPEM04_FechaHora_SalidaBase""::date
                                             LIMIT 1) rpm ON true
                GROUP BY rpm.""REPEM04_Id"";

                create or replace view ""REPEM_Toneladas_Rechazos_Por_Ticket""
                            (""Id_Ticket"", ""NUAP"", ""Toneladas_Rechazadas"", ""Fecha_Rechazo"", ""Num_ECA"") as
                SELECT rpm.""REPEM04_Id""                      AS ""Id_Ticket"",
                       rpm.""REPEM07_NUAP""                    AS ""NUAP"",
                       r.""REPEM09_Toneladas""::numeric(18, 3) AS ""Toneladas_Rechazadas"",
                       r.""REPEM09_Fecha_Corta""               AS ""Fecha_Rechazo"",
                       r.""REPEM09_ECA""                       AS ""Num_ECA""
                FROM ""Reporting-Emvarias_09-Rechazos"" r
                         JOIN LATERAL ( SELECT irpm.""REPEM04_Id"",
                                               irpm.""REPEM04_Patente"",
                                               irpm.""REPEM04_EstadoServicio"",
                                               irpm.""REPEM04_GrupoTurno"",
                                               irpm.""REPEM04_TipoServicio"",
                                               irpm.""REPEM04_RutaCodigo"",
                                               irpm.""REPEM04_EsRefuerzo"",
                                               irpm.""REPEM04_Interno"",
                                               irpm.""REPEM04_PesoTotal"",
                                               irpm.""REPEM04_IdServicio"",
                                               irpm.""REPEM04_FechaHora_EntradaRuta"",
                                               irpm.""REPEM04_FechaHora_SalidaRuta"",
                                               irpm.""REPEM04_Observaciones"",
                                               irpm.""REPEM04_PesoTotal_Toneladas"",
                                               irpm.""REPEM04_FechaHora_Pesaje"",
                                               irpm.""REPEM04_Fecha_de_Servicio"",
                                               irpm.""REPEM04_FechaHora_InicioServicio"",
                                               irpm.""REPEM04_FechaHora_LlegadaBase"",
                                               irpm.""REPEM04_FechaHora_SalidaBase"",
                                               mic.""REPEM07_Numero_de_Microruta"",
                                               mic.""REPEM07_NUAP"",
                                               mic.""REPEM07_Numero_Ruta_Intendencia"",
                                               mic.""REPEM07_Ruta_Larga"",
                                               mic.""REPEM07_Porcentaje_Limpieza_Urbana"",
                                               mic.""REPEM07_Porcentaje_Barrido"",
                                               mic.""REPEM07_Porcentaje_No_Aforado"",
                                               mic.""REPEM07_Porcentaje_Residuos_Aprovechables""
                                        FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" irpm
                                                 JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                      ON mic.""REPEM07_Numero_de_Microruta""::text = irpm.""REPEM04_RutaCodigo""::text AND
                                                         irpm.""REPEM04_Fecha_de_Servicio"" >= mic.""REPEM07_Fecha_Inicio_Vigencia"" AND
                                                         (mic.""REPEM07_Fecha_Fin_Vigencia"" IS NULL OR
                                                          irpm.""REPEM04_Fecha_de_Servicio"" <= mic.""REPEM07_Fecha_Fin_Vigencia"")
                                        WHERE r.""REPEM09_Placa""::text = irpm.""REPEM04_Patente""::text
                                          AND r.""REPEM09_Fecha_Corta"" = irpm.""REPEM04_FechaHora_SalidaBase""::date
                                        LIMIT 1) rpm ON true;
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "OutboxMessages");
            
            migrationBuilder.Sql(@"
                create or replace view ""REPEM_Toneladas_Rechazos_Agrupadas_Por_Ticket""
                            (""Ticket_Asignable"", ""Toneladas_Rechazadas"", ""Cantidad_Rechazos"", ""Fecha_Rechazo"") as
                SELECT rpm.""REPEM04_Id""                           AS ""Ticket_Asignable"",
                       sum(r.""REPEM09_Toneladas"")::numeric(18, 3) AS ""Toneladas_Rechazadas"",
                       count(r.""REPEM09_Id"")                      AS ""Cantidad_Rechazos"",
                       max(r.""REPEM09_Fecha_Corta"")               AS ""Fecha_Rechazo""
                FROM ""Reporting-Emvarias_09-Rechazos"" r
                         LEFT JOIN LATERAL ( SELECT rpm_1.""REPEM04_Id"",
                                                    rpm_1.""REPEM04_Patente"",
                                                    rpm_1.""REPEM04_EstadoServicio"",
                                                    rpm_1.""REPEM04_GrupoTurno"",
                                                    rpm_1.""REPEM04_TipoServicio"",
                                                    rpm_1.""REPEM04_RutaCodigo"",
                                                    rpm_1.""REPEM04_EsRefuerzo"",
                                                    rpm_1.""REPEM04_Interno"",
                                                    rpm_1.""REPEM04_PesoTotal"",
                                                    rpm_1.""REPEM04_IdServicio"",
                                                    rpm_1.""REPEM04_FechaHora_EntradaRuta"",
                                                    rpm_1.""REPEM04_FechaHora_SalidaRuta"",
                                                    rpm_1.""REPEM04_Observaciones"",
                                                    rpm_1.""REPEM04_PesoTotal_Toneladas"",
                                                    rpm_1.""REPEM04_FechaHora_Pesaje"",
                                                    rpm_1.""REPEM04_Fecha_de_Servicio"",
                                                    rpm_1.""REPEM04_FechaHora_InicioServicio"",
                                                    rpm_1.""REPEM04_FechaHora_LlegadaBase"",
                                                    rpm_1.""REPEM04_FechaHora_SalidaBase"",
                                                    mic.""REPEM07_Numero_de_Microruta"",
                                                    mic.""REPEM07_NUAP"",
                                                    mic.""REPEM07_Numero_Ruta_Intendencia"",
                                                    mic.""REPEM07_Porcentaje_Barrido"",
                                                    mic.""REPEM07_Porcentaje_Limpieza_Urbana"",
                                                    mic.""REPEM07_Porcentaje_No_Aforado"",
                                                    mic.""REPEM07_Porcentaje_Residuos_Aprovechables"",
                                                    mic.""REPEM07_Ruta_Larga""
                                             FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" rpm_1
                                                      JOIN ""Reporting-Emvarias_07-Microrutas"" mic ON rpm_1.""REPEM04_RutaCodigo""::text =
                                                                                                     mic.""REPEM07_Numero_de_Microruta""::text AND
                                                                                                     rpm_1.""REPEM04_FechaHora_Pesaje"" >=
                                                                                                     mic.""REPEM07_Fecha_Inicio_Vigencia"" AND
                                                                                                     (mic.""REPEM07_Fecha_Fin_Vigencia"" IS NULL OR
                                                                                                      rpm_1.""REPEM04_FechaHora_Pesaje"" <=
                                                                                                      mic.""REPEM07_Fecha_Fin_Vigencia"")
                                             WHERE r.""REPEM09_Placa""::text = rpm_1.""REPEM04_Patente""::text
                                               AND r.""REPEM09_Fecha_Corta"" = rpm_1.""REPEM04_FechaHora_SalidaBase""::date
                                               AND mic.""REPEM07_Ruta_Larga""::text = r.""REPEM09_Ruta_Larga""::text
                                             LIMIT 1) rpm ON true
                GROUP BY rpm.""REPEM04_Id"";

                create or replace view ""REPEM_Toneladas_Rechazos_Por_Ticket""
                            (""Id_Ticket"", ""NUAP"", ""Toneladas_Rechazadas"", ""Fecha_Rechazo"", ""Num_ECA"") as
                SELECT rpm.""REPEM04_Id""                      AS ""Id_Ticket"",
                       rpm.""REPEM07_NUAP""                    AS ""NUAP"",
                       r.""REPEM09_Toneladas""::numeric(18, 3) AS ""Toneladas_Rechazadas"",
                       r.""REPEM09_Fecha_Corta""               AS ""Fecha_Rechazo"",
                       r.""REPEM09_ECA""                       AS ""Num_ECA""
                FROM ""Reporting-Emvarias_09-Rechazos"" r
                         JOIN LATERAL ( SELECT irpm.""REPEM04_Id"",
                                               irpm.""REPEM04_Patente"",
                                               irpm.""REPEM04_EstadoServicio"",
                                               irpm.""REPEM04_GrupoTurno"",
                                               irpm.""REPEM04_TipoServicio"",
                                               irpm.""REPEM04_RutaCodigo"",
                                               irpm.""REPEM04_EsRefuerzo"",
                                               irpm.""REPEM04_Interno"",
                                               irpm.""REPEM04_PesoTotal"",
                                               irpm.""REPEM04_IdServicio"",
                                               irpm.""REPEM04_FechaHora_EntradaRuta"",
                                               irpm.""REPEM04_FechaHora_SalidaRuta"",
                                               irpm.""REPEM04_Observaciones"",
                                               irpm.""REPEM04_PesoTotal_Toneladas"",
                                               irpm.""REPEM04_FechaHora_Pesaje"",
                                               irpm.""REPEM04_Fecha_de_Servicio"",
                                               irpm.""REPEM04_FechaHora_InicioServicio"",
                                               irpm.""REPEM04_FechaHora_LlegadaBase"",
                                               irpm.""REPEM04_FechaHora_SalidaBase"",
                                               mic.""REPEM07_Numero_de_Microruta"",
                                               mic.""REPEM07_NUAP"",
                                               mic.""REPEM07_Numero_Ruta_Intendencia"",
                                               mic.""REPEM07_Ruta_Larga"",
                                               mic.""REPEM07_Porcentaje_Limpieza_Urbana"",
                                               mic.""REPEM07_Porcentaje_Barrido"",
                                               mic.""REPEM07_Porcentaje_No_Aforado"",
                                               mic.""REPEM07_Porcentaje_Residuos_Aprovechables""
                                        FROM ""Reporting-Emvarias_04-Recoleccion_por_Microruta"" irpm
                                                 JOIN ""Reporting-Emvarias_07-Microrutas"" mic
                                                      ON mic.""REPEM07_Numero_de_Microruta""::text = irpm.""REPEM04_RutaCodigo""::text AND
                                                         irpm.""REPEM04_Fecha_de_Servicio"" >= mic.""REPEM07_Fecha_Inicio_Vigencia"" AND
                                                         (mic.""REPEM07_Fecha_Fin_Vigencia"" IS NULL OR
                                                          irpm.""REPEM04_Fecha_de_Servicio"" <= mic.""REPEM07_Fecha_Fin_Vigencia"")
                                        WHERE r.""REPEM09_Placa""::text = irpm.""REPEM04_Patente""::text
                                          AND r.""REPEM09_Fecha_Corta"" = irpm.""REPEM04_FechaHora_SalidaBase""::date
                                          AND mic.""REPEM07_Ruta_Larga""::text = r.""REPEM09_Ruta_Larga""::text
                                        LIMIT 1) rpm ON true;
            ");
        }
    }
}
