﻿using System.Linq.Expressions;
using Classification.Domain.Entities;
using Classification.Domain.Repositories;
using Microsoft.EntityFrameworkCore;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;

namespace Classification.Infrastructure.Data.EFCore.Repositories;

public class MaterialEntryRepository : Repository<MaterialEntry, int, ClassificationDbContext>, IMaterialEntryRepository
{
    public MaterialEntryRepository(IDbContextProvider<ClassificationDbContext> dbContextProvider, ICacheService cacheService) : base(dbContextProvider, cacheService) { }
    
    public Task<MaterialEntry?> GetSingleWithIncludesAsync(Expression<Func<MaterialEntry, bool>> predicate, CancellationToken cancellationToken)
    {
        return _context
            .MaterialEntries
            .Include(me => me.Details)
            .ThenInclude(med => med.Sacks)
            .SingleOrDefaultAsync(predicate, cancellationToken);
    }
    
    public Task<List<MaterialEntry>> GetAllWithIncludesAsync(Expression<Func<MaterialEntry, bool>> predicate, CancellationToken cancellationToken)
    {
        return _context
            .MaterialEntries
            .Include(me => me.Details)
            .ThenInclude(med => med.Sacks)
            .Where(predicate)
            .ToListAsync(cancellationToken);
    }
}