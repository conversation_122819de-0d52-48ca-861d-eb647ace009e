﻿using Orion.SharedKernel.Api.Services.Serilog;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Settings;
using Orion.SharedKernel.Infrastructure.Serilog.ColumnOptions;
using Reports.Domain.Constants;
using Serilog;
using Serilog.Events;

namespace Mercury.Api.Extensions;

public static class LoggerExtensions
{
    public static WebApplicationBuilder AddMercuryLogger(this WebApplicationBuilder builder, IConfiguration configuration)
    {
        var mercuryLoggerConfiguration = new LoggerConfiguration()
            .WriteTo.Conditional(condition =>
                    condition.Level == LogEventLevel.Information ||
                    condition.Properties.ContainsKey("RequestPath") ||
                    condition.Properties.ContainsKey("ActionName") ||
                    condition.Properties.ContainsKey("User"),
                wt => wt.PostgreSQL(
                    connectionString: configuration.GetConnectionString(Providers.PostgreSql),
                    tableName: ReportsTableNames.EmvariasEventsLog,
                    columnOptions: EventLogColumnOptions.ColumnOptions,
                    restrictedToMinimumLevel: LogEventLevel.Information,
                    needAutoCreateTable: false,
                    respectCase: true))
            .CreateLogger();

        builder.AddOrionLogger(configuration, mercuryLoggerConfiguration);

        return builder;
    }
}