﻿using FluentValidation;

namespace Common.Application.Features.Web.Commands.MaterialTypes.CreateMaterialType;

public class CreateMaterialTypeValidator : AbstractValidator<CreateMaterialTypeRequest>
{
    public CreateMaterialTypeValidator()
    {
        RuleFor(m => m.Name)
            .NotEmpty()
            .WithMessage("El nombre es requerido.")
            .MaximumLength(50)
            .WithMessage("El nombre no puede tener más de 50 carácteres.");
        
        RuleFor(m => m.Description)
            .NotEmpty()
            .WithMessage("La descripción es requerida.")
            .MaximumLength(50)
            .WithMessage("La descripción no puede tener más de 50 carácteres.");
        
        RuleFor(m => m.GroupId)
            .NotEmpty()
            .WithMessage("El id del grupo de tipo de material es requerido.");
        
    }
}