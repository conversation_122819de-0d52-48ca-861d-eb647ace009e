﻿using Orion.SharedKernel.Domain.Entities;
using Reports.Domain.Constants;

namespace Reports.Domain.Entities;

public class WeighingScale : Entity<long>
{
    private const long DefaultDepositPlace = 720105237;
    private const int MinimumTownCodeChars = 5;
    
    public string LicensePlate { get; set; }
    public long NIT { get; set; }
    public int ArrivingWeight { get; set; }
    public int LeavingWeight { get; set; }
    public DateTime EntryDate { get; set; }
    public DateTime EgressDate { get; set; }
    public string MaterialType { get; set; }
    public long DepositPlace { get; set; } = DefaultDepositPlace;
    public OriginType OriginType { get; set; }
    public long NUAP { get; set; }
    public DateTime LoadingDate { get; set; }
    public DateTime? CancelDate { get; set; }
    public LoadingType LoadingType { get; set; }
    public string TownCode { get; set; }
    public Town Town { get; set; }

    public WeighingScale() { }

    public WeighingScale(long id, string licensePlate, long nit, int arrivingWeight, int leavingWeight, DateTime entryDate,
        DateTime egressDate, long depositPlace, OriginType originType, long nuap, DateTime loadingDate,
        LoadingType loadingType, string townCode, Town town)
    {
        Id = id;
        LicensePlate = licensePlate;
        NIT = nit;
        ArrivingWeight = arrivingWeight;
        LeavingWeight = leavingWeight;
        EntryDate = entryDate;
        EgressDate = egressDate;
        DepositPlace = depositPlace;
        OriginType = originType;
        NUAP = nuap;
        LoadingDate = loadingDate;
        LoadingType = loadingType;
        TownCode = townCode;
        Town = town;
    }
    
    public WeighingScale SetTownCode(string code)
    {
        TownCode = code.PadLeft(MinimumTownCodeChars, '0');
        return this;
    }
}