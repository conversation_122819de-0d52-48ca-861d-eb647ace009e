﻿using System.Text.Json.Serialization;
using MediatR;

namespace Common.Application.Features.Web.Commands.MaterialTypes.CreateMaterialType;

public class CreateMaterialTypeRequest : IRequest<CreateMaterialTypeResponse>
{
    /// <summary>
    /// Nombre del tipo de material.
    /// </summary>
    /// <remarks> Máximo 50 carácteres. </remarks>
    /// <example>Cartón corrugado</example>
    [JsonPropertyName("name")]
    public string? Name { get; set; }
    
    /// <summary>
    /// Breve descripción del tipo de material.
    /// </summary>
    /// <remarks> Máximo 50 carácteres. </remarks>
    /// <example>Cartón simple de textura corrugada</example>
    [JsonPropertyName("description")]
    public string? Description { get; set; }
    
    /// <summary>
    /// Id de Grupo del tipo de material.
    /// </summary>
    /// <example>23</example>
    [JsonPropertyName("groupId")]
    public string? GroupId { get; set; }
}