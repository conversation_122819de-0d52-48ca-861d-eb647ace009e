﻿using Classification.Api;
using Common.Api;
using Mercury.Api.Extensions;
using Reports.Api;
using Shared.Api;
using Shared.Infrastructure.Extensions;

namespace Mercury.Api;

public static class ServicesExtensions
{
    public static IServiceCollection AddMercury(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddOrionCore(configuration);

        services.AddHttpContextAccessor();

        services.AddSharedModule(configuration);

        services.AddReportsModule(configuration);

        services.AddCommonModule(configuration);

        services.AddClassificationModule(configuration);

        services.AddMercuryCors(configuration);

        services.AddJWT(configuration);

        return services;
    }

}