﻿namespace Classification.Domain.Constants;

public static class MaterialEntryColumns
{
    public const string Id = "RECYECA04_Id";
    public const string Date = "RECYECA04_Fecha";
    public const string Responsible = "RECYECA04_Responsable";
    public const string LicensePlate = "RECYECA04_Placa";
    public const string Route = "RECYECA04_Ruta";
    public const string Origin = "RECYECA04_Origen";

	public const string IsDeleted = "RECYECA04_Eliminado";

	public const string CreationDate = "RECYECA04_FechaDeCreacion";
	public const string CreatedBy = "RECYECA04_CreadoPor";
	public const string LastModificationDate = "RECYECA04_FechaDeModificacion";
	public const string LastModifiedBy = "RECYECA04_ModificadoPor";
	public const string DeletionDate = "RECYECA04_FechaDeEliminacion";
	public const string DeletedBy = "RECYECA04_EliminadoPor";

	public const string PrimaryKeyConstraintName = ClassificationTableNames.MaterialEntry + "_key";
}

public static class MaterialEntryDetailColumns
{
    public const string Id = "RECYECA05_Id";
    public const string Client = "RECYECA05_Cliente";
    public const string MaterialEntryId = "RECYECA05_Ingreso_de_Material";

	public const string IsDeleted = "RECYECA05_Eliminado";

	public const string MaterialClassificationId = "RECYECA05_Id_de_Clasificacion_de_Material";

	public const string PrimaryKeyConstraintName = ClassificationTableNames.MaterialEntryDetail + "_key";
    public const string MaterialEntryConstraintName = "Recycling-ECA_RECYECA05-RECYECA04_fkey";
    public const string SackConstraintName = "Recycling-ECA_RECYECA05-RECYECA06_fkey";
	public const string MaterialClassificationConstraintName = "Recycling-ECA_RECYECA05-RECYECA07_fkey";
}

public static class SackColumns
{
    public const string Id = "RECYECA06_Id";
    public const string Quantity = "RECYECA06_Cantidad";
    
    public const string PresentationId = "RECYECA06_Presentacion";
    public const string MaterialEntryDetailId = "RECYECA06_Detalle_Ingreso_de_Material";

	public const string IsDeleted = "RECYECA06_Eliminado";

	public const string PrimaryKeyConstraintName = ClassificationTableNames.Sack + "_key";
    public const string MaterialEntryDetailConstraintName = "Recycling-ECA_RECYECA06-ECA_RECYECA05_fkey";
    public const string PresentationConstraintName = "Recycling-ECA_RECYECA06-OrionDomainValue_fkey";
}

public static class MaterialClassificationColumns
{
	public const string Id = "RECYECA07_Id";
	public const string CompleteState = "RECYECA07_Paso_de_la_Clasificacion";

	public const string IsDeleted = "RECYECA07_Eliminado";

	public const string CreationDate = "RECYECA07_FechaDeCreacion";
	public const string CreatedBy = "RECYECA07_CreadoPor";
	public const string LastModificationDate = "RECYECA07_FechaDeModificacion";
	public const string LastModifiedBy = "RECYECA07_ModificadoPor";
	public const string DeletionDate = "RECYECA07_FechaDeEliminacion";
	public const string DeletedBy = "RECYECA07_EliminadoPor";

	public const string PrimaryKeyConstraintName = ClassificationTableNames.MaterialClassification + "_key";
	public const string MaterialClassificationDetailConstraintName = "Recycling-ECA_RECYECA07-RECYECA08_fkey";
}

public static class MaterialClassificationDetailColumns
{
	public const string Id = "RECYECA08_Id";
	public const string Value = "RECYECA08_Valor";
	public const string IsDeleted = "RECYECA08_Eliminado";
	
	public const string UnitId = "RECYECA08_Id_de_Unidad";
	public const string MaterialTypeId = "RECYECA08_Id_de_Tipo_de_Material";
	public const string MaterialClassificationId = "RECYECA08_Id_de_Clasificacion_de_Material";

	public const string PrimaryKeyConstraintName = ClassificationTableNames.MaterialClassificationDetail + "_key";

	public const string UnitConstraintName = "Recycling-ECA_RECYECA08-OrionDomainValue_fkey";
	public const string MaterialTypeConstraintName = "Recycling-ECA_RECYECA08-ECA_RECYECA01_fkey";
	public const string MaterialClassificationConstraintName = "Recycling-ECA_RECYECA08-ECA_RECYECA07_fkey";

}