﻿using Classification.Domain.Entities;
using Classification.Domain.Repositories;
using Orion.SharedKernel.Application.Common.Services;
using Orion.SharedKernel.Infrastructure.Data.EFCore.ContextProvider;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Repositories.Entities;

namespace Classification.Infrastructure.Data.EFCore.Repositories;

public class MaterialClassificationRepository : Repository<MaterialClassification, int, ClassificationDbContext>, IMaterialClassificationRepository
{
    public MaterialClassificationRepository(IDbContextProvider<ClassificationDbContext> dbContextProvider, ICacheService cacheService) : base(dbContextProvider, cacheService) { }
}