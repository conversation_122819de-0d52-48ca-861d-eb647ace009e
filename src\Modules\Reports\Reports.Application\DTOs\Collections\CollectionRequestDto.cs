using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Collections;

public class CollectionRequestDto
{
    /// <summary>
    /// Código del lugar de depósito.
    /// </summary>
    /// <example>124105088</example>
    [JsonPropertyName("Nuap")]
    public string NUAP { get; set; }
    
    /// <summary>
    /// Placa del vehículo.
    /// </summary>
    /// <remarks>Mínimo 5 carácteres alfanuméricos</remarks>
    /// <example>GKV295</example>
    [JsonPropertyName("Placa")]
    public string LicensePlate { get; set; }
    
    /// <summary>
    /// Fecha de carga del registro en la balanza.
    /// </summary>
    /// <remarks>Formato: YYYY-MM-DD HH:MM:SS</remarks>
    /// <example>2023-07-14 11:32:00</example>
    [JsonPropertyName("FechaYHoraRecoleccion")]
    public string CollectionDate { get; set; }
    
    /// <summary>
    /// Tipo de origen.
    /// </summary>
    /// <remarks>(1) NUAP, (2) NUET, (3) NUECA, (4) Otros</remarks>
    /// <example>NUAP</example>
    /// <example>1</example>
    [JsonPropertyName("TipoOrigen")]
    public string OriginType { get; set; }
    
    /// <summary>
    /// Código de micro ruta.
    /// </summary>
    /// <example>ZZZ999</example>
    [JsonPropertyName("NroMicroruta")]
    public string Microroute { get; set; }

    /// <summary>
    /// Cantidad en toneladas de residuos sólidos urbanos.
    /// </summary>
    /// <example>10</example>
    [JsonPropertyName("TonLimUrb")]
    public double UrbanCleaningTons { get; set; }
    
    /// <summary>
    /// Cantidad en toneladas de barrido.
    /// </summary>
    /// <example>20</example>
    [JsonPropertyName("TonBarrido")]
    public double SweepingTons { get; set; }
    
    /// <summary>
    /// Cantidad en toneladas de residuos rechazados.
    /// </summary>
    /// <example>30</example>
    [JsonPropertyName("TonRechazos")]
    public double RejectedTons { get; set; }
    
    /// <summary>
    /// Cantidad de residuos aprovechables.
    /// </summary>
    /// <example>40</example>
    [JsonPropertyName("TonResAprob")]
    public double RecyclableTons { get; set; }
    
    /// <summary>
    /// Cantidad de toneladas.
    /// </summary>
    /// <example>40</example>
    [JsonPropertyName("Toneladas")]
    public double Tons { get; set; }
    
    /// <summary>
    /// Coste de peajes.
    /// </summary>
    /// <example>50</example>
    [JsonPropertyName("ValorPeaje")]
    public double Toll { get; set; }
    
    /// <summary>
    /// Número de identificación tributaria.
    /// </summary>
    /// <example>*********</example>
    [JsonPropertyName("NitViaje")]
    public string NIT { get; set; }
    
    /// <summary>
    /// Nombre de la empresa.
    /// </summary>
    /// <example>La Estrella S.A.</example>
    [JsonPropertyName("Empresa")]
    public string? CompanyName { get; set; }
    
    /// <summary>
    /// Código del municipio de donde provienen los residuos.
    /// </summary>
    /// <example>05001</example>
    [JsonPropertyName("CodDANE_Empresa")]
    public string? DaneCode { get; set; }
}
