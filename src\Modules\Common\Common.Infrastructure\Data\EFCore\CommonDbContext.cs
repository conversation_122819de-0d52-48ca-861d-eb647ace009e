﻿using Common.Domain.Entities;
using Common.Infrastructure.Data.EFCore.Configurations.PostgreSQL;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Orion.SharedKernel.Infrastructure.Data.EFCore;
using Orion.SharedKernel.Infrastructure.Data.EFCore.Audit;

namespace Common.Infrastructure.Data.EFCore;

public sealed class CommonDbContext : OrionDbContext
{
    public DbSet<MaterialType> MaterialTypes { get; set; }

    public CommonDbContext()
    {
        MaterialTypes = Set<MaterialType>();
    }
    
    public CommonDbContext(
        DbContextOptions<CommonDbContext> options,
        IConfiguration configuration,
        IMediator mediator,
        AuditableEntitySaveChangesInterceptor auditableEntitySaveChangesInterceptor)
        : base(options, configuration, mediator, auditableEntitySaveChangesInterceptor)
    {
        MaterialTypes = Set<MaterialType>();
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        ModelConfiguration(modelBuilder);
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        optionsBuilder.UseNpgsql();

        base.OnConfiguring(optionsBuilder);
    }

    private static void ModelConfiguration(ModelBuilder modelBuilder)
    {
		AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);

        modelBuilder.ApplyConfiguration(new MaterialTypeConfiguration());
    }
}