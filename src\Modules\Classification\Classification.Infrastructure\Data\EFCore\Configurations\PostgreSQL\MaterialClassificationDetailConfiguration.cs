﻿using Classification.Domain.Constants;
using Classification.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Classification.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class MaterialClassificationDetailConfiguration : IEntityTypeConfiguration<MaterialClassificationDetail>
{
	public void Configure(EntityTypeBuilder<MaterialClassificationDetail> entity)
	{
		entity.ToTable(ClassificationTableNames.MaterialClassificationDetail);

		entity.HasKey(c => c.Id)
			.HasName(MaterialClassificationDetailColumns.PrimaryKeyConstraintName);

		entity.Property(c => c.Id)
			.HasColumnName(MaterialClassificationDetailColumns.Id)
			.ValueGeneratedOnAdd();

		entity.Property(d => d.Value)
			.HasColumnName(MaterialClassificationDetailColumns.Value);

		entity.Property(d => d.IsDeleted)
			.HasColumnName(MaterialClassificationDetailColumns.IsDeleted)
			.HasDefaultValue(false);

		entity.Property(d => d.UnitId)
			.HasColumnName(MaterialClassificationDetailColumns.UnitId);

		entity.Ignore(d => d.Unit);

		entity.Property(d => d.MaterialTypeId)
			.HasColumnName(MaterialClassificationDetailColumns.MaterialTypeId);

		entity.Property(d => d.MaterialClassificationId)
			.HasColumnName(MaterialClassificationDetailColumns.MaterialClassificationId);

		entity.HasOne(d => d.MaterialType)
		.WithOne()
		.HasForeignKey<MaterialClassificationDetail>(d => d.MaterialTypeId)
		.HasConstraintName(MaterialClassificationDetailColumns.MaterialTypeConstraintName);

		entity.HasOne(d => d.MaterialClassification)
			.WithMany(c => c.Details)
			.HasForeignKey(d => d.MaterialClassificationId)
			.HasConstraintName(MaterialClassificationDetailColumns.MaterialClassificationConstraintName);
	}
}