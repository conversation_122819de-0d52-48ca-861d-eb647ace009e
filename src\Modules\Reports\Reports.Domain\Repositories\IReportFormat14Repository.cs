using System.Linq.Expressions;
using Orion.SharedKernel.Domain.Entities;
using Orion.SharedKernel.Domain.Repositories.Entities;
using Reports.Domain.Entities;

namespace Reports.Domain.Repositories;

public interface IReportFormat14Repository : IReadRepository<ReportFormat14, string>
{
    Task<PaginatedResult<ReportFormat14>> GetFilteredReportAsync(Expression<Func<ReportFormat14, bool>> predicate,
        CancellationToken cancellationToken,
        bool isPaginated = true,
        int pageNumber = 1,
        int pageSize = 25);
}