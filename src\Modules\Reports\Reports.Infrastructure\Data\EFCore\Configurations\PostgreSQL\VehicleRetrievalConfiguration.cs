﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Reports.Domain.Constants;
using Reports.Domain.Entities;

namespace Reports.Infrastructure.Data.EFCore.Configurations.PostgreSQL;

public class VehicleRetrievalConfiguration : IEntityTypeConfiguration<VehicleRetrieval>
{
    private const string DateTimeWithoutTimeZone = "timestamp without time zone";
        
    public void Configure(EntityTypeBuilder<VehicleRetrieval> builder)
    {
        builder.ToTable(ReportsTableNames.VehicleRetrieval);

        builder.Ignore(p => p.Id);

        builder.Property(p => p.NUAP)
            .HasColumnName(VehicleRetrievalColumns.NUAP);

        builder.Property(p => p.Year)
            .HasColumnName(VehicleRetrievalColumns.Year);
        
        builder.Property(p => p.Month)
            .HasColumnName(VehicleRetrievalColumns.Month);

        builder.Property(p => p.Tons)
            .HasColumnName(VehicleRetrievalColumns.Tons);
        
        builder
            .HasKey(p => new
                {
                    p.NUAP,
                    p.Year,
                    p.Month
                })
            .HasName(VehicleRetrievalColumns.PrimaryKeyConstraintName);
    }
}