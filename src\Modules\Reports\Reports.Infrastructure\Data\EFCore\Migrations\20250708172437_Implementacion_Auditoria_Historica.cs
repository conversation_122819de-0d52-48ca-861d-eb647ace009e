﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Reports.Infrastructure.Data.EFCore.Migrations
{
    public partial class Implementacion_Auditoria_Historica : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Reporting-Emvarias_11-Auditoria_Historica",
                columns: table => new
                {
                    REPEM11_Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    REPEM11_Id_Entidad = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    REPEM11_Nombre_Tabla = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    REPEM11_Tipo_Accion = table.Column<int>(type: "integer", nullable: false),
                    REPEM11_Usuario = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    REPEM11_Fecha_Accion = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    REPEM11_Datos_Previos = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("Reporting-Emvarias_11-Auditoria_Historica_key", x => x.REPEM11_Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_HistoricalAudit_ActionDate",
                table: "Reporting-Emvarias_11-Auditoria_Historica",
                column: "REPEM11_Fecha_Accion");

            migrationBuilder.CreateIndex(
                name: "IX_HistoricalAudit_EntityId",
                table: "Reporting-Emvarias_11-Auditoria_Historica",
                column: "REPEM11_Id_Entidad");

            migrationBuilder.CreateIndex(
                name: "IX_HistoricalAudit_TableName",
                table: "Reporting-Emvarias_11-Auditoria_Historica",
                column: "REPEM11_Nombre_Tabla");

            migrationBuilder.CreateIndex(
                name: "IX_HistoricalAudit_User",
                table: "Reporting-Emvarias_11-Auditoria_Historica",
                column: "REPEM11_Usuario");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Reporting-Emvarias_11-Auditoria_Historica");
        }
    }
}
